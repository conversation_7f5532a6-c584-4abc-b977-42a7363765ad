<?php include(APPPATH . 'views/feesv2/reports/components/report_header.php'); ?>

<div class="card-body pt-1">
      
      <div class="col-md-12">
        <div class="row">

          <div class="col-md-3 form-group" id="searchBYstudentId" style="display: none;">
            <p style="margin-bottom: 0.5px;">Search By Student Name</p>
            <input id="student_name" autocomplete="off" placeholder="Search by Student Name" class="form-control input-md" name="student_name">
          </div>

          <div class="col-md-3 form-group">
            <p style="margin-bottom: 0.5px;">Select Blueprints</p>
            <select class="form-control select" id="blueprint_type" name="blueprint_type">
              <option value="all">All</option>
              <?php foreach ($fee_blueprints as $key => $val) { ?>
                <option  value="<?= $val->id ?>"><?php echo $val->name?></option>
              <?php } ?>
            </select>
          </div>
        
         <div class="col-md-3 form-group">
          <p style="margin-bottom: 0.5px;">Select Class/Sections</p>
            <?php 
              $array = array();
              // $array[0] = 'All Section';
              foreach ($classSectionList as $key => $cl) {
                  $array[$cl->id] = $cl->class_name . $cl->section_name;
              }
              echo form_dropdown("classSectionId", $array, '', "id='classSectionId' multiple title='Select Class/Section' class='form-control select'");
            ?>
          </div>

          <div class="col-md-3 form-group">
            <p style="margin-bottom: 0.5px;">Select Status</p>
            <select class="form-control select" name="fees_status" id="fees_status">
              <option value="">All</option>
              <option value="NOT_STARTED">Not-Paid</option>
              <option value="PARTIAL">Partial</option>
              <option value="FULL">Fully-Paid</option>
            </select>
          </div>

          <div class="col-md-3 form-group">
            <p style="margin-bottom: 0.5px;">RTE/NON-RTE</p>
            <select class="form-control select" name="rte_status" id="rte_status">
              <option value="">All</option>
              <option value="1">RTE</option>
              <option value="2">NON-RTE</option>
            </select>
          </div>

          <div class="col-md-3 form-group">
            <p style="margin-bottom: 0.5px;">Admission Type</p>
            <select class="form-control select" name="admission_type" id="admission_type">
              <option value="">All</option>
              <option value="2">New Admission</option>
              <option value="1">Re-Admission</option>
            </select>
          </div>

          <div class="col-md-3 form-group" id="installmentType" style="display: none;">
             <p style="margin-bottom: 0.5px;">Select Installments Type</p>
              <select class="form-control" name="installment_type" id="installment_type">
              
            </select>
          </div>

          <div class="col-md-3 form-group" id="installment" style="display: none">
             <p style="margin-bottom: 0.5px;">Select Installments</p>
            <select class="form-control" name="installment" id="installmentId">
              
            </select>
          </div>
        </div>
        <div class="form-group" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
            <div>
                <label class="checkbox-inline"><input style="width:16px;height: 16px;" type="checkbox" name="search_by_student" id="search_by_student"><span style="font-size:16px; margin-left: 10px;">Show Search by Student</span></label>
                <!--   <label class="checkbox-inline"><input style="width:20px;height: 20px;" type="checkbox" name="due_cross" id="due_cross"><span style="font-size:16px; margin-left: 10px;font-family: inherit;">Display Over-due Status</span></label> -->
            </div>
            <div>
                <input type="button" value="Get Report" id="getReport" class="getreport_btn">
            </div>
        </div>
    
      </div>
      <!-- <ul class="panel-controls mb-3" id="exportIcon" style="display: none;">
        <button id="stu_print" class="btn btn-danger" onclick="print_fees_status()"><span class="glyphicon glyphicon-print" aria-hidden="true"></span> Print</button>
        <a style="margin-left:3px;" onclick="exportToExcel_fee_status()" class="btn btn-primary pull-right"><span class="fa fa-file-text-o"></span> Export</a>

      </ul>
       -->
      <ul class="panel-controls mb-3">
         <div class="mr-3">
          <button id="activate-btn" onclick="activateStudents()" class="btn btn-success" style="border-radius: 8px;display: none;">Activate</button>
          <button id="deactivate-btn" onclick="deactivateStudents()" class="btn btn-danger" style="border-radius: 8px;display: none; color: #dc3545 !important; border: 2px solid #dc3545 !important; background-color: transparent !important; font-size: 14px !important;">De-activate Partially</button>
        </div>
       
      </ul>

      <div class="col-md-12 pt-2" style="overflow: hidden;">

        <div class="text-center"><div style="display: none;" class="progress" id="progress"><div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%"></div></div></div>
        <!-- <div id="sliderDiv" class="mb-3" style="display: none;">
          <input id="slider" type="range" min="1" max='100' value="0" step="1">
        </div> -->
      
        <div id="fees_student_status" class="fee_status pt-3">
          
        </div>
        

      </div>
    </div>


  </div> 
</div>

<script type="text/javascript">
  function print_fees_status(){
    var restorepage = document.body.innerHTML;
    $('.print_hide').css('display', 'none');
    var printcontent = document.getElementById('fees_student_status').innerHTML;
    document.body.innerHTML = printcontent;
    window.print();
    document.body.innerHTML = restorepage;
  }

  function exportToExcel_fee_status(){
    var htmls = "";
    var uri = 'data:application/vnd.ms-excel;base64,';
    var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
    var base64 = function(s) {
        return window.btoa(unescape(encodeURIComponent(s)))
    };

    var format = function(s, c) {
        return s.replace(/{(\w+)}/g, function(m, p) {
            return c[p];
        })
    };

    var mainTable = $(".fee_status").html();

    htmls =mainTable;

    var ctx = {
      worksheet : 'Spreadsheet',
      table : htmls
    }

    var link = document.createElement("a");
    link.download = "export.xls";
    link.href = uri + base64(format(template, ctx));
    link.click();

  }
</script>
<style type="text/css">
  input[type= 'range'] {
    margin: 0 auto;
    width: 100px;
  }

  /* #sliderDiv {
    text-align: center;
    width: 350px;
    float: right;
  } */

      /* Force scrollbar width for this specific page */
      #fees_student_status::-webkit-scrollbar,
    #fees_student_status .dataTables_wrapper::-webkit-scrollbar,
    #fees_student_status .dataTables_scrollBody::-webkit-scrollbar {
        width: 12px !important;
        height: 12px !important;
    }

    #fees_student_status::-webkit-scrollbar-thumb,
    #fees_student_status .dataTables_wrapper::-webkit-scrollbar-thumb,
    #fees_student_status .dataTables_scrollBody::-webkit-scrollbar-thumb {
        background: #A09EAE !important;
        border-radius: 6px !important;
    }

    #fees_student_status::-webkit-scrollbar-track,
    #fees_student_status .dataTables_wrapper::-webkit-scrollbar-track,
    #fees_student_status .dataTables_scrollBody::-webkit-scrollbar-track {
        background: #f1f1f1 !important;
        border-radius: 6px !important;
    }

    #fees_student_status,
    #fees_student_status .dataTables_wrapper,
    #fees_student_status .dataTables_scrollBody {
        scrollbar-width: auto !important;
        scrollbar-color: #A09EAE #f1f1f1 !important;
    }

</style>

<script>
  // $(function() {
  //   $("#slider").on('change input', function() {
  //     console.log($('.table').width());
  //     var tableWidth = $(".table").width();
  //     var currentWidth = $('#fees_student_status').width();
  //     var width = tableWidth;
  //     if (tableWidth > currentWidth) {
  //       width = $(".table").width() - $('#fees_student_status').width();
  //     }
  //     var posLeft = $(this).val() * width / 100;
  //     console.log(posLeft);
  //     $(".table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td").css('left', -posLeft);
  //   });
  // });

  $('#search_by_student').on('change',function(){
    searchByStudent = 0;
    $('#searchBYstudentId').hide();
    if ($('#search_by_student').is(':checked')) {
      $('#searchBYstudentId').show();
    }
  });


$('#blueprint_type').on('change',function(){
  var bpType =  $('#blueprint_type').val();
  $('#installment_type').val('');
 
  $('#installmentType').hide();
  if (bpType != 'all') {
    changeInstallment_type(bpType);
    $('#installmentType').show();
  }else{
    $('#installmentId').val('');
    $('#installmentType').hide();
  }
});

  function changeInstallment_type(bpType){
    $.ajax({
      url:'<?php echo site_url('reports/student/student_report/get_bpTypewise_insType') ?>',
      type:'post',
      data : {'bpType':bpType},
      success : function(data){
        var data = JSON.parse(data);
        var output = '<option value="">Select Installments Type</option>';
        for(var i=0; i < data.length; i++){
          output += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
        }
        $('#installment_type').html(output);
      }
    });
  }
  function changeInstallment() {
    var installment_type = $('#installment_type').val();
    $.ajax({
      url:'<?php echo site_url('feesv2/reports/installment_type_installment') ?>',
      type:'post',
      data : {'installment_type':installment_type},
      success : function(data){
        var data = JSON.parse(data);
        var output = '<option value="">Select Installments</option>';
        for(var i=0; i < data.length; i++){
          output += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
        }
        $('#installmentId').html(output);
        $('#installment').show();
      }
    });
  }
  $('#installment_type').on('change',function(){
    changeInstallment();
  }); 
  $(document).ready(function(){
    $('#getReport').on('click',function(){
      getReport();
    });
  });

  function getReport() {
    $('#getReport').prop('disabled', true).val('Please wait...');
    $("#progress").show();
    var progress = document.getElementById('progress-ind');
    progress.style.width = '10%';
    $('#exportIcon').hide();
    // $("#sliderDiv").hide();
    // $('#slider').val('0');
    $('.print_visible').hide();
    $('#fees_student_status').html('<div class="text-center pt-5"><p>Loading...</p></div>');
      var classSectionId =  $('#classSectionId').val();
      var installment_type = $('#installment_type').val();
      var installmentId = $('#installmentId').val();
      var fee_type = $('#blueprint_type').val();
      var fees_status = $('#fees_status').val();
      var rte_status = $('#rte_status').val();
      var admission_type = $('#admission_type').val();
      var student_name = '';
      if ($('#search_by_student').is(':checked')) {
        var student_name = $('#student_name').val();
      }
      var progress = document.getElementById('progress-ind');
      progress.style.width = '50%';

      $.ajax({
        url: '<?php echo site_url('reports/student/student_report/get_fees_student_list'); ?>',
        data: {'installment_type':installment_type,'installmentId':installmentId,'fee_type':fee_type,'classSectionId':classSectionId,'student_name':student_name,'fees_status':fees_status,'rte_status':rte_status,'admission_type':admission_type},
        type: "post",
        success: function (data) {
          var data = JSON.parse(data);
          if(data == 0){
            $('#fees_student_status').html('<h3 class="no-data-display">No Student match the filter criteria</h3>');
            $("#progress").hide();
            $('#getReport').prop('disabled', false).val('Get Report');
            return false;
          }
          var students = data.students;
          var headers = data.headers;
          var header = data.header;
          if (students.length <= 0) {
            $('#fees_student_status').html('<h3 class="no data-display">No Student match the filter criteria</h3>');
            $("#progress").hide();
            $('#getReport').prop('disabled', false).val('Get Report');
            return false;
          }
          $('#studentcount').html(students.length);
          var html = '<table id="std-table" class="table table-bordered" style="width:100%">';
          html += header;
          html += '<tbody>';
          var j=1;
          var status_arr = {
            'Not Assigned' : '-',
            'NOT_STARTED' : '<span class="text-warning">Not Paid</span>',
            'FULL' : '<span class="text-success">Paid</span>',
            'PARTIAL' : '<span class="text-warning">Partial</span>',
            'Is Due' : '<span class="text-danger">Over-due</span>'
          };
          for(var i in students) {
            var fee_url = '<?php echo site_url('feesv2/fees_collection/history/') ?>'+students[i].stdId+'/'+'0';
            var bg_color = 'style="background-color:#d2ffc5"';
            var className = 'activated';
            var cur_status = 'Active';
            if(students[i].temp_deactivation == 1) {
              bg_color = 'style="background-color:#ffccc5"';
              className = 'deactivated';
              cur_status = 'In-active';
            }
            html += '<tr>';
            html += '<td class="print_hide" '+bg_color+'><input class="'+className+' checkStudentAll" onclick="showBtn('+students[i].stdId+')" data-type="'+className+'" data-stdid="'+students[i].stdId+'" type="checkbox" id="std_'+students[i].stdId+'" value="'+students[i].stdId+'"></td>';
            html += '<td>'+(j++)+'</td>';
            html += '<td>'+cur_status+'</td>';
            html += '<td>'+students[i].student_name+'</td>';
            html += '<td>'+students[i].className+'</td>';
            html += '<td>'+students[i].sectionName+'</td>';
            html += '<td>'+students[i].admission_no+'</td>';
            html += '<td>'+students[i].enrollment_number+'</td>';
            html += '<td>'+students[i].rte_status+'</td>';
            html += '<td>'+students[i].admission_type+'</td>';
            for(var h in headers) {
              var status = (students[i][headers[h].id] == undefined)?'Not Assigned':students[i][headers[h].id];
              html += '<td>'+status_arr[status]+'</td>';
            }

            // html += '<td><a class="btn btn-warning" target="_blank" href="'+fee_url+'">View </a> </td>';
            html += `
            <td class="print_hide">
                <a class="btn pull-right" 
                  style="margin-left: 8px"  
                  data-placement="top" 
                  data-toggle="modal" 
                  data-original-title="View" 
                  href="javascript:void(0)" 
                  onclick="fee_receipt_view(${students[i].stdId})" 
                  data-target="#receipt_view_model">
                  <?= $this->load->view('svg_icons/visibility.svg', [], true); ?>
                </a>
            </td>`;

            html += '</tr>';
          }
          html += '</tbody>';
          html += '</table>';
          $('#fees_student_status').html(html);

          // Store original header HTML before DataTable processes it
          window.originalTableHeader = $('#std-table thead').html();

           $('#std-table').DataTable( {
               "language": {
               "search": "",
               "searchPlaceholder": "Enter Search...",
               "paginate": {
                 "first": "«",
                 "last": "»",
                 "next": "Next ›",
                 "previous": "‹ Previous"
               },
               "info": "Showing _START_ to _END_ of _TOTAL_ entries",
               "infoEmpty": "Showing 0 to 0 of 0 entries",
               "infoFiltered": "(filtered from _MAX_ total entries)",
               "lengthMenu": "Show _MENU_ "
             },
             "lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
             "pageLength": 10,
             dom: '<"d-flex justify-content-between align-items-center"<"d-flex align-items-center"l><"d-flex align-items-center"fB>><"datatable-heading-container">rt<"d-flex justify-content-between align-items-center mt-3"<"dataTables_info"i><"dataTables_paginate"p>>',
             ordering:false,
             "bPaginate":true,
                   "scrollX": true,
                   "scrollY": 500,
                   scrollCollapse: true,
                   initComplete: function() {
                     // Add heading between controls and table
                     var headingHtml = '<div class="datatable-heading" style="background-color: #cec3f8; padding: 15px; text-align: center;"><h3 style="margin: 0; color: #333; font-weight: 600;">Fees collection status reports</h3></div>';
                     $('.datatable-heading-container').html(headingHtml);
                   },
                  buttons: [{
                    extend: 'print',
                   text: `<button class="btn btn-outline-primary" id="expbtns" style="margin-right:-7px;"><?= $this->load->view('svg_icons/print.svg', [], true); ?> Print</button>`,
                    title: '',
                    footer: true,
                    exportOptions: {
                      columns: ':visible',
                    },
                    customize: function(win) {
                      $(win.document.body).find('table tbody tr').each(function() {
                        $(this).find('td:first-child').remove();
                        $(this).find('td:last-child').remove();
                      });

                      if (window.originalTableHeader) {
                        var $tempDiv = $('<div>').html('<table><thead>' + window.originalTableHeader + '</thead></table>');
                        $tempDiv.find('thead tr').each(function(rowIndex) {
                          var $row = $(this);
                          var $allTh = $row.find('th');

                          if ($allTh.length > 0) {
                            var totalCols = 0;
                            $allTh.each(function() {
                              var colspan = parseInt($(this).attr('colspan')) || 1;
                              totalCols += colspan;
                            });

                            var $firstTh = $allTh.first();
                            var firstColspan = parseInt($firstTh.attr('colspan')) || 1;
                            if (totalCols > 1 && firstColspan < totalCols) {
                              if ($firstTh.attr('colspan')) {
                                var colspan = parseInt($firstTh.attr('colspan'));
                                if (colspan > 1) {
                                  $firstTh.attr('colspan', colspan - 1);
                                } else {
                                  $firstTh.remove();
                                }
                              } else {
                                $firstTh.remove();
                              }
                            }
                            $allTh = $row.find('th');
                            if ($allTh.length > 0) {
                              var $lastTh = $allTh.last();
                              var lastColspan = parseInt($lastTh.attr('colspan')) || 1;
                              var newTotalCols = 0;
                              $allTh.each(function() {
                                var colspan = parseInt($(this).attr('colspan')) || 1;
                                newTotalCols += colspan;
                              });
                              if (newTotalCols > 1 && lastColspan < newTotalCols) {
                                if ($lastTh.attr('colspan')) {
                                  var colspan = parseInt($lastTh.attr('colspan'));
                                  if (colspan > 1) {
                                    $lastTh.attr('colspan', colspan - 1);
                                  } else {
                                    $lastTh.remove();
                                  }
                                } else {
                                  $lastTh.remove();
                                }
                              }
                            }
                          }
                        });

                        $(win.document.body).find('table thead').html($tempDiv.find('thead').html());
                      }
                      $(win.document.body).prepend(`
                        <h3 style="margin-bottom: 0.25rem; font-size: 1.5rem; font-family: 'Poppins', serif; text-align: center;">
                            <?php echo htmlspecialchars($this->settings->getSetting('school_name')); ?>
                        </h3>
                       
                      `);

                      $(win.document.body)
                        .css('font-family', "'Poppins', sans-serif")
                        .css('font-size', '10pt')
                        .css('padding', '10px');

                      $(win.document.head).append(`
                          <style>
                            @page {
                              size: auto;
                              margin: 12mm;
                            }
                          </style>
                        `);
                    }
                  },
                  {
                    extend: 'excelHtml5',
                    text: `<button class="btn btn-outline-primary" id="expbtns"><?= $this->load->view('svg_icons/excel_2.svg', [], true); ?> Excel</button>`,
                    filename: 'Fees Collection Status Report'
                  }
           ]
         });
         
         // Style the search input to match the button design
         styleSearchInput();
           
           $('#exportIcon').show();
          $('.print_visible').show();
          var tabwidth = $(".table").width();
          var curwidth = $("#fees_student_status").width();
          if(tabwidth > curwidth) {
            // $("#sliderDiv").show();
          }

          var progress = document.getElementById('progress-ind');
          progress.style.width = '100%';
          setTimeout(function() {
            $("#progress").hide();
          }, 500);
          $('#getReport').prop('disabled', false).val('Get Report');
          $('#std-table tr').click(function(event) {
            if (event.target.type !== 'checkbox') {
                $(':checkbox', this).trigger('click');
                if($(':checkbox', this).is(':checked')) {
                  $(this).css('background-color', '#e7e7e7');
                } else {
                  $(this).css('background-color', '');
                }
            }
          });

          // $('#selectAll').on('click',function(){
          //   if ($('#selectAll').is(':checked')){
          //     $(".checkStudentAll").prop('checked', true);
              
          //     $("#deactivate-btn").show();
          //   }else{
          //     $(".checkStudentAll").prop('checked', false);
          //     $("#deactivate-btn").hide();
          //   }
          // });

          // console.log(data);
        },
        error: function (err) {
          $("#progress").hide();
          $('#getReport').prop('disabled', false).val('Get Report');
          console.log(err);
        }
      });
  }

  function showBtn(std_id) {
    $(".deactivated").prop('disabled', false);
    $(".activated").prop('disabled', false);
    $("#deactivate-btn").hide();
    $("#activate-btn").hide();
    var deactivated = [];
    $(".deactivated").each(function () {
      if($(this).is(':checked'))
        deactivated.push($(this).val());
    });

    if(deactivated.length) {
      $("#activate-btn").show();
      $(".activated").prop('disabled', true);
      $(".deactivated").prop('disabled', false);
      return false;
    }

    var activated = [];
    $(".activated").each(function () {
      if($(this).is(':checked'))
        activated.push($(this).val());
    });

    if(activated.length) {
      $("#deactivate-btn").show();
      $(".deactivated").prop('disabled', true);
      $(".activated").prop('disabled', false);
    }


    /*var type = $("#std_"+std_id).data('type');
    if(type == 'activated') {
      $("#deactivate-btn").show();
      $(".deactivated").prop('disabled', true);
      $(".activated").prop('disabled', false);
    } else {
      $("#activate-btn").show();
      $(".activated").prop('disabled', true);
      $(".deactivated").prop('disabled', false);
    }*/
  }

function deactivateStudents() {
  var status = 1;
  var student_ids = [];
  $(".activated").each(function( index ) {
    if($(this).is(':checked'))
      student_ids.push($(this).val());
  });
  if(student_ids.length) {
    changeActivationStatus(status, student_ids);
  } else {
    return false;
  }
}

function activateStudents() {
  var status = 0;
  var student_ids = [];
  $(".deactivated").each(function () {
    if($(this).is(':checked'))
      student_ids.push($(this).val());
  });

  if(student_ids.length) {
    changeActivationStatus(status, student_ids);
  } else {
    return false;
  }
}

function changeActivationStatus(status, student_ids) {
  $.ajax({
    url:'<?php echo site_url('reports/student/student_report/student_temp_activation') ?>',
    type:'post',
    data : {'status':status, 'student_ids':student_ids},
    success : function(data){
      getReport();
    }
  });
}

function fee_receipt_view(stdId) {
 $.ajax({
    url: '<?php echo site_url('feesv2/reports_v2/get_fee_history_data'); ?>',
    type: 'post',
    data: {'stdId':stdId},
    success: function(data) {
      var resData = JSON.parse(data);
      var fees = resData.fees;
      var student = resData.student;
      $("#receipt_view").html(prepare_receipt_table(fees, student));
    }
  });
}

function prepare_receipt_table(resData, std) {
  var res = '';
  res +='<div class="form-group mb-5"><div class="col-md-5"><label>Student Name : </label>'+std.student_name+'</div><div class="col-md-3"><label>Class : </label>'+std.class_name+'</div><div class="col-md-4"><label>Admission No : </label>'+std.admission_no+'</div></div>'
  res += '<table class="table table-bordered" id="mytable">';
  res += '<thead>';
  res += '<tr>';
  res += '<th>Blueprint Name</th>';
  res += '<th>Total Fees</th>';
  res += '<th>Total Fees paid</th>';
  res += '<th>Concession</th>';
  res += '<th>Balance</th>';
  res += '</tr>'
  res += '</thead>';
  res += '<tbody>';
  var total_fee = 0;
  var total_fee_paid = 0;
  var total_concession = 0;
  var total_balance = 0;
  for(var key in resData){   
    total_fee += parseFloat(resData[key].fee_amount);
    total_fee_paid += parseFloat(resData[key].paid_amount);
    total_concession += parseFloat(resData[key].concession) ;
    total_balance += parseFloat(resData[key].balance);
    res += '<tr>';
    res += '<td>'+resData[key].blueprint_name+'</td>';
    res += '<td>'+in_currency(resData[key].fee_amount)+'</td>';
    res += '<td>'+in_currency(resData[key].paid_amount)+'</td>';
    res += '<td>'+in_currency(resData[key].concession)+'</td>';
    res += '<td>'+in_currency(resData[key].balance)+'</td>';
    res += '</tr>';
  }
  res += '<tr>';
  res += '<th style="text-align: right">Total</th>';
  res += '<th>'+in_currency(total_fee)+'</th>';
  res += '<th>'+in_currency(total_fee_paid)+'</th>';
  res += '<th>'+in_currency(total_concession)+'</th>';
  res += '<th>'+in_currency(total_balance)+'</th>';
  res += '</tr>';
  res += '</tbody>';
  res += '</table>';
  return res;
}

 function in_currency(amount) {
   var formatter = new Intl.NumberFormat('en-IN', {
     // style: 'currency',
     currency: 'INR',
   });
   return formatter.format(amount);
 }

// Function to style search input
function styleSearchInput() {
    // Method 1: Target the new DataTables DOM structure
    let $input = $('.dt-search input[type="search"]');
    if ($input.length) {      
        // Add placeholder
        $input.attr('placeholder', 'Search');
        
        // Style the input
        if ($input.parent().hasClass('search-box')) {
            $input.unwrap();
        }
        $input.siblings('.bi-search').remove();
        $input.addClass('input-search');
        if (!$input.parent().hasClass('search-box')) {
            $input.wrap('<div class="search-box position-relative" style="display: inline-block; margin-right: 1px; margin-bottom: 5px;"></div>');
            $input.parent().prepend('<i class="bi bi-search"></i>');
        }
        return true;
    }
    return false;
}
 </script>
<style type="text/css">
 /* #fees_student_status{
   overflow-x: auto;

 } */
  /* #fees_student_status tr, #fees_student_status td, #fees_student_status th {
    position: relative;
    max-width: 100%;
    white-space: nowrap; */
    /*padding: 10px;*/
  /* } */

/* Fix dropdown z-index layering issue */
.form-group {
    position: relative !important;
    z-index: 1 !important;
}

/* Dropdown form groups - higher z-index */
.form-group:has(.form-control.select) {
    z-index: 10 !important;
}

/* Specific dropdown z-index ordering */
.form-group:has(#blueprint_type) {
    z-index: 15 !important;
}

.form-group:has(#classSectionId) {
    z-index: 14 !important;
}

.form-group:has(#fees_status) {
    z-index: 13 !important;
}

.form-group:has(#rte_status) {
    z-index: 12 !important;
}

.form-group:has(#admission_type) {
    z-index: 11 !important;
}

.form-group:has(#installment_type) {
    z-index: 10 !important;
}

.form-group:has(#installmentId) {
    z-index: 9 !important;
}

/* Checkbox form group - lower z-index */
.form-group:has(input[type="checkbox"]) {
    z-index: 1 !important;
}

/* Ensure select dropdowns have proper z-index */
.form-control.select {
    position: relative !important;
    z-index: inherit !important;
}

/* Fix for Bootstrap select dropdowns */
.form-control.select:focus {
    z-index: 999 !important;
}

/* Ensure dropdown options appear above other elements */
.form-control.select option {
    z-index: 999 !important;
}

/* Additional fix for multiple select dropdowns */
#classSectionId {
    z-index: inherit !important;
}

#classSectionId:focus {
    z-index: 999 !important;
}

/* Style Print, Excel and Search fields to match index_v2.php */
.dataTables_wrapper .dt-buttons {
    float: right;
    margin-left: 10px;
  }
  .dataTables_filter input {
    background-color: #f2f2f2;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-right: 5px;
  }
  .dataTables_wrapper .dataTables_filter {
    float: right;
    text-align: left;
    width: unset;
    margin-right: 10px;
  }
  
  /* Ensure proper spacing between search and buttons */
  .dataTables_wrapper .d-flex {
    gap: 10px;
  }
  
  /* Style the right side container */
  .dataTables_wrapper .d-flex:last-child {
    justify-content: flex-end;
  }

  /* Modal backdrop styling - Grey translucent overlay covering entire screen */
  .modal-backdrop {
    background-color: rgba(128, 128, 128, 0.6) !important;
    backdrop-filter: blur(3px) !important;
    -webkit-backdrop-filter: blur(3px) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 999998 !important;
  }
  
  /* Ensure modal backdrop covers everything */
  body.modal-open {
    overflow: hidden !important;
  }
  
  /* Ensure popup stays sharp and above blur */
  #receipt_view_model.modal {
    z-index: 999999 !important;
  }
  
  #receipt_view_model .modal-dialog {
    z-index: 999999 !important;
  }
  
  #receipt_view_model .panel {
    z-index: 999999 !important;
  }
  
  /* Ensure backdrop appears immediately */
  #receipt_view_model.modal-backdrop {
    opacity: 0.6 !important;
  }
  
  /* Force backdrop to cover entire viewport */
  #receipt_view_model.modal-backdrop.show {
    opacity: 0.6 !important;
  }
</style>

<div id="receipt_view_model" class="modal fade" role="dialog">
  <div class="modal-dialog" style="margin: auto;width: 75%; max-width: 900px;">
    <div class="panel panel-default new-panel-style_3" style="background-color: #F9F7FE; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
      <div class="panel-heading new-panel-heading" style="background-color: #F9F7FE !important; border-radius: 12px 12px 0 0; padding: 20px;">
        <h3 class="panel-title" style="color: #333; font-weight: 600; margin: 0; font-size: 1.4rem;">Student Fees Details</h3>
      </div>
      <div class="panel-body" style="padding: 25px; background-color: #F9F7FE;">
        <div id="receipt_view" style="max-height: 500px; overflow-y: auto; background-color: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.05);"></div> 
      </div>
      <div class="panel-footer" style="border-radius: 0 0 12px 12px; padding: 20px; border-top: 1px solid #e0d6f7; background-color: #F9F7FE; text-align: right;">
        <button type="button" class="getreport_btn" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
