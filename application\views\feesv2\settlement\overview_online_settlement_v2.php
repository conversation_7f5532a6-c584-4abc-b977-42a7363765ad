<?php include(APPPATH . 'views/feesv2/reports/components/report_header.php'); ?>



<div class="card-body" style="margin-left: 1.3rem; padding-bottom: 10px;">
      <div class="row">
        <div class="col-md-3 form-group" style="padding-left: 5px; padding-right: 5px;">
          <p style="margin-bottom: 6px; margin-left:5px;">Date range <font color="red">*</font></p>
          <div id="reportrange" class="dtrange" style="width: 100%">                                            
            <span></span>
            <input type="hidden" id="from_date">
            <input type="hidden" id="to_date">
          </div>
        </div>

        <div class="col-md-2 form-group" style="padding-left: 5px; padding-right: 5px;">
          <p style="margin-bottom: 6px; margin-left:5px;">&nbsp;</p>
          <input type="button" value="Get Report" id="getReport" class="getreport_btn" style="margin-top: 0px;" onclick="get_overview_settelment()">
        </div>
      </div>

      <div class="row">
        <div class="col-12 text-center">
          <div style="display: none;" class="progress" id="progress">
            <div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="card-body" style="padding-top: 5px;">
      <div class="row" style="margin: 0px;display: block;">
        <div id="settle" class='panel-title'></div>
        <div id="datatable_controls" style="margin-bottom: 8px;"></div>
        <div id="settlement_table"></div>
        <div id="datatable_pagination" style="margin-top: 15px;"></div>
      </div>

    </div>
  </div>

</div>

<!--- Verification Settlement Dialog box --->
<div id="_verify_settlement_modal" class="modal fade" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-x1" style="width:40%;margin:auto;">
    <div class="modal-content" style="border-radius: 8px;overflow-x:auto;max-height: 70vh;">
      <div>
      <div class="modal-header" style="border-bottom: 2px solid #ccc;">
        <h4 class="modal-title">Verify Settlement</h4>
        <button style="font-size: 32px;font-weight: bold;color: #e04b4a;opacity: 1;padding-top: .5rem;" type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
      </div>

      <div class="modal-body">
        <div class="col-md-12">
          <label class="control-label">Settlement Id</label><h6 id='verification_settlement_id_title'>XXXX</h6>

          <input type="hidden" id="verification_settlement_id" name="verification_settlement_id" value=''>
          <label for="verification_comment" class="control-label">Enter Comments (if any)</label>
          <textarea rows=3 placeholder="Enter Comment" id="verification_comment" name="verification_comment" class="form-control"></textarea>
          <br>
        </div>
      </div>

      <div class="modal-footer">
        <div class="col-md-12">
          <button class="btn btn-primary" id="verify_settlement_button" type="button" onclick="submit_verification()">Verify Settlement</button>
        </div>
      </div>

    </div>
  </div>
</div>

<!--- Settlement Details Dialog box --->
<div id="_show_settlement_details" class="modal fade" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" style="width:90%; max-width: 1400px; margin:auto;">
    <div class="modal-content" style="border-radius: 0px; box-shadow: 0 8px 32px rgba(0,0,0,0.25);">

      <div class="modal-header" style="background-color: #efecfd; border-bottom: 1px solid #dee2e6; border-radius: 0px; padding: 20px 25px;">
        <h4 class="modal-title" id="modalHeader" style="color: #000000; font-weight: 600; font-size: 20px;">Settlement Details of <span id='settlement_id_title'>XXXX</span></h4>
        <button type="button" class="close" data-dismiss="modal" style="color: #000000; opacity: 0.8; font-size: 24px;">&times;</button>
      </div>
      <div class="action-buttons" style="position: sticky; top: 0; background: white; z-index: 1000; padding: 10px 0; border-bottom: 1px solid #eee;">
  
        <!-- Print Button -->
        <a class="control-primary pull-right mr-4 mb-2 mt-2" href="javascript:void(0)" id="printBtn" onclick="printSettelemntDetails()">
          <button class="btn btn-outline-primary" id="expbtns" style="margin-right:-7px;">
            <?= $this->load->view('svg_icons/print.svg', [], true); ?> Print
          </button>
        </a>

        <!-- Excel Button -->
        <a class="control-primary pull-right m-2" href="javascript:void(0)" id="printExcelBtn" onclick="excelSettelemntDetails()">
          <button class="btn btn-outline-primary" id="expbtns">
            <?= $this->load->view('svg_icons/excel_2.svg', [], true); ?> Excel
          </button>
        </a>

      </div>


      <div class="modal-body" style="padding: 20px; background-color: #ffffff; overflow-x:auto; max-height: 80vh; min-height: 60vh; border-radius: 0px;">
       
        
        <div class="content-scrollable" style="max-height: calc(90vh - 80px); overflow-y: auto;">
          <div id='settlement_match_div'>
          </div>

          <div id='settlement_summary_div'>       
          </div>
          <br>
          
          <div class="card">
            <div class="card-header sticky-header" style="position: sticky; top: 0; background: white; z-index: 999; border-bottom: 2px solid #dee2e6;">
              <h5>Details of Receipts Raised</h5>
            </div>
            <div class="card-body" style="overflow-y:auto;height:55vh;">
              <div id='tx_split_summary_div'>
              </div>
              <div id='tx_table_div'>            
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>

<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<style type="text/css">
  .new_circleShape_res {
    padding: 8px;
    border-radius: 50% !important;
    color: white !important;
    font-size: 22px;
    height: 3.2rem !important;
    width: 3.2rem !important;
    text-align: center;
    vertical-align: middle;
    float: left;
    border: none !important;
    box-shadow: 0px 3px 7px #ccc;
    line-height: 1.7rem !important;
}

/* Ensure daterangepicker appears above sticky headers */
.daterangepicker,
.daterangepicker *,
.daterangepicker .dropdown-menu,
.daterangepicker .dropdown-menu * {
  z-index: 9999 !important;
}

/* Dropdown menu styles */
.action-dropdown {
    position: relative;
    display: inline-block;
}

.action-dropdown-btn {
    background: none;
    border: 1px solid #e5e7eb;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.action-dropdown-btn:hover {
    background-color: #f5f5f5;
    border-color: #623CE7;
}

.action-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    z-index: 1000;
    min-width: 150px;
    display: none;
}

.action-dropdown-menu.show {
    display: block;
}

.action-dropdown-item {
    display: block;
    width: 100%;
    padding: 10px 16px;
    text-align: left;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 14px;
    color: #374151;
    transition: background-color 0.2s ease;
}

.action-dropdown-item:hover {
    background-color: #f9fafb;
    color: #623CE7;
}

.action-dropdown-item:first-child {
    border-radius: 8px 8px 0 0;
}

.action-dropdown-item:last-child {
    border-radius: 0 0 8px 8px;
}

.action-dropdown-item:only-child {
    border-radius: 8px;
}

/* No Data Container Styles */
.no-data-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f8f8fc;
    border-radius: 12px;
    min-height: 400px;
}

.no-data-icon {
    margin-bottom: 30px;
}

.no-data-icon svg {
    width: 180px;
    height: 180px;
}

.no-data-content {
    text-align: center;
    max-width: 400px;
}

.no-data-title {
    font-size: 24px;
    font-weight: 600;
    color: #333333;
    margin: 0 0 16px 0;
    line-height: 1.3;
}

.no-data-description {
    font-size: 16px;
    line-height: 1.5;
    font-weight: 400;
    color: #666666;
}

.no-data-description:last-child {
    margin-bottom: 0;
}
</style>

<script>
  function submit_verification() {
    settlement_id = $('#verification_settlement_id').val();
    settlement_comment = $('#verification_comment').val();

    $.ajax({
      url:'<?php echo site_url('payment_controller/submit_settlement_verification') ?>',
      type:'post',
      data : {'settlement_id': settlement_id, 'settlement_comment': settlement_comment},
      success : function(data) {
        new PNotify({
          title: 'Success',
          text: 'Verification Noted',
          type: 'success',
        });
      },
      error: function(err) {
        console.log(err);
      },
      complete: function () {
        $("#_verify_settlement_modal").modal('hide');
        get_overview_settelment();
      }
    });
  }

</script>

<script type="text/javascript">
  $("#reportrange").daterangepicker({
    ranges: {
      'Today': [moment(), moment()],
      'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
      'Last 7 Days': [moment().subtract(6, 'days'), moment()],
      'Last 30 Days': [moment().subtract(29, 'days'), moment()],
      'This Month': [moment().startOf('month'), moment()],
      'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'DD-MM-YYYY',
    separator: ' to ',
    startDate: moment().subtract(6, 'days'),
    endDate: moment()            
  },function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });

  $("#reportrange span").html(moment().subtract(6, 'days').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
  $('#from_date').val(moment().subtract(6, 'days').format('DD-MM-YYYY'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));
</script>


<script type="text/javascript">

   $(document).ready(function() {
    $('#_verify_settlement_modal').on('show.bs.modal', function (event) {
      //Get the settlement_id
      var settlement_id = $(event.relatedTarget).data('settlement_id');

      //Initialize all the fields for creating a new subject
      $(this)
        .find("input[type=hidden],input[type=text],textarea")
        .val('')
        .end();

      $(this).find('.modal-body #verification_settlement_id').val(settlement_id);
      $(this).find('.modal-body #verification_settlement_id_title').html(settlement_id);
    });
  });

  function show_settlement_details(settlement_id, settlement_datetime, account_name, bank_reference, payout_amount) {
    var loading_icon_html = `<div class="col-12 text-center loading-icon" style="display: show;"><i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i></div>`;
    $('#settlement_id_title').html(settlement_id);
    $('#settlement_summary_div').html(loading_icon_html);
    $('#tx_table_div').html(loading_icon_html);
    $('#settlement_match_div').html('');
    $('#tx_split_summary_div').html('');

    $.ajax({
        url:'<?php echo site_url('payment_controller/get_settlement_details') ?>',
        type:'post',
        data : {'settlement_id': settlement_id},
        success : function(data){
          var data = JSON.parse(data);
          var transactions = data.tx_details_se;
          var vendor_data = data.vendor_data;
          $('#settlement_match_div').html(_build_settlement_match_summary(payout_amount, transactions));
          $('#settlement_summary_div').html(_build_settlement_summary(settlement_id, settlement_datetime, account_name, bank_reference, payout_amount));
          $('#tx_split_summary_div').html(_build_split_summary_table(vendor_data));
          $('#tx_table_div').html(_build_tx_table(transactions));
        },
        error: function(err) {
          console.log(err);
        }
      });
  }

  function _build_settlement_match_summary(payout_amount, transactions_data) {
    var sum_tx_amount = 0;
    transactions_data.forEach(tx => {
      //Add only if the receipt number is generated
      if (tx.receipt_number) {
        sum_tx_amount += parseInt(tx.amount_paid || 0);
      }
    });

    var difference = parseInt(payout_amount) - sum_tx_amount;
    var difference_html = '';
    if (difference == 0) {
      difference_html = `<font color="green">SETTLEMENTS MATCH!</font>`;
    } else if (difference < 0) {
      difference_html = `<font color="red">LESS AMOUNT SETTLED</font>`;
    } else {
      difference_html = `<font color="red">MORE AMOUNT SETTLED</font>`;
    }

    var output_html = `
      <table class="table table-bordered" style="font-size:16px;background:#dcdce6;">
        <thead>
          <tr>
            <th colspan=4>SETTLEMENT MATCH SUMMARY
            </th>
          </tr>
        </thead>
        <tr>
          <td>Settlement Amount</td><td><strong>${numberToCurrency(payout_amount)}</strong></td>
          <td>Receipt Amount</td><td><strong>${numberToCurrency(sum_tx_amount)}</strong></td>
        </tr>
        <tfoot>
          <tr>
            <td colspan=2><strong>Difference: ${numberToCurrency(difference)}</strong></td>
            <td colspan=2><strong>${difference_html}</strong></td>
          </tr>
        </tfoot>
      </table>
    `;

    return output_html;
  }

  function _build_split_summary_table(vendor_data) {
    if (vendor_data.length == 0) return '';
    var html_output = `
      <table class="table table-bordered" width="75%">
        <thead>
          <th colspan=3>Expected settlement based on Receipts</th>
        </thead>
        <thead>
          <th>Code</th>
          <th>Name</th>
          <th>Amount</th>
        </thead>
      `;

    vendor_data.forEach(vendor => {
      html_output += `
        <tr>
          <td>${vendor.vendor_code}</td>
          <td>${vendor.vendor_name}</td>
          <td>${numberToCurrency(vendor.vendor_amount)}</td>
        </tr>
      `;
    });

    html_output += `</table>`;

    return html_output;
  }

  function _build_settlement_summary(settlement_id, settlement_datetime, account_name, bank_reference, payout_amount) {
    var html = `
      <table class="table table-bordered" style="width:100%">
        <thead>
          <th colspan=6>Settlement Details</th>
        </thead>
        <tr>
          <td width="10%">Settlement Id</td>
          <td width="20%"><strong>${settlement_id}</strong></td>
          <td width="10%">Settlement Date</td>
          <td width="20%"><strong>${settlement_datetime}</strong></td>
          <td width="10%">Account Name</td>
          <td width="30%"><strong>${account_name}</strong></td>
        </tr>
        <tr>
          <td>Bank Reference</td>
          <td><strong>${(bank_reference)?bank_reference:'Not Processed'}</strong></td>
          <td colspan=2 style="background-color:#dcdce6">Settlement Amount</td>
          <td colspan=2 style="background-color:#dcdce6"><strong>${numberToCurrency(payout_amount)}</strong></td>
        </tr>
      </table>
    `;

    return html;
  }

  function _build_tx_table(transactions_data) {
    if (transactions_data.length == 0) {
      var html = `
        <div class="no-data-container">
            <div class="no-data-icon">
                <?= $this->load->view('svg_icons/no_data.svg', [], true); ?>
            </div>
            <div class="no-data-content">
                <h2 class="no-data-title">No Transactions</h2>
                <p class="no-data-description">There are no transactions available for this settlement.</p>
            </div>
        </div>
      `;
      return html;
    }

    var html = `
      <table class="table table-bordered">
        <thead>
          <th width="5%">#</th>
          <th width="10%">Payment Type</th>
          <th width="10%">Receipt Number</th>
          <th width="25%">Student Name</th>
          <th width="25%">Admission No.</th>
          <th width="25%">Enrollment No.</th>
          <th width="20%">Paid Date</th>
          <th width="15%">Amount</th>
          <th width="15%">Split JSON</th>
          <th width="25%">Order ID</th>
          <th width="25%">Tx ID</th>
          <th width="25%">Actions</th>
        </thead>`;

    var sl_no = 1;
    var sum_tx_amount = 0;
    transactions_data.forEach(tx => {
      var generate_receipt_btn_html = '';
       sum_tx_amount += parseFloat(tx.amount_paid || 0);
      //Add only if the receipt number is generated
      if (tx.receipt_number) {
        var url = '<?php echo base_url() ?>feesv2/fees_collection/receipt_pdf_download/'+tx.trans_id;
        generate_receipt_btn_html = `
          Receipt Generated <br><br>
          <a class="circleButton_noBackColor_actionBtn" 
            style="margin-left: 8px;" 
            data-placement="top" 
            data-toggle="tooltip" 
            data-original-title="Download PDF" 
            href="${url}">
            <?= $this->load->view('svg_icons/download.svg', [], true); ?>
          </a>`;
        var receipt_url = '<?php echo base_url() ?>feesv2/fees_collection/fee_reciept_viewv1/'+tx.trans_id;

      } else {
        generate_receipt_btn_html = `<button class="getreport_btn" onclick="generate_receipt('${tx.order_id}')">Generate Receipt</button>`;
      }

      html += `
        <tr>
          <td>${sl_no++}</td>
          <td>${tx.fees_type}</td>
          <td><a target="_blank" data-toggle="print receipt" data-original-title="Print Receipt" href="${receipt_url}">${tx.receipt_number}</a> </td>
          <td>${tx.student_name} (${tx.section_name})</td>
          <td>${tx.admission_no ? tx.admission_no : '—'}</td>
          <td>${tx.enrollment_number ? tx.enrollment_number : '—'}</td>
          <td>${tx.paid_datetime}</td>
          <td>${numberToCurrency(tx.amount_paid)}</td>
          <td>${_build_split_json_small_table(tx.split_objs)}</td>
          <td>${tx.order_id}</td>
          <td>${tx.tx_id}</td>
          <td>${generate_receipt_btn_html}</td>
        </tr>`;
    });
    html += `
      <tr>
      <td colspan=7 style="background-color:#dcdce6;font-weight:700;font-size=14;">Total Receipt Collection</td>
      <td colspan=4 style="background-color:#dcdce6;font-weight:700;font-size=14;">${numberToCurrency(sum_tx_amount)}</td>
      </tr>
    `;

    html += `</table>`;

    return html;
  }

  function _build_split_json_small_table(split_objs) {
    if (!split_objs) return 'NA';

    var html = `
      <table class="table table-bordered">`;

    var sl_no = 1;
    var sum_tx_amount = 0;
    split_objs.forEach(obj => {
      html += `
        <tr>
          <td><small>${obj.vendor_name}</small></td>
          <td><small>${numberToCurrency(obj.vendor_amount)}</small></td>
        </tr>`;
    });

    html += `</table>`;

    return html;
  }

  function generate_receipt(order_id) {
    alert(order_id);
  }

  function numberToCurrency(amount) {
    var formatter = new Intl.NumberFormat('en-IN', {
      currency: 'INR',
    });
    return formatter.format(amount);
  }


 function get_overview_settelment(){
  $('#search').prop('disabled',true).val('Please wait...'); 
  $('#progress').show();
  $('#progress-ind').css('width', '50%').attr('aria-valuenow', 50); 
    $("#btns_display").hide();
    $('#settlement_table').html('');

    var fromdate = $("#from_date").val();
    var toDate = $("#to_date").val();

    // var input_array = input.split(",");
    // var date = input_array[0];
    // var verify = input_array[1];
    // var conformed_by = input_array[2];
    // var conformed_on = input_array[3];

    $('#trans').html('<h4><strong>Transactions on From ' + fromdate + ' to '+toDate+'</strong></h4>');
    $("#confirmBtn").hide();
    $("#confirmed").hide();
    $.ajax({
        url:'<?php echo site_url('payment_controller/get_overview_transactions') ?>',
        type:'post',
        data : {'fromdate':fromdate,'toDate':toDate},
        success : function(data){
            var data = JSON.parse(data);
            if(data.code == '1028') {
              $('#progress').hide();
              $('#search').prop('disabled',false).val('Get Report'); 
              $('#settle').html('<h4><strong>Settlement not started</strong></h4>');
              return false;
            }
            $("#btns_display").show();
            $('#progress-ind').css('width', '100%').attr('aria-valuenow', 100); 
            setTimeout(function() { $('#progress').hide(); }, 400);
            $('#search').prop('disabled',false).val('Get Report'); 
            var settlements = data.settlement;
            var settlement_amount = data.settlement_amount;
            var trans_amount = data.trans_amount;
            var not_settled_count = data.not_settled_count;
            var settled_count = data.settled_count;
            var isConfirmed = data.isConfirmed;

            var html  = '';
            var html1 = '<table class="table table-bordered dataTable" id="settlement_data_table">';
            if(settlements.length == 0 || data.code == '1028') {
                $('#settle').html('<h4><strong>Settlement not started</strong></h4>');
            } else {
              html1 +='<thead>';
              html1 +='<tr>';
              html1 +='<th>#</th>';
              html1 +='<th>Settlement ID</th>';
              html1 +='<th>Settlement Date</th>';
              html1 +='<th>Account Name</th>';
              html1 +='<th>Bank Reference</th>';
              html1 +='<th>Settlement Amount</th>';
              html1 +='<th>Transaction Amount</th>';
              html1 +='<th>Receipt Not Generated Amount</th>';
              html1 +='<th>Settlement Status</th>';
              html1 +='<th>Verification Status</th>';
              html1 +='<th>Actions</th>';
              html1 +='</tr>';
              html1 +='</thead>';
              html1 +='<tbody>';
              var totalTransAmount = 0;
              var totalNotGeneartedTransAmount = 0;
              for (var j = 0; j < settlements.length; j++) {
                totalTransAmount += parseFloat(settlements[j].amount_paid);
                totalNotGeneartedTransAmount += parseFloat(settlements[j].total_tax_not_generated_amount);
                var vericiation_status_style = '';
                var verification_button = '';
                var verification_details = '';
                if (settlements[j].verification_status == 'Not Verified') {
                  vericiation_status_style = "style='background-color:#fabbbb;font-weight: 700;font-size: 14px;'";
                  verification_button = `<button style="margin-top: 5px;" class="getreport_btn" data-settlement_id='${settlements[j].settlement_id}' data-target='#_verify_settlement_modal' data-toggle='modal'>Mark as Verified</button>`;
                } else {
                  vericiation_status_style = "style='background-color:#91cb91;font-weight: 700;font-size: 14px;'";
                  verification_details = `<br><small>${settlements[j].verified_by} on ${settlements[j].verified_on}</small>`;
                }
                var verification_comment = (settlements[j].remarks) ? `<br><small>(${settlements[j].remarks})</small>` : '';

                html1 += '<tr>';
                html1 += '<td>'+(j+1)+'</td>';
                html1 += '<td>'+settlements[j].settlement_id+'</td>';
                html1 += '<td>'+settlements[j].settlement_datetime+'</td>';
                html1 += '<td>'+settlements[j].account_name+'</td>';
                html1 += '<td>'+((settlements[j].bank_reference)?settlements[j].bank_reference:'Not Processed')+'</td>';
                html1 += '<td>'+settlements[j].payout_amount+'</td>';
                html1 += '<td>'+settlements[j].amount_paid+'</td>';
                html1 += '<td style="color:red">'+settlements[j].total_tax_not_generated_amount+'</td>';
                html1 += '<td>'+settlements[j].status+'</td>';
                html1 += `<td ${vericiation_status_style}>${settlements[j].verification_status}${verification_details}${verification_comment}</td>`;
                var bankReference = settlements[j].bank_reference.trim().replace(/\n/g, '').replace(/'/g, "\\'");
                // Create dropdown menu for actions
                var dropdownId = 'dropdown_' + settlements[j].settlement_id;
                var actionsHtml = '<td style="text-align: center;">';
                actionsHtml += '<div class="action-dropdown">';
                actionsHtml += '<button class="action-dropdown-btn" onclick="toggleDropdown(\'' + dropdownId + '\')">';
                actionsHtml += `<?= $this->load->view("svg_icons/more_horiz.svg", [], true); ?>`;
                actionsHtml += '</button>';
                actionsHtml += '<div class="action-dropdown-menu" id="' + dropdownId + '">';
                actionsHtml += '<button class="action-dropdown-item" onclick="show_settlement_details(\'' + settlements[j].settlement_id + '\', \'' + settlements[j].settlement_datetime + '\', \'' + settlements[j].account_name + '\', \'' + bankReference + '\', \'' + settlements[j].payout_amount + '\')" data-toggle="modal" data-target="#_show_settlement_details">View Details</button>';
                
                if (settlements[j].verification_status == 'Not Verified') {
                    actionsHtml += '<button class="action-dropdown-item" data-settlement_id="' + settlements[j].settlement_id + '" data-target="#_verify_settlement_modal" data-toggle="modal">Mark as Verified</button>';
                }
                
                actionsHtml += '</div>';
                actionsHtml += '</div>';
                actionsHtml += '</td>';
                
                html1 += actionsHtml;
                html1 += '</tr>';
              }
              html1 +='</tbody>';
              html1 +='<tfoot>';
              html1 +='<tr>';
              html1 +='<th colspan="5" style="font-weight: bold;">Total Amount</th>';
              html1 +='<th style="font-weight: bold;">'+settlement_amount+'</th>';
              html1 +='<th style="font-weight: bold;">'+totalTransAmount+'</th>';
              html1 +='<th style="font-weight: bold;">'+totalNotGeneartedTransAmount+'</th>';
              html1 +='<th></th>';
              html1 +='<th></th>';
              html1 +='<th></th>';
              html1 +='</tr>';
              html1 +='</tfoot>';
              html1 += '</table>';
              $('#settlement_table').html(html1);

              var table = $('#settlement_data_table').DataTable( {
                ordering: false,
                paging: false,
                searching: true,
                scrollY: "600px",
                scrollCollapse: true,
                scrollX: true,
                autoWidth: false,
                dom: '<"d-flex justify-content-end align-items-center mb-1"<"d-flex align-items-center"fB>>rtip',
                info: false,
                responsive: false,
                processing: false, // Disable default processing indicator
                oLanguage: {
                  sSearch: ""
                },
                // Force footer to appear above scrollbar
                drawCallback: function(settings) {
                  // Move footer above scrollbar if it exists
                  var $scrollFoot = $('.dataTables_scrollFoot');
                  var $scrollBody = $('.dataTables_scrollBody');
                  if ($scrollFoot.length && $scrollBody.length) {
                    $scrollFoot.insertBefore($scrollBody);
                  }
                },
                buttons: [
                  {
                    extend: 'colvis',
                    text: `<button class="btn btn-outline-primary" id="expbtns" style="margin-right:-7px;"><?= $this->load->view('svg_icons/column.svg', [], true); ?> Columns</button>`,
                    className: 'btn btn-outline-primary',
                  },
                  {
                    extend: 'print',
                    text: `<button class="btn btn-outline-primary" id="expbtns" style="margin-right:-7px;"><?= $this->load->view('svg_icons/print.svg', [], true); ?> Print</button>`,
                    filename: 'Settlement Report',
                    autoPrint: true,
                    exportOptions: {
                      columns: [0,1,2,3,4,5,6,7,8,9]
                    }
                  },
                  {
                    extend: 'excel',
                    text: `<button class="btn btn-outline-primary" id="expbtns"><?= $this->load->view('svg_icons/excel_2.svg', [], true); ?> Excel</button>`,
                    filename: 'Settlement Report',
                    exportOptions: {
                      columns: [0,1,2,3,4,5,6,7,8,9]
                    }
                  },
                ]
              });
              
              // Style the search input to match the button design
              styleSearchInput();
            }
        },
        error: function() {
            $('#progress').hide();
            $('#search').prop('disabled',false).val('Get Report'); // Reset button on error
        }
    });
}

// Function to style search input
function styleSearchInput() {
  // Method 1: Target the new DataTables DOM structure
  let $input = $('.dt-search input[type="search"]');
  if ($input.length) {      
    // Add placeholder
    $input.attr('placeholder', 'Search');
    
    // Style the input
    if ($input.parent().hasClass('search-box')) {
      $input.unwrap();
    }
    $input.siblings('.bi-search').remove();
    $input.addClass('input-search');
    if (!$input.parent().hasClass('search-box')) {
      $input.wrap('<div class="search-box position-relative" style="display: inline-block; margin-right: 1px; margin-bottom: 5px;"></div>');
      $input.parent().prepend('<i class="bi bi-search"></i>');
    }
    return true;
  }
  return false;
}

function exportToExcel_daily() {
  var schoolName = '<?php echo $this->settings->getSetting('school_name') ?>';
  var reportTitle = 'Online Payment Settlement Report';
  var header = '<h3 style="text-align:center;">' + schoolName + '</h3><h4 style="text-align:center;">' + reportTitle + '</h4>';
  var currentDate = '<div style="text-align:center; font-size:12px; margin-bottom:10px;">Generated on: ' + new Date().toLocaleString() + '</div>';

  var tableHtml = $("#settlement_table").html();
  if (!tableHtml) {
    alert("No data to export!");
    return;
  }

  var tempDiv = document.createElement('div');
  tempDiv.innerHTML = tableHtml;
  var table = tempDiv.querySelector('table');
  if (table) {
    // Add colgroup for proper column widths
    var colgroup = document.createElement('colgroup');

    // Set widths in px to avoid ##### issues in Excel
    var widths = [
      '100px', // S.No or ID
      '150px', // Student Name or Payee
      '150px', // Payment Method
      '200px', // Transaction ID
      '150px', // Amount
      '150px', // Status
      '180px', // Settlement Date
      '120px', // Class
      '120px', // Section
      '150px'  // Any other relevant column
    ];

    for (var i = 0; i < widths.length; i++) {
      var col = document.createElement('col');
      col.style.width = widths[i];
      colgroup.appendChild(col);
    }
    table.insertBefore(colgroup, table.firstChild);

    // Apply nowrap and min-width to all table cells
    var cells = table.querySelectorAll('th, td');
    cells.forEach(cell => {
      cell.style.whiteSpace = 'nowrap';
      cell.style.minWidth = '80px';
    });

    // Remove last th from thead (Actions)
    var theads = table.querySelectorAll('thead tr');
    theads.forEach(tr => {
      if (tr.lastElementChild) tr.removeChild(tr.lastElementChild);
    });

    // Remove last td from tbody (Actions)
    var tbodys = table.querySelectorAll('tbody tr');
    tbodys.forEach(tr => {
      if (tr.lastElementChild) tr.removeChild(tr.lastElementChild);
    });

    // Remove last td from tfoot (Actions) if present
    var tfoot = table.querySelector('tfoot');
    if (tfoot) {
      Array.from(tfoot.rows).forEach(row => {
        if (row.lastElementChild) row.removeChild(row.lastElementChild);
      });
    }
  }

  // Construct full HTML
  var htmls =
    '<div>' + header + currentDate + '</div>' +
    (table ? '<div>' + table.outerHTML + '</div>' : '');

  // Excel template and download logic
  var uri = 'data:application/vnd.ms-excel;base64,';
  var template =
    '<html xmlns:o="urn:schemas-microsoft-com:office:office" ' +
    'xmlns:x="urn:schemas-microsoft-com:office:excel" ' +
    'xmlns="http://www.w3.org/TR/REC-html40">' +
    '<head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet>' +
    '<x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions>' +
    '</x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->' +
    '<meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body>{table}</body></html>';

  var base64 = function (s) {
    return window.btoa(unescape(encodeURIComponent(s)));
  };

  var format = function (s, c) {
    return s.replace(/{(\w+)}/g, function (m, p) {
      return c[p];
    });
  };

  var ctx = {
    worksheet: 'Settlement Report',
    table: htmls
  };

  var link = document.createElement("a");
  link.download = "Settlement Report.xls";
  link.href = uri + base64(format(template, ctx));
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}


function printProfile() {
    const mainTableDiv = document.getElementById('settlement_table');
    if (!mainTableDiv) return;
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = mainTableDiv.innerHTML;
    const table = tempDiv.querySelector('table');
    if (table) {
        const theads = table.querySelectorAll('thead tr');
        theads.forEach(tr => {
            if (tr.lastElementChild) tr.removeChild(tr.lastElementChild);
        });
        const tbodys = table.querySelectorAll('tbody tr');
        tbodys.forEach(tr => {
            if (tr.lastElementChild) tr.removeChild(tr.lastElementChild);
        });
        const tfoot = table.querySelector('tfoot');
        if (tfoot) {
            const tbody = table.querySelector('tbody');
            if (tbody) {
                Array.from(tfoot.rows).forEach(row => {
                    const clonedRow = row.cloneNode(true);
                    if (clonedRow.lastElementChild) {
                        clonedRow.removeChild(clonedRow.lastElementChild);
                    }
                    tbody.appendChild(clonedRow);
                });
            }
            tfoot.parentNode.removeChild(tfoot);
        }
    }

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>Settlement Overview Report</title>
            <style>
                body {
                    font-family:sans-serif;
                    padding: 20px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 15px 0;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    font-size: 12px;
                }
                h3 { margin: 15px 0; }
                @media print {
                    table { page-break-inside: auto }
                    tr { page-break-inside: avoid }
                }
            </style>
        </head>
        <body>
            <center><h2><?php echo $this->settings->getSetting('school_name'); ?></h2></center>
            ${tempDiv.innerHTML}
            <script>
                window.onload = function() {
                    window.print();
                };
                window.onafterprint = function() {
                    window.close();
                };
            <\/script>
        </body>
        </html>
    `);

    printWindow.document.close();
}

function printSettelemntDetails(){
  var divContents = document.getElementById("_show_settlement_details").innerHTML;
    $('tr,th,td').css('max-width', 'fit-content').css('min-width', 'fit-content').css('border', '1px solid black');
    $('tr,th,td').css('border-collapse', 'collapse');
    $('tr').css('background', 'white');
    var a = window.open('');
    a.document.write('<html>');
    a.document.write('<body><center><h2><?php echo $this->settings->getSetting('school_name'); ?></h2></center>');
    a.document.write(document.getElementById("settlement_match_div").innerHTML);
    a.document.write(document.getElementById("settlement_summary_div").innerHTML);
    a.document.write(document.getElementById("tx_split_summary_div").innerHTML);
    a.document.write(document.getElementById("tx_table_div").innerHTML);
    // a.document.write(divContents);
    a.document.write('</body></html>');
    a.document.close();
    a.print();
    a.close();
    $("#_show_settlement_details").html(divContents);
  }

function excelSettelemntDetails(){
  var htmls = "";
  var uri = 'data:application/vnd.ms-excel;base64,';
  var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
  var base64 = function(s) {
      return window.btoa(unescape(encodeURIComponent(s)))
  };

  var format = function(s, c) {
      return s.replace(/{(\w+)}/g, function(m, p) {
          return c[p];
      })
  };

  var settlementDiv = $("#settlement_match_div").html();
  var summaryDiv = $("#settlement_summary_div").html();
  var splitDiv = $("#tx_split_summary_div").html();
  var txDiv = $("#tx_table_div").html();
  // var mainTable = $("#printArea").html();
  

  htmls = settlementDiv + summaryDiv + splitDiv + txDiv;

  var ctx = {
      worksheet : 'Spreadsheet',
      table : htmls
  }


  var link = document.createElement("a");
  link.download = "Settlement Report.xls";
  link.href = uri + base64(format(template, ctx));
  link.click();
}

// Dropdown functionality
function toggleDropdown(dropdownId) {
    // Close all other dropdowns
    $('.action-dropdown-menu').removeClass('show');
    
    // Toggle current dropdown
    $('#' + dropdownId).toggleClass('show');
}

// Close dropdown when clicking outside
$(document).click(function(event) {
    if (!$(event.target).closest('.action-dropdown').length) {
        $('.action-dropdown-menu').removeClass('show');
    }
});
</script>

<style>
    /* @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500&display=swap'); */

    table {
      font-family:sans-serif !important;
    }

    #settlement_data_table {
      width: 100%;
      border-collapse: collapse;
      background-color: #ffffff;
      border-radius: 1.5rem;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
      opacity: 1 !important;
      transition: none !important;
    }

    #settlement_data_table thead th {
      position: sticky !important;
      top: -6;
      background-color: #f1f5f9;
      color: #111827;
      font-size: 11px;
      font-weight: 500;
      z-index: 10;
      text-align: left;
      padding: 12px 16px;
    }

    #settlement_data_table th,
    #settlement_data_table td {
      padding: 10px 14px;
      border-bottom: 1px solid #e5e7eb;
      font-size: 11px;
      font-weight: 400;
    }

    #settlement_data_table tbody tr:nth-child(even) {
      background-color: #f9fafb;
    }

    #settlement_data_table tbody tr:hover {
      background-color: #f1f5f9;
    }


    #settlement_data_table tfoot th,
    #settlement_data_table tfoot td {
      background: #f3f4f6;
    }

    /* DataTables scroll container */
    #settlement_table {
      margin-bottom: 0 !important;
    }

    #settlement_data_table {
      width: 100% !important;
      margin-bottom: 0 !important;
    }

    /* DataTables scroll styling */
    .dataTables_scrollHead {
      border-bottom: 1px solid #dee2e6;
    }

    .dataTables_scrollHeadInner {
      width: 100% !important;
      padding-right: 0 !important;
    }

    .dataTables_scrollBody {
      overflow-y: auto !important;
      overflow-x: auto !important;
      border-bottom: 1px solid #dee2e6;
    }

    /* Ensure header and body have matching widths */
    .dataTables_scrollHead table,
    .dataTables_scrollBody table {
      width: 100% !important;
      margin-bottom: 0 !important;
    }

    /* Footer positioning - should appear above horizontal scrollbar */
    .dataTables_scrollFoot {
      border-top: 1px solid #dee2e6;
      border-bottom: 1px solid #dee2e6;
      background: #f3f4f6 !important;
      position: relative !important;
      z-index: 1 !important;
      clear: both !important;
    }

    .dataTables_scrollFoot table {
      width: 100% !important;
      margin-bottom: 0 !important;
    }

    .dataTables_scrollFoot th,
    .dataTables_scrollFoot td {
      background: #f3f4f6 !important;
      font-weight: bold !important;
      border-top: 1px solid #dee2e6 !important;
    }

    /* Fix the overall scroll container */
    .dataTables_scroll {
      position: relative !important;
      border-bottom: 1px solid #dee2e6;
    }

    /* Ensure proper alignment of all scroll containers */
    .dataTables_scrollHead,
    .dataTables_scrollBody {
      width: 100% !important;
    }

    /* Footer should be full width and positioned above scrollbar */
    .dataTables_scrollFoot {
      width: 100% !important;
      display: block !important;
    }

    /* Custom scrollbar styling for DataTables */
    .dataTables_scrollBody::-webkit-scrollbar {
      width: 12px;
      height: 12px;
    }

    .dataTables_scrollBody::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 6px;
    }

    .dataTables_scrollBody::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 6px;
    }

    .dataTables_scrollBody::-webkit-scrollbar-thumb:hover {
      background: #555;
    }

    /* Original tfoot styling removed - now handled by DataTables scroll footer */

    .dataTables_wrapper .dt-buttons {
      float: right;
    }

    .dataTables_wrapper .dataTables_filter {
      float: none;
      text-align: left;
      width: unset;
    }

    #datatable_controls .form-control {
      display: inline-block;
      width: auto;
    }

    #datatable_controls .btn {
      margin-left: 5px;
    }

    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dt-buttons,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
      display: none !important;
    }

    .content-scrollable::-webkit-scrollbar {
      width: 12px;
    }

    .content-scrollable::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 6px;
    }

    .content-scrollable::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 6px;
    }

    .content-scrollable::-webkit-scrollbar-thumb:hover {
      background: #555;
    }

    .card-body::-webkit-scrollbar {
      width: 12px;
    }

    .card-body::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 6px;
    }

    .card-body::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 6px;
    }

    .card-body::-webkit-scrollbar-thumb:hover {
      background: #555;
    }

    .table thead th {
      position: sticky;
      top: 0;
      background: white;
      z-index: 10;
      border-bottom: 2px solid #dee2e6;
    }

    .modal-body::-webkit-scrollbar {
      width: 12px;
    }

    .modal-body::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 6px;
    }

    .modal-body::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 6px;
    }

    .modal-body::-webkit-scrollbar-thumb:hover {
      background: #555;
    }

    .content-scrollable {
      scrollbar-width: thick;
      scrollbar-color: #888 #f1f1f1;
    }

    .card-body {
      scrollbar-width: thick;
      scrollbar-color: #888 #f1f1f1;
    }

    .modal-body {
      scrollbar-width: thick;
      scrollbar-color: #888 #f1f1f1;
    }



    /* Settlement Details Modal Styling - Enhanced with blurry background */
    /* Modal backdrop styling - Grey translucent overlay covering entire screen */
    .modal-backdrop {
      background-color: rgba(128, 128, 128, 0.6) !important;
      backdrop-filter: blur(2px) !important;
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      z-index: 9999 !important
    }
    
    /* Ensure modal backdrop covers everything */
    body.modal-open {
      overflow: hidden !important;
    }
    
    /* Force backdrop to cover entire viewport */
    #_show_settlement_details.modal-backdrop {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      z-index: 9999 !important;
      background-color: rgba(128, 128, 128, 0.6) !important;
    }
    
    /* Modal dialog positioning and sizing - Increased horizontal size */
    #_show_settlement_details .modal-dialog {
      margin: 30px auto !important;
      max-width: 90% !important;
      width: 1400px !important;
      z-index: 10000 !important;
      position: relative !important;
    }
    
    /* Modal content styling - Enhanced popup effect */
    #_show_settlement_details .modal-content {
      border: none !important;
      border-radius: 0px !important;
      box-shadow: 0 8px 32px rgba(0,0,0,0.25) !important;
      background-color: #ffffff !important;
      transform: scale(1) !important;
      transition: all 0.3s ease !important;
    }
    
    /* Modal header styling - Light purple theme */
    #_show_settlement_details .modal-header {
      background-color: #efecfd !important;
      border-bottom: 1px solid #dee2e6 !important;
      border-radius: 0px !important;
      padding: 20px 25px !important;
    }
    
    #_show_settlement_details .modal-title {
      color: #000000 !important;
      font-weight: 600 !important;
      font-size: 20px !important;
    }
    
    #_show_settlement_details .modal-header .close {
      color: #000000 !important;
      opacity: 0.8 !important;
      font-size: 24px !important;
    }
    
    #_show_settlement_details .modal-header .close:hover {
      opacity: 1 !important;
      color: #333333 !important;
    }
    
    /* Modal body styling */
    #_show_settlement_details .modal-body {
      padding: 20px !important;
      background-color: #ffffff !important;
      max-height: 80vh !important;
      min-height: 60vh !important;
      overflow-y: auto !important;
      overflow-x: auto !important;
    }
    
    /* Action buttons styling */
    #_show_settlement_details .action-buttons {
      position: sticky !important;
      top: 0 !important;
      background: #ffffff !important;
      z-index: 1000 !important;
      padding: 15px 20px !important;
      border-bottom: 1px solid #dee2e6 !important;
    }
    
    /* Table styling within modal */
    #_show_settlement_details .table {
      margin-bottom: 0 !important;
      border-radius: 0px !important;
      overflow: hidden !important;
      border: 1px solid #dee2e6 !important;
      table-layout: auto !important;
      width: 100% !important;
      min-width: 1200px !important;
      border-collapse: collapse !important;
    }
    
    #_show_settlement_details .table th {
      background-color: #efecfd !important;
      border: 1px solid #dee2e6 !important;
      border-right: 1px solid #dee2e6 !important;
      border-left: 1px solid #dee2e6 !important;
      font-weight: 600 !important;
      color: #000000 !important;
      padding: 12px 15px !important;
      white-space: nowrap !important;
      min-width: auto !important;
      width: auto !important;
      text-align: center !important;
    }
    
    #_show_settlement_details .table td {
      border: 1px solid #dee2e6 !important;
      border-right: 1px solid #dee2e6 !important;
      border-left: 1px solid #dee2e6 !important;
      vertical-align: middle !important;
      padding: 12px 15px !important;
      background-color: #ffffff !important;
      color: #000000 !important;
      word-wrap: break-word !important;
      word-break: break-word !important;
      white-space: normal !important;
      overflow-wrap: break-word !important;
      min-width: auto !important;
      width: auto !important;
    }
    
    #_show_settlement_details .table tbody tr:nth-child(even) {
      background-color: #fafafa !important;
    }
    
    /* Content scrollable styling */
    #_show_settlement_details .content-scrollable {
      max-height: calc(80vh - 120px) !important;
      overflow-y: auto !important;
      overflow-x: auto !important;
    }

    /* Card body within modal styling */
    #_show_settlement_details .card-body {
      overflow-x: auto !important;
      overflow-y: auto !important;
    }

    /* Specific styling for the receipts table */
    #_show_settlement_details #tx_table_div {
      overflow-x: auto !important;
    }

    #_show_settlement_details #tx_table_div .table {
      min-width: 100% !important;
      table-layout: auto !important;
      border-collapse: collapse !important;
    }

    #_show_settlement_details #tx_table_div .table th {
      white-space: nowrap !important;
      min-width: fit-content !important;
      padding: 8px 12px !important;
      font-size: 12px !important;
      border: 1px solid #dee2e6 !important;
      border-right: 1px solid #dee2e6 !important;
      border-left: 1px solid #dee2e6 !important;
      background-color: #efecfd !important;
      font-weight: 600 !important;
    }

    #_show_settlement_details #tx_table_div .table td {
      white-space: nowrap !important;
      padding: 8px 12px !important;
      font-size: 12px !important;
      min-width: fit-content !important;
      border: 1px solid #dee2e6 !important;
      border-right: 1px solid #dee2e6 !important;
      border-left: 1px solid #dee2e6 !important;
    }
    
    /* Popup animation effects */
    #_show_settlement_details.modal.fade .modal-dialog {
      transform: translate(0, -50px) !important;
      transition: transform 0.3s ease-out !important;
    }
    
    #_show_settlement_details.modal.show .modal-dialog {
      transform: translate(0, 0) !important;
    }
    
    /* Ensure backdrop appears immediately */
    #_show_settlement_details.modal-backdrop {
      opacity: 0.6 !important;
    }
    
    /* Additional popup styling */
    #_show_settlement_details .modal-content {
      position: relative !important;
      z-index: 10000 !important;
    }
    
    /* Ensure modal container covers full screen */
    #_show_settlement_details.modal {
      z-index: 10000 !important;
    }
    
    /* Force full viewport coverage */
    .modal-backdrop.show {
      opacity: 0.6 !important;
    }
    
    /* Additional backdrop positioning */
    .modal-backdrop.fade {
      opacity: 0 !important;
    }
    
    .modal-backdrop.fade.show {
      opacity: 0.6 !important;
    }
    
    /* Ensure no scrollbars interfere */
    html.modal-open {
      overflow: hidden !important;
    }

    /* Sticky header styling */
    #_show_settlement_details .sticky-header {
      position: sticky !important;
      top: 0 !important;
      background: white !important;
      z-index: 999 !important;
      border-bottom: 2px solid #dee2e6 !important;
    }

    /* Fix DataTable header z-index conflicts with modal */
    body.modal-open #settlement_data_table thead th {
      z-index: 1 !important;
    }
    
    body.modal-open .dataTables_scrollHead {
      z-index: 1 !important;
    }
    
    body.modal-open .dataTables_scrollHead th {
      z-index: 1 !important;
    }
    
    /* Ensure modal appears above everything */
    body.modal-open #_show_settlement_details {
      z-index: 10000 !important;
    }
    
    body.modal-open #_show_settlement_details .modal-dialog {
      z-index: 10001 !important;
    }
    
    body.modal-open #_show_settlement_details .modal-content {
      z-index: 10002 !important;
    }

    /* Override any DataTable sticky positioning when modal is open */
    body.modal-open .table thead th {
      position: relative !important;
      z-index: 1 !important;
    }

    /* Fix z-index for verification modal - needs to be higher than settlement details modal */
    #_verify_settlement_modal {
      z-index: 10050 !important;
    }
    
    #_verify_settlement_modal .modal-dialog {
      z-index: 10051 !important;
    }
    
    #_verify_settlement_modal .modal-content {
      z-index: 10052 !important;
    }
    
    /* Verification modal backdrop should be higher than settlement modal */
    body.modal-open #_verify_settlement_modal.modal-backdrop {
      z-index: 10049 !important;
    }
    
    /* Ensure verification modal appears above settlement details modal */
    #_verify_settlement_modal.modal.show {
      z-index: 10050 !important;
    }
    
    #_verify_settlement_modal.modal.fade .modal-dialog {
      transform: translate(0, -50px) !important;
      transition: transform 0.3s ease-out !important;
    }
    
    #_verify_settlement_modal.modal.show .modal-dialog {
      transform: translate(0, 0) !important;
    }
    
    /* Verification modal styling to match the design */
    #_verify_settlement_modal .modal-dialog {
      margin: 50px auto !important;
      max-width: 50% !important;
      width: 600px !important;
      z-index: 10051 !important;
      position: relative !important;
      max-height: 80vh !important;
    }
    
    #_verify_settlement_modal .modal-content {
      border: none !important;
      border-radius: 0px !important;
      box-shadow: 0 8px 32px rgba(0,0,0,0.25) !important;
      background-color: #ffffff !important;
      transform: scale(1) !important;
      transition: all 0.3s ease !important;
      overflow-y: visible !important;
      max-height: none !important;
    }
    
    #_verify_settlement_modal .modal-header {
      background-color: #efecfd !important;
      border-bottom: 1px solid #dee2e6 !important;
      border-radius: 0px !important;
      padding: 20px 25px !important;
    }
    
    #_verify_settlement_modal .modal-title {
      color: #000000 !important;
      font-weight: 600 !important;
      font-size: 20px !important;
    }
    
    #_verify_settlement_modal .modal-header .close {
      color: #000000 !important;
      opacity: 0.8 !important;
      font-size: 24px !important;
    }
    
    #_verify_settlement_modal .modal-body {
      padding: 25px !important;
      background-color: #ffffff !important;
    }
    
    #_verify_settlement_modal .modal-footer {
      background-color: #ffffff !important;
      border-top: 1px solid #dee2e6 !important;
      border-radius: 0px !important;
      padding: 20px 25px !important;
    }

    /* Ensure verification modal backdrop doesn't interfere */
    #_verify_settlement_modal.modal-backdrop {
      background-color: rgba(128, 128, 128, 0.3) !important;
      backdrop-filter: blur(1px) !important;
      z-index: 10049 !important;
    }

    /* Form controls styling within verification modal */
    #_verify_settlement_modal .form-control {
      border: 1px solid #dee2e6 !important;
      border-radius: 4px !important;
      padding: 8px 12px !important;
      font-size: 14px !important;
    }

    #_verify_settlement_modal .form-control:focus {
      border-color: #623ce7 !important;
      box-shadow: 0 0 0 0.2rem rgba(98, 60, 231, 0.25) !important;
    }

    /* Button styling within verification modal */
    #_verify_settlement_modal .btn-primary {
      background-color: #623ce7 !important;
      border-color: #623ce7 !important;
      color: #ffffff !important;
      font-weight: 600 !important;
    }

    #_verify_settlement_modal .btn-primary:hover {
      background-color: #4a2bb8 !important;
      border-color: #4a2bb8 !important;
    }
  </style>