<?php include(APPPATH . 'views/feesv2/reports/components/report_header.php'); ?>

<style>
    .no-data-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    background-color: #f8f8fc;
    border-radius: 12px;
    margin: 20px 0;
    min-height: 400px;
}

.no-data-icon {
    margin-bottom: 30px;
}

.no-data-icon svg {
    width: 180px;
    height: 180px;
}

.no-data-content {
    text-align: center;
    max-width: 400px;
}

.no-data-title {
    font-size: 24px;
    font-weight: 600;
    color: #333333;
    margin: 0 0 16px 0;
    line-height: 1.3;
}

.no-data-description {
    font-size: 16px;
    line-height: 1.5;
    font-weight: 400;
}

.no-data-description:last-child {
    margin-bottom: 0;
}
</style>
    <div class="row">
        <div class="col-12 md-1">
            <?php if ($this->authorization->isSuperAdmin()) { ?>
                 <a target="_blank" onclick="redrive_success_payment()" class="getreport_invert" style="margin-left: 16px !important; margin-right: 20px">Success Payment Redrive</a>
              <?php } ?>
        </div>
    </div>
    
    <div class="card-body">
  <div class="row g-3 align-items-end">
    
    <!-- Search by Student Name -->
    <div class="col-md-3">
      <label class="form-label">Search By Student Name</label>
      <div class="input-group">
        <input id="stdName1" autocomplete="off" onkeyup="disableStudentButton()" placeholder="Search by Student Name" class="form-control" name="stdName1">
        <button id="getByStdName" class="getreport_btn_2" disabled>Get</button>
      </div>
    </div>

    <!-- Search by Date -->
    <div class="col-md-3">
      <label class="form-label">Search By Date</label>
      <div class="input-group">
        <input id="init_date" autocomplete="off" placeholder="Search by Date" class="form-control" name="init_date" value="<?php echo date('d-m-Y'); ?>">
        <span class="input-group-text"><?= $this->load->view('svg_icons/calendar_month.svg', [], true); ?></span>
        <button id="getByDate" class="getreport_btn_2">Get</button>
      </div>
    </div>

    <!-- Search by Order Id -->
    <div class="col-md-3">
      <label class="form-label">Search By Order Id</label>
      <div class="input-group">
        <input id="order_id" autocomplete="off" onkeyup="disableOrderButton()" placeholder="Search by Order Id" class="form-control" name="order_id">
        <button id="getByOrderId" class="getreport_btn_2" disabled>Get</button>
      </div>
    </div>

    <!-- Search by Transaction Id -->
    <div class="col-md-3">
      <label class="form-label">Search By Transaction Id</label>
      <div class="input-group">
        <input id="transaction_id" autocomplete="off" onkeyup="disableTransactionButton()" placeholder="Search by Transaction Id" class="form-control" name="transaction_id">
        <button id="getByTransactionId" class="getreport_btn_2" disabled>Get</button>
      </div>
    </div>

  </div>
</div>


        <div id="loader" class="loaderclass" style="display:none;"></div>
        <div class="row" style="margin: 0px;display: block;">
            <div class="card-body studentData leaveData table-responsive" id="transactionData">
                <h5>Select filter to get Transaction list</h5>
            </div>
        </div>
    </div>
</div>




<div class="col-md-12">
   
</div>

<style type="text/css">
.loaderclass {
  border: 8px solid #eee;
  border-top: 8px solid #7193be;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  position: fixed;
  z-index: 1;
  animation: spin 2s linear infinite;
  margin-top: 50%;
  margin-left: 40%;
  position: absolute;
  z-index: 99999;
}
.widthadjust{
    width: 32%;
    margin: auto;
}

/* Success Payment Redrive Popup Styling */
.widthadjust .modal-content {
    border-radius: 12px !important;
    border: none !important;
    box-shadow: 0px 10px 40px rgba(0, 0, 0, 0.15) !important;
    z-index: 99999 !important;
}

.widthadjust {
    z-index: 99999 !important;
}

/* Background blur effect for popup - only backdrop gets blurred */
.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.3) !important;
    backdrop-filter: blur(2px) !important;
    -webkit-backdrop-filter: blur(2px) !important;
    z-index: 99998 !important;
}

/* Ensure popup content is completely sharp and above backdrop */
.widthadjust {
    filter: none !important;
    z-index: 99999 !important;
}

.widthadjust .modal-content {
    filter: none !important;
    z-index: 99999 !important;
    position: relative !important;
}

.widthadjust .modal-content * {
    filter: none !important;
}

.widthadjust .modal-header {
    background-color: #f8f8fc !important;
    border-bottom: 1px solid #e5e7eb !important;
    border-radius: 12px 12px 0 0 !important;
    padding: 20px 25px !important;
}

.widthadjust .modal-title {
    color: #333333 !important;
    font-weight: 600 !important;
    font-size: 18px !important;
    margin: 0 !important;
}

/* Hide the close button in Bootbox modal */
.widthadjust .modal-header .close,
.widthadjust .modal-header .bootbox-close-button,
.widthadjust .modal-header button[aria-label="Close"],
.widthadjust .modal-header .fa-times {
    display: none !important;
}

.widthadjust .modal-body {
    padding: 25px !important;
    background-color: #ffffff !important;
    color: #333333 !important;
    font-size: 16px !important;
    line-height: 1.5 !important;
}

.widthadjust .modal-footer {
    background-color: #ffffff !important;
    border-top: 1px solid #e5e7eb !important;
    border-radius: 0 0 12px 12px !important;
    padding: 20px 25px !important;
    text-align: right !important;
}


/* Custom styles for date picker width and positioning */
#datetimepicker1 {
    max-width: 250px !important;
    width: 250px !important;
    position: relative !important;
    z-index: 1000 !important;
}

#datetimepicker1 .form-control {
    width: 180px !important;
}

/* Ensure the input group doesn't clip the calendar */
.input-group.date {
    position: relative !important;
    z-index: 1000 !important;
}

/* Fix for Bootstrap grid overflow */
.col-md-4.col-md-offset-3 {
    position: relative !important;
    overflow: visible !important;
}

/* Ensure the row container allows overflow */
.row {
    overflow: visible !important;
    position: relative !important;
}

/* Calendar dropdown positioning and containment */
.bootstrap-datetimepicker-widget.dropdown-menu {
    z-index: 99999 !important;
    border: 1px solid #e5e7eb !important;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: auto !important;
    max-width: 260px !important;
    width: 260px !important;
    margin-top: 2px !important;
}

/* Ensure calendar stays within viewport */
.bootstrap-datetimepicker-widget {
    position: relative !important;
    overflow: visible !important;
    z-index: 99999 !important;
}

/* Container positioning to prevent overflow */
#datetimepicker1 {
    position: relative !important;
    overflow: visible !important;
    z-index: 99999 !important;
}

/* Ensure calendar appears above DataTable headers */
.bootstrap-datetimepicker-widget,
.bootstrap-datetimepicker-widget * {
    z-index: 99999 !important;
}

/* Adjust calendar position if it would overflow right */
.bootstrap-datetimepicker-widget.dropdown-menu.pull-right {
    left: auto !important;
    right: 0 !important;
}

/* Ensure calendar doesn't overflow container */
.form-group {
    overflow: visible !important;
    position: relative !important;
}

/* Data table horizontal scroll */
#transactionData {
    overflow-x: auto !important;
    width: 100% !important;
}

#transactionData .table {
    min-width: 100% !important;
    width: max-content !important;
}

#transactionData table {
    min-width: 100% !important;
    width: max-content !important;
}

/* Modal z-index fix for Redrive Status popup */
#summary.modal {
    z-index: 999999 !important;
}

#summary .modal-dialog {
    z-index: 999999 !important;
}

#summary .modal-content {
    z-index: 999999 !important;
    position: relative !important;
}

/* Bootbox.js popup z-index fix for Success Payment Redrive */
.bootbox {
    z-index: 999999 !important;
}

.bootbox .modal {
    z-index: 999999 !important;
}

.bootbox .modal-dialog {
    z-index: 999999 !important;
}

.bootbox .modal-content {
    z-index: 999999 !important;
    position: relative !important;
}

.modal-backdrop {
    z-index: 999998 !important;
}

/* Ensure modal appears above DataTable */
.dataTables_wrapper {
    z-index: 1 !important;
}

.dataTables_scrollHead {
    z-index: 1 !important;
}

/* Remove all border radius and shadows from calendar elements */
.bootstrap-datetimepicker-widget,
.bootstrap-datetimepicker-widget *,
.bootstrap-datetimepicker-widget table,
.bootstrap-datetimepicker-widget table *,
.bootstrap-datetimepicker-widget .dropdown-menu,
.bootstrap-datetimepicker-widget .dropdown-menu * {
    border-radius: 0 !important;
    -webkit-border-radius: 0 !important;
    -moz-border-radius: 0 !important;
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
}

/* Specifically target calendar header and all its elements */
.bootstrap-datetimepicker-widget table thead,
.bootstrap-datetimepicker-widget table thead th,
.bootstrap-datetimepicker-widget table thead tr,
.bootstrap-datetimepicker-widget table thead td {
    border-radius: 0 !important;
    -webkit-border-radius: 0 !important;
    -moz-border-radius: 0 !important;
}

/* Style for dates not in current month (old and new dates) */
.bootstrap-datetimepicker-widget td.old,
.bootstrap-datetimepicker-widget td.new {
    color: #D1D5DB !important;
    background-color: #F9FAFB !important;
    opacity: 0.6 !important;
    pointer-events: none !important;
    cursor: not-allowed !important;
}

.bootstrap-datetimepicker-widget td.old:hover,
.bootstrap-datetimepicker-widget td.new:hover {
    color: #D1D5DB !important;
    background-color: #F9FAFB !important;
    opacity: 0.6 !important;
    pointer-events: none !important;
    cursor: not-allowed !important;
}

/* Ensure off dates are also non-accessible */
.bootstrap-datetimepicker-widget td.off {
    color: #D1D5DB !important;
    background-color: #F9FAFB !important;
    opacity: 0.6 !important;
    pointer-events: none !important;
    cursor: not-allowed !important;
}

.bootstrap-datetimepicker-widget td.off:hover {
    color: #D1D5DB !important;
    background-color: #F9FAFB !important;
    opacity: 0.6 !important;
    pointer-events: none !important;
    cursor: not-allowed !important;
}

.getreport_btn {
    width: 100%;           /* take full width of card */
    max-width: 220px;      /* optional: limit max size */
    padding: 10px 16px;    /* adjust padding to fit */
    box-sizing: border-box; /* include padding inside width */
    margin: 0 auto;        /* center inside card */
    display: block;        /* ensures proper alignment */
}

/* Dropdown menu styles */
.action-dropdown {
    position: relative;
    display: inline-block;
}

.action-dropdown-btn {
    background: none;
    border: 1px solid #e5e7eb;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.action-dropdown-btn:hover {
    background-color: #f5f5f5;
    border-color: #623CE7;
}

.action-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    z-index: 1000;
    min-width: 150px;
    display: none;
}

.action-dropdown-menu.show {
    display: block;
}

.action-dropdown-item {
    display: block;
    width: 100%;
    padding: 10px 16px;
    text-align: left;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 14px;
    color: #374151;
    transition: background-color 0.2s ease;
}

.action-dropdown-item:hover {
    background-color: #f9fafb;
    color: #623CE7;
}

.action-dropdown-item:first-child {
    border-radius: 8px 8px 0 0;
}

.action-dropdown-item:last-child {
    border-radius: 0 0 8px 8px;
}

.action-dropdown-item:only-child {
    border-radius: 8px;
}

</style>

<script>

    function redrive_success_payment() {
        var date = $('#init_date').val();
        bootbox.dialog({
            title : "Confirm", 
            message: 'Redrive payment date : '+date,
            className: 'widthadjust',
            closeButton: false,
            buttons: {
                cancel: {
                    label: "Cancel",
                    className: 'getreport_btn_2',
                    callback: function(){

                    }
                },
                ok: {
                    label: "Yes",
                    className: 'getreport_invert',
                    callback: function(){
                        window.location.href='<?php echo site_url('Callback_Controller/fees_online_payment_success_check/') ?>'+date;
                    }
                }
            }
        });

    }

  function submitForm (id) {
    $("#opm_id").val(id);
    $("#tx_detail_form").submit();
  }
  function submitTxRedriveForm (id) {
    $('#confirmBtn').hide();
    $('#modal-data').html('<div></div>');
    $.ajax({
            url: "<?php echo site_url('payment_controller/get_transaction_status');?>",
            data: {'opm_id':id},
            type: 'post',
            success: function(data) {
                var tx_status = JSON.parse(data);
                //console.log(tx_status);
                dataLen = tx_status.data.length;
                tx_status_string = '';
                for (i=0;i<dataLen;i+=50) {
                    tx_status_string += tx_status.data.substring(i, i + 50) + ' ';
                }

                html = 'Result: ' + tx_status.result + '<br>';
                html += 'Data: ' + tx_status_string + '<br>';
                var response = JSON.parse(tx_status.data);
                if (tx_status.result == 'SUCCESS') {
                    if (typeof(response.data[0]) != "undefined") {
                        if (response.data[0].response_code == '0') {
                            $('#confirmBtn').show();
                        }
                    }
                }else{
                    $('#confirmBtn').hide();
                }
                // if (tx_status.result.indexOf('"response_code":0') >= 0) {
                //     $('#confirmBtn').hide();
                // }

                $('#modal-data').html('<div>'+html+'</div>');
                $("#response").val(tx_status.data);
                // $('#modal-data').html('<h4>Test</h4>');
            },
            error: function(err) {
                console.log(err);
            }
        });

    // $("#tx_redrive_form").submit();
  }

  function updateFunction() {
    $("#tx_redrive_form").submit();
  }

  $("#stdName1").keydown(function(e) {
        if(e.keyCode == 13) {
            getByStdName();
        }
    });

    $("#getByStdName").click(function (){
        getByStdName();
    });

    function getByStdName() {
        var name = $("#stdName1").val();
        $('#loader').show();
        $.ajax({
            url: '<?php echo site_url('payment_controller/getTransactionDetailsByStudent'); ?>',
            type: 'post',
            data: {'name':name, 'mode':'std_name'},
            success: function(data) {
                $('#loader').hide();
                var std = JSON.parse(data);
                $(".studentData").html(prepare_student_table(std));
            },
            error: function (err) {
                console.log(err);
            }
        });
    }

    $("#order_id").keydown(function(e) {
        if(e.keyCode == 13) {
            getByOrderId();
        }
    });

    $("#getByOrderId").click(function (){
        getByOrderId();
    });


    function getByOrderId() {
        var order = $("#order_id").val();
        $('#loader').show();

        $.ajax({
            url: '<?php echo site_url('payment_controller/getTransactionDetailsByStudent/'); ?>',
            type: 'post',
            data: {'order':order, 'mode':'order_id'},
            success: function(data) {
                $('#loader').hide();
                var std = JSON.parse(data);
                $(".studentData").html(prepare_student_table(std));
            },
            error: function (err) {
                console.log(err);
            }
        });
    }

    $("#transaction_id").keydown(function(e) {
        if(e.keyCode == 13) {
            getByOrderId();
        }
    });

    $("#getByTransactionId").click(function (){
        getByTransactionId();
    });


    function getByTransactionId() {
        var transaction = $("#transaction_id").val();
        $('#loader').show();

        $.ajax({
            url: '<?php echo site_url('payment_controller/getTransactionDetailsByStudent/'); ?>',
            type: 'post',
            data: {'transaction':transaction, 'mode':'transaction_id'},
            success: function(data) {
                $('#loader').hide();
                var std = JSON.parse(data);
                $(".studentData").html(prepare_student_table(std));
            },
            error: function (err) {
                console.log(err);
            }
        });
    }

    $("#init_date").keydown(function(e) {
        if(e.keyCode == 13) {
            getByOrderId();
        }
    });

    $("#getByDate").click(function (){
        getByDate();
    });


    function getByDate() {
        var date = $("#init_date").val();
        $('#loader').show();

        $.ajax({
            url: '<?php echo site_url('payment_controller/getTransactionDetailsByStudent/'); ?>',
            type: 'post',
            data: {'date':date, 'mode':'init_date'},
            success: function(data) {
                $('#loader').hide();
                var std = JSON.parse(data);
                $(".studentData").html(prepare_student_table(std));

                $('#customers2').DataTable({
                    "language": {
                        "search": "",
                        "searchPlaceholder": "Enter Search..."
                    },
                    dom: '<"d-flex justify-content-end align-items-center mb-1"<"d-flex align-items-center"fB>>rtip',
                    paging: false,
                    scrollY: '600px',
                    scrollCollapse: true,
                    autoWidth: false,
                    oLanguage: {
                        sSearch: ""
                    },
                    buttons: [
                        {
                            extend: 'excel',
                            text: `<button class="btn btn-outline-primary" id="expbtns"><?= $this->load->view('svg_icons/excel_2.svg', [], true); ?> Excel</button>`,
                            filename: 'Online Transaction Report',
                            exportOptions: {
                                columns: ':visible'
                            },
                        },
                        {
                            extend: 'print',
                            text: `<button class="btn btn-outline-primary" id="expbtns" style="margin-right:-7px;"><?= $this->load->view('svg_icons/print.svg', [], true); ?> Print</button>`,
                            filename: 'Online Transaction Report',
                            exportOptions: {
                                columns: ':visible'
                            },
                        }
                    ]
                });
                
                // Style the search input to match the button design
                styleSearchInput();
            },
            error: function (err) {
                console.log(err);
            }
        });
    }

    function disableTransactionButton(){
      var getButton = document.getElementById('getByTransactionId');
      var inputField = document.getElementById('transaction_id');
      // var student = document.getElementById('multi_users_to_2');
      if (inputField.value == ''){
        getButton.disabled = true;
      }else {
        getButton.disabled = false;
      }
    }

    function disableOrderButton(){
      var getButton = document.getElementById('getByOrderId');
      var inputField = document.getElementById('order_id');
      // var student = document.getElementById('multi_users_to_2');
      if (inputField.value == ''){
        getButton.disabled = true;
      }else {
        getButton.disabled = false;
      }
    }

    function disableStudentButton(){
      var getButton = document.getElementById('getByStdName');
      var inputField = document.getElementById('stdName1');
      // var student = document.getElementById('multi_users_to_2');
      if (inputField.value == ''){
        getButton.disabled = true;
      }else {
        getButton.disabled = false;
      }
    }

    function disableDateButton(){
      var getButton = document.getElementById('getByDate');
      var inputField = document.getElementById('init_date');
      // var student = document.getElementById('multi_users_to_2');
      if (inputField.value == ''){
        getButton.disabled = true;
      }else {
        getButton.disabled = false;
      }
    }

    var is_admin = "<?php echo $this->authorization->isSuperAdmin() ?>";

    function prepare_student_table (std) {
        //console.log(std);
    var html = '';
    var disable_action = 'display:none;';
    if (is_admin == '1') {
        disable_action = '';
    }

    if(std == ''){
        html += `
        <div class="no-data-container">
            <div class="no-data-icon">
                <?= $this->load->view('svg_icons/no_data.svg', [], true); ?>
            </div>
            <div class="no-data-content">
                <h2 class="no-data-title">No Data Available</h2>
                <p class="no-data-description">There is no available data to show.</p>
            </div>
        </div>
        `;
    }
    else {
        html += '<table id="customers2" class="table datatable" style="table-layout: fixed; width: 100%;"><thead><th style="width: 50px; min-width: 50px; max-width: 50px;">#</th><th style="width: 120px; min-width: 120px; max-width: 120px;">Order Id</th><th style="width: 200px; min-width: 200px; max-width: 200px;">Student (Parent)</th><th style="width: 80px; min-width: 80px; max-width: 80px;">Amount</th><th style="width: 150px; min-width: 150px; max-width: 150px;">Date (in GMT)</th><th style="width: 200px; min-width: 200px; max-width: 200px; '+disable_action+'">Split JSON</th><th style="width: 100px; min-width: 100px; max-width: 100px;">Payment by</th><th style="width: 100px; min-width: 100px; max-width: 100px;">Result</th><th style="width: 120px; min-width: 120px; max-width: 120px;">Status</th><th style="width: 150px; min-width: 150px; max-width: 150px;">Transaction Id</th><th style="width: 100px; min-width: 100px; max-width: 100px; '+disable_action+'">Actions</th></thead><tbody>';
        // console.log(std);
        for (i=0;i < std.length; i++) {
            var urlStdhistory = '<?php echo site_url('feesv2/fees_collection/history/') ?>'+std[i].student_id+'/0';
            html += "<tr><td style='width: 50px; min-width: 50px; max-width: 50px; text-align: center;'>" + (i+1) + "</td>";
            html += "<td style='width: 150px; min-width: 150px; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;'>" + std[i].order_id  + "</td>";
            html += "<td style='width: 200px; min-width: 200px; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;'><a target='_blank' href="+urlStdhistory+">" + capitalizeFirstLetter(std[i].student_name) + " (" +capitalizeFirstLetter(std[i].parent_name)+ ")</a></td>";
            html += "<td style='width: 80px; min-width: 80px; max-width: 80px; text-align: right;'>" + std[i].amount  + "</td>";
            // var s = new Date(std[i].init_date_time).toLocaleString("en-US", {timeZone: "Asia/Kolkata"});
            // var s = moment(std[i].init_date_time).tz("Asia/Kolkata").format("DD MM YYYY hh:mm:ss");
            html += "<td style='width: 150px; min-width: 150px; max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;'>" + std[i].init_date_time + "</td>";
            html+= "<td style='width: 200px; min-width: 200px; max-width: 200px; "+disable_action+"'> <div style='display: flex; align-items: center; gap: 5px;'><input type='text' id="+std[i].opm_id+" class='form-control' readonly value="+std[i].split_json+" style='font-size: 10px; flex: 1;'><button onclick='copySplitJson("+std[i].opm_id+")' style='background: none; border: none; padding: 5px; cursor: pointer; color: #623ce7;' title='Copy to clipboard'><svg width='16' height='16' viewBox='0 0 24 24' fill='currentColor'><path d='M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z'/></svg></button></div></td> ";
           
            payment_by = '';
            if(std[i].tx_payment_mode == null){
                payment_by = '';
            }else{
                payment_by = std[i].tx_payment_mode ;
            }
            
            html+= "<td style='width: 100px; min-width: 100px; max-width: 100px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;'>" +payment_by;
            html += "<td style='width: 100px; min-width: 100px; max-width: 100px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;'>" + std[i].response_message + "</td>";
            html += "<td style='width: 120px; min-width: 120px; max-width: 120px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;'>" + std[i].status  + "</td>";
            
            transaction = '';
            if(std[i].tx_id == null){
                transaction = '';
            }else{
                transaction = std[i].tx_id ;
            }
            
            html+= "<td style='width: 150px; min-width: 150px; max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;'>" +transaction;

           
        
        // Create dropdown menu for actions
        var dropdownId = 'dropdown_' + std[i].opm_id;
        var actionsHtml = '<td style="width: 100px; min-width: 100px; max-width: 100px; text-align: center; '+disable_action+'">';
        actionsHtml += '<div class="action-dropdown">';
        actionsHtml += '<button class="action-dropdown-btn" onclick="toggleDropdown(\'' + dropdownId + '\')">';
        actionsHtml += `<?= $this->load->view("svg_icons/more_horiz.svg", [], true); ?>`;
        actionsHtml += '</button>';
        actionsHtml += '<div class="action-dropdown-menu" id="' + dropdownId + '">';
        actionsHtml += '<button class="action-dropdown-item" onclick="submitForm(' + std[i].opm_id + ')">Detail</button>';
        
        if (std[i].response_message != "SUCCESSFUL" || std[i].status != "COMPLETED" || std[i].trans_check == "1") { 
            actionsHtml += '<button class="action-dropdown-item" onclick="submitTxRedriveForm('+ std[i].opm_id + ')" data-toggle="modal" data-target="#summary">Redrive Status</button>';
        }
        
        actionsHtml += '</div>';
        actionsHtml += '</div>';
        actionsHtml += '</td>';
        
        html += actionsHtml;

            
        }


        html += '</tbody></table>';
        
        // Add hidden forms for actions
        html += '<form id="tx_detail_form" action="<?php echo site_url("/payment_controller/online_transaction_detail") ?>" method="post" style="display: none;"><input type="hidden" name="opm_id" id="opm_id"></form>';
        html += '<form id="tx_redrive_form" action="<?php echo site_url("/payment_controller/redrive_transaction") ?>" method="post" style="display: none;"><input type="hidden" name="response" id="response"></form>';
    }
    return html;
}

function capitalizeFirstLetter(str) {
    return str.replace(/\w\S*/g, function(txt){
        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });
}


function copySplitJson(id) { 
    var cls = document.getElementById(id);
    cls.select();
    document.execCommand("copy");
}


function toTimeZone(time, zone) {
    var format = 'YYYY/MM/DD HH:mm:ss ZZ';
    return moment(time, format).tz(zone).format(format);
}

// Function to style search input
function styleSearchInput() {
    // Method 1: Target the new DataTables DOM structure
    let $input = $('.dt-search input[type="search"]');
    if ($input.length) {      
        // Add placeholder
        $input.attr('placeholder', 'Search');
        
        // Style the input
        if ($input.parent().hasClass('search-box')) {
            $input.unwrap();
        }
        $input.siblings('.bi-search').remove();
        $input.addClass('input-search');
        if (!$input.parent().hasClass('search-box')) {
            $input.wrap('<div class="search-box position-relative" style="display: inline-block; margin-right: 1px; margin-bottom: 5px;"></div>');
            $input.parent().prepend('<i class="bi bi-search"></i>');
        }
        return true;
    }
    return false;
}

$(document).ready(function(){
	    $('#datetimepicker1, #init_date').datetimepicker({
	        format: 'DD-MM-YYYY'
	    });
	});

// Dropdown functionality
function toggleDropdown(dropdownId) {
    // Close all other dropdowns
    $('.action-dropdown-menu').removeClass('show');
    
    // Toggle current dropdown
    $('#' + dropdownId).toggleClass('show');
}

// Close dropdown when clicking outside
$(document).click(function(event) {
    if (!$(event.target).closest('.action-dropdown').length) {
        $('.action-dropdown-menu').removeClass('show');
    }
});





</script>


<style type="text/css">

.dataTables_wrapper .dt-buttons {
    float: right;
  }
  .dataTables_filter input {
    background-color: #f2f2f2;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-right: 5px;
  }
  .dataTables_wrapper .dataTables_filter {
    float: right;
    text-align: left;
    width: unset;
  }
</style>

<div id="summary" class="modal fade" role="dialog">
    <div class="modal-dialog">

    <!-- Modal content-->
    <div class="modal-content" style="width: 80%;margin: auto;margin-top: 2%;border-radius: .75rem;">
        <div class="modal-header" style="border-top-left-radius: .75rem; border-top-right-radius: .75rem;">
            <h4 class="modal-title">Online Transaction Details</h4>
            
        </div>
        <div id="numberBody" class="modal-body table-responsive" style="overflow-y:auto;height:400px;">
            <div id="modal-loader" style="display: none; text-align: center;">
                <!-- ajax loader -->
                <img src="<?php echo base_url('assets/img/ajax-loader.gif');?>" style="width:400px; height:400px;">
           </div>
            <table class="table" id="modal-data" width="100%">
                
            </table>

        </div>
        <div class="modal-footer">
            <input type="submit" id="confirmBtn" onclick="updateFunction()" class="btn btn-primary" style="width: 9rem; border-radius: .45rem;" value="Update">
            <button type="button" id="cancelModal" class="getreport_invert" data-dismiss="modal">Cancel</button>
        </div>
    </div>
    </div>
</div>

<?php function Kolkata_datetime($date)
                    {
                        $timezone = new DateTimeZone("Asia/Kolkata" );
                        $date = new DateTime($date);
                        $date->setTimezone($timezone );
                        $dtobj = $date->format('d-m-Y H:i');
                        return $dtobj;
                    }?>