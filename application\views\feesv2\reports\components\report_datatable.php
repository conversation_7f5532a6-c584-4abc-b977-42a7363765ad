<!-- DataTable Section with Latest DataTables -->

<style>
/* Specific targeting for daily transaction report tables */
/* #daily_dataTable_wrapper {
    margin-left: 7px !important;
} */
.dataTables_wrapper .dataTables_paginate {
    margin-top: 1rem; /* adjust as needed */
}

/* Sticky headers for all DataTables */
.dataTables_wrapper table thead th {
    position: sticky !important;
}

/* Sticky headers for scrollable DataTables */
.dataTables_wrapper .dataTables_scrollHead table thead th {
    position: sticky !important;
}

/* Sticky headers for custom scrollable containers */
.custom-scrollable-table table thead th {
    position: sticky !important;
}


/* Vertical scroll implementation without scrollY option */
.dataTables_wrapper {
    position: relative !important;
}

.dataTables_wrapper .dataTables_scrollBody {
    max-height: 450px !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 0.375rem !important;
}

.dataTables_wrapper .dataTables_scrollBody::-webkit-scrollbar {
    width: 8px !important;
}

.dataTables_wrapper .dataTables_scrollBody::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 4px !important;
}

.dataTables_wrapper .dataTables_scrollBody::-webkit-scrollbar-thumb {
    background: #c1c1c1 !important;
    border-radius: 4px !important;
}

.dataTables_wrapper .dataTables_scrollBody::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8 !important;
}

/* Alternative approach for tables without scrollBody */
.dataTables_wrapper table {
    max-height: 400px !important;
    overflow-y: auto !important;
    display: block !important;
}

.dataTables_wrapper table thead,
.dataTables_wrapper table tbody,
.dataTables_wrapper table tr {
    display: table !important;
    width: 100% !important;
    table-layout: fixed !important;
}

.dataTables_wrapper table thead {
    width: calc(100% - 8px) !important;
}

/* Custom scrollable container */
.custom-scrollable-table {
    max-height: 400px !important;
    overflow-y: auto !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 0.375rem !important;
}

.custom-scrollable-table::-webkit-scrollbar {
    width: 8px !important;
}

.custom-scrollable-table::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 4px !important;
}

.custom-scrollable-table::-webkit-scrollbar-thumb {
    background: #c1c1c1 !important;
    border-radius: 4px !important;
}

.custom-scrollable-table::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8 !important;
}

</style>

<!-- <div class="container-fluid">
    <div class="text-center">
        <h3 id="report_title">Daily fee report</h3>
        <h5>From <span id="fromDate"></span> To <span id="toDate"></span></h5>
    </div>
</div> -->


<div class="container-fluid">
    <div id="summary_data">
    </div>
    <div id="transaction_data">
    </div>
    <?php echo no_data_message(); ?>
</div>


<script>
    function summary_datable(summaryid, transaction_id) {
        var summaryTable = $('#' + summaryid).DataTable({
            dom: 'Bfrtip',
            paging: true,
            pageLength: 10,
            pagingType: 'full_numbers',
            searching: false,
            ordering: false,
            info: false,
            language: {
                paginate: {
                    first: "&laquo;",
                    last: "&raquo;",
                    next: "&rsaquo;",
                    previous: "&lsaquo;"
                }
            },
            buttons: [{
                    text: 'Print',
                    className: 'btn btn-info',
                    action: function(e, dt, node, config) {
                        var summaryHtml = $('#' + summaryid).clone();
                        var transactionHtml = $('#' + transaction_id).clone();
                        summaryHtml.removeClass('dataTable');
                        transactionHtml.removeClass('dataTable');
                        var win = window.open('', '', 'height=800,width=1200');
                        win.document.write('<html><head><title>Print</title>');
                        win.document.write('<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">');
                        win.document.write('</head><body>');
                        win.document.write('<h2>Accounts Summary</h2>');
                        win.document.write(summaryHtml.prop('outerHTML'));
                        win.document.write('<h2>Transaction Data</h2>');
                        win.document.write(transactionHtml.prop('outerHTML'));
                        win.document.write('<script>window.onload=function(){window.print();window.close();};<\/script>');
                        win.document.write('</body></html>');
                        win.document.close();
                    }
                },
                {
                    extend: 'excelHtml5',
                    text: 'Excel',
                    className: 'btn btn-success',
                    title: 'Accounts Summary'
                }
            ]
        });
        summaryTable.on('draw.dt', function() {
            customizePagination();
            applyVerticalScroll(summaryid);
        });
    }

    function transaction_table(transaction_id) {
        var table = $('#' + transaction_id).DataTable({
            scrollX: true,
            scrollY: 200,
            stateSave: true,
            autoWidth: false,
            fixedHeader: true,
            ordering: false,
            paging: true,
            pageLength: 10,
            pagingType: 'full_numbers',
            dom: 'Bfrtip',
            language: {
                paginate: {
                    first: "&laquo;",
                    last: "&raquo;",
                    next: "&rsaquo;",
                    previous: "&lsaquo;"
                }
            },
            buttons: [{
                    extend: 'print',
                    className: 'btn btn-info'
                },
                {
                    extend: 'excelHtml5',
                    text: 'Excel',
                    className: 'btn btn-success'
                },
                {
                    extend: 'colvis',
                    className: 'btn btn-warning'
                }
            ]
        });
        table.on('draw.dt', function() {
            customizePagination();
            applyVerticalScroll(transaction_id);
        });
    }

    // Enhanced DataTable Functions for Latest Version

    // Initialize Summary DataTable with enhanced features
    function initializeSummaryDataTable() {
        if ($.fn.DataTable.isDataTable('#summary-table')) {
            $('#summary-table').DataTable().destroy();
        }

        summaryTable = $('#summary-table').DataTable({
            dom: 'Bfrtip',
            paging: true,
            pageLength: 10,
            pagingType: 'full_numbers',
            searching: false,
            ordering: false,
            info: false,
            scrollX: true,
            language: {
                paginate: {
                    first: "&laquo;",
                    last: "&raquo;",
                    next: "&rsaquo;",
                    previous: "&lsaquo;"
                }
            },
            buttons: [{
                    text: '<i class="fa fa-print"></i> Print',
                    className: 'btn btn-info btn-sm',
                    action: function(e, dt, node, config) {
                        printSummaryAndTransaction();
                    }
                },
                {
                    extend: 'excelHtml5',
                    text: '<i class="fa fa-file-excel-o"></i> Excel',
                    className: 'btn btn-success btn-sm',
                    title: 'Summary Report',
                    filename: function() {
                        var from_date = $('#from_date').val();
                        var to_date = $('#to_date').val();
                        return 'Summary_Report_' + from_date + '_to_' + to_date;
                    }
                }
            ]
        });
        summaryTable.on('draw.dt', function() {
            customizePagination();
            applyVerticalScroll('summary-table');
        });
    }
    // Initialize Transaction DataTable with enhanced features
    function initializeTransactionDataTable() {
        if ($.fn.DataTable.isDataTable('#daily_dataTable')) {
            $('#daily_dataTable').DataTable().destroy();
        }

        detailsTable = $('#daily_dataTable').DataTable({
            dom: '<"row align-items-center mb-2" <"col-md-6"l> <"col-md-6 d-flex justify-content-end gap-2"fB> >' +
     'rt' +
     '<"row"<"col-md-6 mt-3"i><"col-md-6 mt-3"p>>',

            // scrollX: true,
            // scrollY: 400,
            scrollCollapse: true,
            stateSave: true,
            autoWidth: false,
            fixedHeader: true,
            ordering: true,
            paging: true,
            pageLength: 10,
            pagingType: 'full_numbers',
            // dom: 'Bfrtip',
            language: {
                search: "",
                searchPlaceholder: "Search",
                lengthMenu: "Show _MENU_ ",
                info: "Showing _START_ to _END_ of _TOTAL_ ",
                paginate: {
                    first: "&laquo;",
                    last: "&raquo;",
                    next: "Next &rsaquo;",
                    previous: "&lsaquo; Previous"
                },
                emptyTable: "No data available in table",
                zeroRecords: "No matching records found"
            },
            columnDefs: [{
                    targets: '_all',
                    className: 'text-center'
                },
                {
                    targets: [-1, -2, -3, -4],
                    className: 'text-right'
                }
            ],
            buttons: [

                {
                    extend: 'colvis',
                    text: `<button class="btn btn-outline-primary" id="expbtns"><?= $this->load->view('svg_icons/column.svg', [], true); ?> Columns</button>`,
                    // className: 'btn btn-warning btn-sm',
                    columns: ':not(.no-export)'
                },
                {
                    extend: 'print',
                    text: `<button class="btn btn-outline-primary" id="expbtns"><?= $this->load->view('svg_icons/print.svg', [], true); ?> Print</button>`,
                    // className: 'btn btn-info btn-sm',
                    exportOptions: {
                        columns: ':visible',
                        // stripHtml: false
                    },
                },
                {
                    extend: 'excelHtml5',
                    text: `<button class="btn btn-outline-primary" id="expbtns"><?= $this->load->view('svg_icons/excel.svg', [], true); ?> Excel</button>`,
                    // className: 'btn btn-success btn-sm',
                    exportOptions: {
                        columns: ':visible'
                    },
                    filename: function() {
                        var from_date = $('#from_date').val();
                        var to_date = $('#to_date').val();
                        return 'Transaction_Report_' + from_date + '_to_' + to_date;
                    },
                    title: function() {
                        var from_date = $('#from_date').val();
                        var to_date = $('#to_date').val();
                        return 'Transaction Report From ' + from_date + ' To ' + to_date;
                    }
                }
            ]
                
            
    });

        // Handle pagination for footer visibility
        // detailsTable.on('page.dt', function() {
        //     var info = detailsTable.page.info();
        //     if (info.page + 1 !== info.pages) {
        //         $('#daily_dataTable tfoot').hide();
        //     } else {
        //         $('#daily_dataTable tfoot').show();
        //     }
        // });
        // detailsTable.on('draw.dt', function() {
        //     customizePagination();
        // });

        setTimeout(() => {
            const $input = $('.dt-search input[type="search"]');
            if ($input.length) {
                if ($input.parent().hasClass('search-box')) {
                    $input.unwrap();
                }
                $input.siblings('.bi-search').remove();
                $input.addClass('input-search');
                $input.wrap('<div class="search-box position-relative"></div>');
                $input.parent().prepend('<i class="bi bi-search"></i>');
                const searchWrapper = $input.closest('.search-box');
                $('.dt-search').empty().append(searchWrapper);
                $input.off('input').on('input', function() {
                    detailsTable.search(this.value).draw();
                });
            }
            
            // Apply vertical scroll
            applyVerticalScroll('daily_dataTable');
        }, 0);

    }

    // Enhanced print function - prints complete data from both summary and transaction tables
    function printSummaryAndTransaction() {
        var from_date = $('#from_date').val();
        var to_date = $('#to_date').val();
        var report_title = $('#report_title').text() || 'Daily Fee Report';

        var win = window.open('', '_blank');
        win.document.write('<html><head><title>' + report_title + '</title>');
        win.document.write('<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">');
        win.document.write('<style>');
        win.document.write('body { font-family: Arial, sans-serif; padding: 20px; }');
        win.document.write('table { width: 100%; border-collapse: collapse; margin: 15px 0; font-size: 12px; }');
        win.document.write('th, td { border: 1px solid #ddd; padding: 6px; text-align: left; }');
        win.document.write('th { background-color: #f2f2f2; font-weight: bold; text-align: center; }');
        win.document.write('.text-right { text-align: right !important; }');
        win.document.write('.text-center { text-align: center !important; }');
        win.document.write('@media print { .no-print { display: none; } }');
        win.document.write('</style>');
        win.document.write('</head><body>');
        win.document.write('<h2 style="text-align: center;">' + report_title + '</h2>');
        win.document.write('<p style="text-align: center;">From ' + from_date + ' To ' + to_date + '</p>');

        // Print Summary Data (extract from transaction_data div)
        var transactionDataHtml = $('#transaction_data').html();
        if (transactionDataHtml && transactionDataHtml.trim() !== '') {
            // Extract summary tables from the transaction data
            var $tempDiv = $('<div>').html(transactionDataHtml);
            var summaryTables = $tempDiv.find('.summary-table');
            
            if (summaryTables.length > 0) {
                win.document.write('<h3>Summary</h3>');
                summaryTables.each(function() {
                    win.document.write($(this).prop('outerHTML'));
                });
            }
        }

        // Print Complete Transaction Data (all data from DataTable)
        if (detailsTable && detailsTable.data().count() > 0) {
            win.document.write('<h3>Complete Transaction Details</h3>');
            
            var allData = detailsTable.data().toArray();
            var columns = detailsTable.columns().header().toArray();
            
            // Create complete table HTML
            win.document.write('<table class="table table-bordered">');
            win.document.write('<thead><tr>');
            columns.forEach(function(header) {
                win.document.write('<th>' + $(header).text() + '</th>');
            });
            win.document.write('</tr></thead><tbody>');
            
            allData.forEach(function(row) {
                win.document.write('<tr>');
                row.forEach(function(cell) {
                    win.document.write('<td>' + (cell || '') + '</td>');
                });
                win.document.write('</tr>');
            });
            win.document.write('</tbody></table>');
        } else {
            // Fallback: use the original table HTML if DataTable not available
            var transactionData = $('#transaction_data').html();
            if (transactionData && transactionData.trim() !== '') {
                win.document.write('<h3>Transaction Details</h3>');
                win.document.write(transactionData);
            }
        }

        win.document.write('<script>window.onload=function(){window.print();window.close();};<\/script>');
        win.document.write('</body></html>');
        win.document.close();
    }

    // Export function - creates CSV data and downloads it
    function exportToExcel_daily() {
        var from_date = $('#from_date').val() || '';
        var to_date = $('#to_date').val() || '';
        var report_title = $('#report_title').text() || 'Daily Fee Report';
        
        var csvContent = '';
        
        // Add summary data
        var transactionDataHtml = $('#transaction_data').html();
        if (transactionDataHtml && transactionDataHtml.trim() !== '') {
            var $tempDiv = $('<div>').html(transactionDataHtml);
            var summaryTables = $tempDiv.find('.summary-table');
            
            if (summaryTables.length > 0) {
                csvContent += 'SUMMARY DATA\n';
                summaryTables.each(function() {
                    var $table = $(this);
                    var $rows = $table.find('tr');
                    $rows.each(function() {
                        var $row = $(this);
                        var cells = [];
                        $row.find('th, td').each(function() {
                            var cellText = $(this).text().trim();
                            // Escape commas and quotes in CSV
                            if (cellText.includes(',') || cellText.includes('"')) {
                                cellText = '"' + cellText.replace(/"/g, '""') + '"';
                            }
                            cells.push(cellText);
                        });
                        csvContent += cells.join(',') + '\n';
                    });
                });
                csvContent += '\nTRANSACTION DATA\n';
            }
        }
        
        // Add transaction data
        if (detailsTable && detailsTable.data().count() > 0) {
            var columns = detailsTable.columns().header().toArray();
            var allData = detailsTable.data().toArray();
            
            // Add headers
            var headerRow = [];
            columns.forEach(function(header) {
                var headerText = $(header).text().trim();
                if (headerText.includes(',') || headerText.includes('"')) {
                    headerText = '"' + headerText.replace(/"/g, '""') + '"';
                }
                headerRow.push(headerText);
            });
            csvContent += headerRow.join(',') + '\n';
            
            // Add data rows
            allData.forEach(function(row) {
                var dataRow = [];
                row.forEach(function(cell) {
                    var cellText = (cell || '').toString();
                    if (cellText.includes(',') || cellText.includes('"')) {
                        cellText = '"' + cellText.replace(/"/g, '""') + '"';
                    }
                    dataRow.push(cellText);
                });
                csvContent += dataRow.join(',') + '\n';
            });
        }
        
        // Create and download file
        var blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        var link = document.createElement('a');
        var url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', 'Complete_Report_' + from_date + '_to_' + to_date + '.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // Print function for backward compatibility
    function printProfile() {
        printSummaryAndTransaction();
    }

    // Function to initialize vertical scroll for DataTables
    function initializeVerticalScroll() {
        // Wrap tables in scrollable containers
        $('.dataTables_wrapper').each(function() {
            var $wrapper = $(this);
            var $table = $wrapper.find('table');
            
            // Check if already wrapped
            if (!$table.parent().hasClass('custom-scrollable-table')) {
                $table.wrap('<div class="custom-scrollable-table"></div>');
            }
        });
    }

    // Function to apply scroll after DataTable initialization
    function applyVerticalScroll(tableId) {
        setTimeout(function() {
            var $wrapper = $('#' + tableId + '_wrapper');
            var $table = $wrapper.find('table');
            
            // Wrap table in scrollable container if not already wrapped
            if (!$table.parent().hasClass('custom-scrollable-table')) {
                $table.wrap('<div class="custom-scrollable-table"></div>');
            }
        }, 100);
    }
</script>