<?php include(APPPATH . 'views/feesv2/reports/components/report_header.php'); ?>

<div class="card-body">
  <div class="row g-3 align-items-end">

    <!-- Search by Student Name -->
    <div class="col-md-3">
      <label class="form-label">Search By Student Name</label>
      <div class="input-group">
        <input id="studentName" autocomplete="off" onkeyup="disableStudentButton()" placeholder="Search by Student Name" class="form-control input-md" name="studentName">
        <button type="button" id="student_name" class="getreport_btn_2" disabled>Get</button>
      </div>
    </div>

    <!-- Search by Date -->
    <div class="col-md-3">
      <label class="form-label">Search By Date</label>
      <div class="input-group">
        <input id="init_date" autocomplete="off" placeholder="Search by Date" class="form-control input-md" name="init_date" value="<?php echo date('d-m-Y'); ?>">
        <span class="input-group-text">
          <?= $this->load->view('svg_icons/calendar_month.svg', [], true); ?>
        </span>
        <button type="button" id="tx_date" class="getreport_btn_2">Get</button>
      </div>
    </div>

    <!-- Search by Transaction Id -->
    <div class="col-md-3">
      <label class="form-label">Search By Transaction Id</label>
      <div class="input-group">
        <input id="transaction_id" autocomplete="off" onkeyup="disableTransactionButton()" placeholder="Search by Transaction Id" class="form-control input-md" name="transaction_id">
        <button type="button" id="tx_id" class="getreport_btn_2" disabled>Get</button>
      </div>
    </div>

  </div>
</div>


    <div class="card-body">
      <div id="loading" class="text-center my-4" style="display: none;">
        <img src="https://i.gifer.com/ZZ5H.gif" alt="Loading" width="50">
      </div>
      <div class="row" style="margin: 0px;display: block;">
        <div id="onlin_refund_table"></div>
      </div>

    </div>

    <!-- Hidden template for no data display -->
    <div id="no-data-template" style="display: none;">
        <div class="no-data-container">
            <div class="no-data-icon">
                <?= $this->load->view('svg_icons/no_data.svg', [], true); ?>
            </div>
            <div class="no-data-content">
                <h2 class="no-data-title">No Data Available</h2>
                <p class="no-data-description">There is no available data to show.</p>
            </div>
        </div>
    </div>
  </div>
</div>

<script type="text/javascript">

    $("#studentName").keydown(function(e) {
        if(e.keyCode == 13) {
            get_refund_report('student_name');
        }
    });

    $("#student_name").click(function (){
        get_refund_report('student_name');
    });

    $("#transaction_id").keydown(function(e) {
        if(e.keyCode == 13) {
            get_refund_report('tx_id');
        }
    });

    $("#tx_id").click(function (){
        get_refund_report('tx_id');
    });

    $("#init_date").keydown(function(e) {
        if(e.keyCode == 13) {
            get_refund_report('tx_date');
        }
    });

    $("#tx_date").click(function (){
        get_refund_report('tx_date');
    });

function get_refund_report(type){
  let getValue = '';
  switch (type) {
    case 'student_name':
      getValue = $('#studentName').val();
      break;
    case 'tx_date':
      getValue = $('#init_date').val();
      break;
    case 'tx_id':
      getValue = $('#transaction_id').val();
      break;
    default:
    getValue = '';
      break;
  }
  get_online_refund_report(type, getValue);

}

function disableTransactionButton(){
  var getButton = document.getElementById('tx_id');
  var inputField = document.getElementById('transaction_id');
  if (inputField.value == ''){
    getButton.disabled = true;
  }else {
    getButton.disabled = false;
  }
}

function disableStudentButton(){
  var getButton = document.getElementById('student_name');
  var inputField = document.getElementById('studentName');
  if (inputField.value == ''){
    getButton.disabled = true;
  }else {
    getButton.disabled = false;
  }
}

function construct_refund_table(resData){
  var html1 = '<table class="table table-bordered" id="refund_data_table">';
  html1 +='<thead>';
  html1 +='<tr>';
  html1 +='<th>#</th>';
  html1 +='<th>Student Details</th>';
  html1 +='<th>Transaction ID</th>';
  html1 +='<th>Transaction Date</th>';
  html1 +='<th>Transaction Amount</th>';
  html1 +='<th>Refund ID</th>';
  html1 +='<th>Refund Date</th>';
  html1 +='<th>Refund Amount</th>';
  html1 +='</tr>';
  html1 +='</thead>';
  html1 +='<tbody>';
  for (var j = 0; j < resData.length; j++) {
    html1 +='<tr>';
    html1 +='<td>'+(j+1)+'</td>';
    html1 +='<td>'+resData[j].student_name+ '('+resData[j].csname+')<br>' +resData[j].parent_name+'</td>';
    html1 +='<td>'+resData[j].transaction_id+'</td>';
    html1 +='<td>'+resData[j].tx_date+'</td>';
    html1 +='<td>'+resData[j].transaction_amount+'</td>';
    html1 +='<td>'+resData[j].refund_id+'</td>';
    html1 +='<td>'+resData[j].date+'</td>';
    html1 +='<td>'+resData[j].refund_amount+'</td>';
    html1 +='</tr>';
  }
  html1 +='</tbody>';
  html1 +='</table>';
  return html1;
}

function get_online_refund_report(type, getValue){
  if(getValue ==''){
    $('#onlin_refund_table').html($('#no-data-template').html());
  }
  // $('#'+type).prop('disabled',true);
  $('#loading').show();
  $('#onlin_refund_table').html('');
  
  $.ajax({
    url:'<?php echo site_url('payment_controller/get_online_refund_transactions') ?>',
    type:'post',
    data : {'type':type,'getValue':getValue},
    success : function(data){
      var resData = JSON.parse(data);
      $('#loading').hide();
      $('#'+type).prop('disabled',false);
      if(resData.length == 0){
        $('#onlin_refund_table').html($('#no-data-template').html());
      }else{
        $('#onlin_refund_table').html(construct_refund_table(resData));
        $('#refund_data_table').DataTable({
          "language": {
          "search": "",
          "searchPlaceholder": "Enter Search..."
          },
          dom: '<"d-flex justify-content-end align-items-center mb-1"<"d-flex align-items-center"fB>>rtip',
          paging: false,
          scrollX: true,
          scrollY: '400px',
          scrollCollapse: true,
          oLanguage: {
              sSearch: ""
          },
          buttons: [
            {
              extend: 'excel',
              text: `<button class="btn btn-outline-primary" id="expbtns"><?= $this->load->view('svg_icons/excel_2.svg', [], true); ?> Excel</button>`,
              filename: 'Online Refund Report',
              exportOptions: {
                  columns: ':visible'
              },
            },
            {
              extend: 'print',
              text: `<button class="btn btn-outline-primary" id="expbtns" style="margin-right:-7px;"><?= $this->load->view('svg_icons/print.svg', [], true); ?> Print</button>`,
              filename: 'Online Refund Report',
              exportOptions: {
                  columns: ':visible'
              },
            }
          ]
        });
        
        // Style the search input to match the button design
        styleSearchInput();

      }
    }
  });
}

// Function to style search input
function styleSearchInput() {
    // Method 1: Target the new DataTables DOM structure
    let $input = $('.dt-search input[type="search"]');
    if ($input.length) {      
        // Add placeholder
        $input.attr('placeholder', 'Search');
        
        // Style the input
        if ($input.parent().hasClass('search-box')) {
            $input.unwrap();
        }
        $input.siblings('.bi-search').remove();
        $input.addClass('input-search');
        if (!$input.parent().hasClass('search-box')) {
            $input.wrap('<div class="search-box position-relative" style="display: inline-block; margin-right: 1px; margin-bottom: 5px;"></div>');
            $input.parent().prepend('<i class="bi bi-search"></i>');
        }
        return true;
    }
    return false;
}

$(document).ready(function(){
    $('#datetimepicker1, #init_date').datetimepicker({
        format: 'DD-MM-YYYY'
    });
});

</script>

<style type="text/css">
.loaderclass {
  border: 8px solid #eee;
  border-top: 8px solid #7193be;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  position: fixed;
  z-index: 1;
  animation: spin 2s linear infinite;
  margin-top: 50%;
  margin-left: 40%;
  position: absolute;
  z-index: 99999;
}
.widthadjust{
    width: 32%;
    margin: auto;
}

/* Custom styles for date picker width and positioning */
#datetimepicker1 {
    max-width: 250px !important;
    width: 250px !important;
    position: relative !important;
    z-index: 1000 !important;
}

#datetimepicker1 .form-control {
    width: 180px !important;
}

/* Ensure the input group doesn't clip the calendar */
.input-group.date {
    position: relative !important;
    z-index: 1000 !important;
}

/* Fix for Bootstrap grid overflow */
.col-md-4.col-md-offset-3 {
    position: relative !important;
    overflow: visible !important;
}

/* Ensure the row container allows overflow */
.row {
    overflow: visible !important;
    position: relative !important;
}

/* Calendar dropdown positioning and containment */
.bootstrap-datetimepicker-widget.dropdown-menu {
    z-index: 99999 !important;
    border: 1px solid #e5e7eb !important;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: auto !important;
    max-width: 260px !important;
    width: 260px !important;
    margin-top: 2px !important;
}

/* Ensure calendar stays within viewport */
.bootstrap-datetimepicker-widget {
    position: relative !important;
    overflow: visible !important;
    z-index: 99999 !important;
}

/* Container positioning to prevent overflow */
#datetimepicker1 {
    position: relative !important;
    overflow: visible !important;
    z-index: 99999 !important;
}

/* Ensure calendar appears above DataTable headers */
.bootstrap-datetimepicker-widget,
.bootstrap-datetimepicker-widget * {
    z-index: 99999 !important;
}

/* Adjust calendar position if it would overflow right */
.bootstrap-datetimepicker-widget.dropdown-menu.pull-right {
    left: auto !important;
    right: 0 !important;
}

/* Ensure calendar doesn't overflow container */
.form-group {
    overflow: visible !important;
    position: relative !important;
}

/* Data table horizontal scroll */
#onlin_refund_table {
    overflow-x: auto !important;
    width: 100% !important;
}

#onlin_refund_table .table {
    min-width: 100% !important;
    width: max-content !important;
}

#onlin_refund_table table {
    min-width: 100% !important;
    width: max-content !important;
}

/* Remove all border radius and shadows from calendar elements */
.bootstrap-datetimepicker-widget,
.bootstrap-datetimepicker-widget *,
.bootstrap-datetimepicker-widget table,
.bootstrap-datetimepicker-widget table *,
.bootstrap-datetimepicker-widget .dropdown-menu,
.bootstrap-datetimepicker-widget .dropdown-menu * {
    border-radius: 0 !important;
    -webkit-border-radius: 0 !important;
    -moz-border-radius: 0 !important;
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
}

/* Specifically target calendar header and all its elements */
.bootstrap-datetimepicker-widget table thead,
.bootstrap-datetimepicker-widget table thead th,
.bootstrap-datetimepicker-widget table thead tr,
.bootstrap-datetimepicker-widget table thead td {
    border-radius: 0 !important;
    -webkit-border-radius: 0 !important;
    -moz-border-radius: 0 !important;
}

/* Style for dates not in current month (old and new dates) */
.bootstrap-datetimepicker-widget td.old,
.bootstrap-datetimepicker-widget td.new {
    color: #D1D5DB !important;
    background-color: #F9FAFB !important;
    opacity: 0.6 !important;
    pointer-events: none !important;
    cursor: not-allowed !important;
}

.bootstrap-datetimepicker-widget td.old:hover,
.bootstrap-datetimepicker-widget td.new:hover {
    color: #D1D5DB !important;
    background-color: #F9FAFB !important;
    opacity: 0.6 !important;
    pointer-events: none !important;
    cursor: not-allowed !important;
}

/* Ensure off dates are also non-accessible */
.bootstrap-datetimepicker-widget td.off {
    color: #D1D5DB !important;
    background-color: #F9FAFB !important;
    opacity: 0.6 !important;
    pointer-events: none !important;
    cursor: not-allowed !important;
}

.bootstrap-datetimepicker-widget td.off:hover {
    color: #D1D5DB !important;
    background-color: #F9FAFB !important;
    opacity: 0.6 !important;
    pointer-events: none !important;
    cursor: not-allowed !important;
}

.getreport_btn {
    width: 100%;           /* take full width of card */
    max-width: 220px;      /* optional: limit max size */
    padding: 10px 16px;    /* adjust padding to fit */
    box-sizing: border-box; /* include padding inside width */
    margin: 0 auto;        /* center inside card */
    display: block;        /* ensures proper alignment */
}

.no-data-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    background-color: #f8f8fc;
    border-radius: 12px;
    margin: 20px 0;
    min-height: 400px;
}

.no-data-icon {
    margin-bottom: 30px;
}

.no-data-icon svg {
    width: 180px;
    height: 180px;
}

.no-data-content {
    text-align: center;
    max-width: 400px;
}

.no-data-title {
    font-size: 24px;
    font-weight: 600;
    color: #333333;
    margin: 0 0 16px 0;
    line-height: 1.3;
}

.no-data-description {
    font-size: 16px;
    line-height: 1.5;
    font-weight: 400;
}

.no-data-description:last-child {
    margin-bottom: 0;
}

.dataTables_wrapper .dt-buttons {
    float: right;
  }
  .dataTables_filter input {
    background-color: #f2f2f2;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-right: 5px;
  }
  .dataTables_wrapper .dataTables_filter {
    float: right;
    text-align: left;
    width: unset;
  }

/* Custom Vertical Scrollbar Styling - Matching Other Files */
#onlin_refund_table::-webkit-scrollbar,
#refund_data_table::-webkit-scrollbar,
.dataTables_scrollBody::-webkit-scrollbar,
.dataTables_wrapper::-webkit-scrollbar {
    width: 8px !important;
    height: 8px !important;
    border-radius: 4px !important;
    background: #f1f1f1 !important;
}

#onlin_refund_table::-webkit-scrollbar-thumb,
#refund_data_table::-webkit-scrollbar-thumb,
.dataTables_scrollBody::-webkit-scrollbar-thumb,
.dataTables_wrapper::-webkit-scrollbar-thumb {
    background: #A09EAE !important;
    border-radius: 4px !important;
    transition: background 0.3s ease !important;
}

#onlin_refund_table::-webkit-scrollbar-track,
#refund_data_table::-webkit-scrollbar-track,
.dataTables_scrollBody::-webkit-scrollbar-track,
.dataTables_wrapper::-webkit-scrollbar-track {
    background: #f1f1f1 !important;
    border-radius: 4px !important;
}

#onlin_refund_table::-webkit-scrollbar-thumb:hover,
#refund_data_table::-webkit-scrollbar-thumb:hover,
.dataTables_scrollBody::-webkit-scrollbar-thumb:hover,
.dataTables_wrapper::-webkit-scrollbar-thumb:hover {
    background: #8B8A9A !important;
}

/* Firefox scrollbar support */
#onlin_refund_table,
#refund_data_table,
.dataTables_scrollBody,
.dataTables_wrapper {
    scrollbar-width: thin !important;
    scrollbar-color: #A09EAE #f1f1f1 !important;
}
</style>