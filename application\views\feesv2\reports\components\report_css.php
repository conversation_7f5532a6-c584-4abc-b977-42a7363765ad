<style>
    @import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap");
    
    /* Global Inter font application - excluding icon elements */
    *:not(.glyphicon):not(.fa):not(.icon):not([class*="glyphicon"]):not([class*="fa-"]):not([class*="icon-"]):not(.bi):not([class*="bi-"]) {
        font-family: 'Inter', sans-serif !important;
    }

    .report-header-icon,
    .report-header-title {
        color: #161327;
        font-family: Inter, sans-serif;
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 120%;
    }

    #report_title {
        padding-top: 10px;
    }
    .custom-dates {
        padding-bottom: 10px;
    }

    .progress-bar {
        background-color: #623CE7 !important;
        border-radius: 2px !important;
    }

    .progress {
        border-radius: 2px !important;
    }

    .grow-select {
        border: 1px solid #dcdcdc;
        background-color: #ffffff !important;
        color: #6b6b6b;
        appearance: none;
        background: url('https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/icons/chevron-down.svg') no-repeat center center/contain;
        background-repeat: no-repeat;
        background-position: right 10px center;
        background-size: 12px;
        cursor: pointer;
        max-width: 270px;
        border-radius: 4px;
        z-index: 1 !important;
    }
    
    /* Ensure saved reports dropdown stays behind modals */
    #filter_types.grow-select,
    select.grow-select {
        z-index: 1 !important;
        position: relative !important;
    }

    .arrow-mark {
        appearance: none;
        border: 1px solid #dcdcdc;
        background-color: #ffffff !important;
        background: url('https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/icons/chevron-down.svg') no-repeat center center/contain;
        background-repeat: no-repeat;
        background-position: right 10px center;
        background-size: 12px;
    }

    #clear {
        background: transparent;
        color: #623CE7;
        border-radius: 5px;
        padding: 0.75rem 1.5rem;
        font-weight: bold;
        font-size: 15px;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 82px;
        height: 40px !important;
        border: 0px;
    }

    #clear:focus,
    #clear:active {
        box-shadow: 0 0 0 3px rgba(98, 60, 231, 0.3);
    }

    #clear:disabled {
        background: transparent;
        color: #A9A6A9;
        cursor: not-allowed;
    }

    .export_btn {
        all: unset !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 0.75em 1.5em !important;
        border: 2px solid #623CE7 !important;
        color: #623CE7 !important;
        background-color: transparent !important;
        font-size: 1rem !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
        gap: 0.5em !important;
        border-radius: 4px !important;
    }

    #expbtns {
        all: unset;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75em 1.5em;
        border: 2px solid #623CE7;
        color: #623CE7;
        background-color: transparent;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;
        gap: 0.5em;
        border-radius: 4px;
    }

    #expbtns:hover {
        background-color: #EFECFD;
    }

    #expbtns:focus {
        outline: none;
        color: #623CE7;
        border: 2px solid #CEC3F8;
    }

    #expbtns:active {
        background-color: #CEC3F8;
        border: 2px solid #623CE7;
        color: #623CE7;
    }

    #expbtns:disabled {
        border-color: #A9A6A9;
        background-color: #DEDCDF;
        color: #c0c0c0;
        cursor: not-allowed;
    }

    #expbtns:disabled::after {
        color: #c0c0c0;
    }

    #expbtns2 {
        all: unset;
        background: #623CE7;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        font-weight: bold;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5em;
        border: 2px solid #623CE7;
    }

    #search {
        background: #623CE7;
        color: white;
        border: none;
        border-radius: 5px;
        padding: 0.75rem 1.5rem;
        font-weight: bold;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 120px;
        height: 40px !important;
    }

    .getreport_btn{
        background: #623CE7;
        color: white;
        border: none;
        border-radius: 5px;
        padding: 0.75rem 1.5rem;
        font-weight: bold;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 120px;
        height: 40px !important;
    }
  
  .getreport_btn:hover {
    background: #462BA4 !important;
  }

  .getreport_btn_2{
        background: #623CE7 !important;
        color: white !important;
        border: none !important;
        border-radius: 5px !important;
        padding: 0.75rem 1.5rem !important;
        font-weight: bold !important;
        font-size: 1rem !important;
        cursor: pointer !important;
        transition: all 0.2s ease-in-out !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 20% !important;
        height: 40px !important;
    }
  
  .getreport_btn_2:hover {
    background: #462BA4 !important;
  }

  .getreport_invert {
    all: unset ;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0.8rem 1.4rem !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    color: #623CE7 !important;              /* Purple text */
    background-color: #fff ;      /* White background */
    border: 2px solid #623CE7 !important;   /* Purple border */
    border-radius: 6px !important;          /* Rounded corners */
    cursor: pointer !important;
    transition: all 0.2s ease-in-out !important;
    text-decoration: none !important;  /* Remove underline */
  }

    /* Responsive button styles for all devices */
    .btn-responsive {
        min-width: 80px;
        height: 40px !important;
        font-size: 14px;
        padding: 8px 16px !important;
        white-space: nowrap;
        text-align: center;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    /* Mobile devices (up to 576px) */
    @media (max-width: 575.98px) {
        .btn-responsive {
            min-width: 70px;
            height: 36px !important;
            font-size: 13px;
            padding: 6px 12px !important;
        }
        
        #search {
            min-width: 100px;
            height: 36px !important;
            font-size: 13px;
            padding: 6px 16px !important;
        }
        
        #clear {
            min-width: 70px;
            height: 36px !important;
            font-size: 13px;
            padding: 6px 12px !important;
        }
        
        /* Stack buttons vertically on very small screens */
        .d-flex.flex-wrap.align-items-center.gap-2.gap-md-3 {
            flex-direction: column;
            gap: 8px !important;
            width: 100%;
        }
        
        .d-flex.flex-wrap.align-items-center.gap-2.gap-md-3 .btn-responsive {
            width: 100%;
            max-width: 200px;
        }
    }

    /* Small devices (576px and up) */
    @media (min-width: 576px) and (max-width: 767.98px) {
        .btn-responsive {
            min-width: 75px;
            height: 38px !important;
            font-size: 13px;
            padding: 7px 14px !important;
        }
        
        #search {
            min-width: 110px;
            height: 38px !important;
        }
    }

    /* Medium devices (768px and up) */
    @media (min-width: 768px) and (max-width: 991.98px) {
        .btn-responsive {
            min-width: 80px;
            height: 40px !important;
            font-size: 14px;
            padding: 8px 16px !important;
        }
        
        #search {
            min-width: 120px;
            height: 40px !important;
        }
    }

    /* Large devices (992px and up) */
    @media (min-width: 992px) {
        .btn-responsive {
            min-width: 80px;
            height: 40px !important;
            font-size: 14px;
            padding: 8px 16px !important;
        }
        
        #search {
            min-width: 120px;
            height: 40px !important;
        }
    }

    #search:hover,
    #expbtns2:hover {
        background: #462BA4 !important;
    }

    #search:focus,
    #expbtns2:focus {
        background: #623CE7;
        outline: 3px solid #B7A5F4;
        outline-offset: 2px;
    }

    #search:active,
    #expbtns2:active {
        background: #36217F;
    }

    #search:disabled,
    #expbtns2:disabled {
        background: #DEDCDF !important;
        color: #A9A6A9 !important;
        cursor: not-allowed;
        border: 2px solid #a9a6a9 !important;
    }

    .search-box {
        display: flex;
        align-items: center;
        width: 260px;
        height: 40px;
        gap: 8px;
        border-radius: 4px;
        border: 1px solid #DEDCDF;
        background: #F9FAFB;
        padding: 0 14px 0 38px;
        position: relative;
        margin-right: 18px;
        margin-top: 6px;
    }

    .search-box:focus-within {
        border-color: #623CE7;
    }

    .search-box .bi-search {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 20px;
        color: #d1d1e0;
        pointer-events: none;
        transition: color 0.2s;
    }

    .search-box:focus-within .bi-search {
        color: #623CE7;
    }

    /* Calender Btns */
    .btn-small.btn-primary {
        background: #623CE7 !important;
        border-color: #623CE7 !important;
        color: #fff !important;
    }

    .cancelBtn.btn-small {
        color: #623CE7 !important;
        background: transparent !important;
        border: none !important;
    }

    .input-search {
        font-family: "Inter", sans-serif !important;
        flex: 1;
        border: none;
        background: transparent;
        font-size: 15px;
        color: #161327;
        outline: none;
        line-height: 1.5;
        padding: 0;
    }

    .input-search::placeholder {
        color: #b3b3c6;
        font-size: 14px;
        font-weight: 400;
    }

    /* Container style */
    .dataTables_filter.custom-search-box {
        float: right;
        margin-bottom: 5px;
        margin-right: -17px;
    }

    /* Search box */
    .dataTables_filter .search-box {
        display: flex;
        align-items: center;
        width: 260px;
        height: 40px;
        gap: 8px;
        border-radius: 4px;
        border: 1px solid #DEDCDF;
        background: #F9FAFB;
        box-shadow: 0 2px 8px rgba(16, 24, 40, 0.07);
        padding: 0 14px 0 38px;
        position: relative;
        transition: box-shadow 0.2s, border-color 0.2s;
    }

    .dataTables_filter .search-box:focus-within {
        box-shadow: 0 4px 16px rgba(98, 60, 231, 0.10);
        border-color: #623CE7;
    }

    /* Icon inside input */
    .dataTables_filter .search-box .bi-search {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 20px;
        color: #d1d1e0;
        pointer-events: none;
        transition: color 0.2s;
    }

    /* Change icon color on focus */
    .dataTables_filter .search-box:focus-within .bi-search {
        color: #623CE7;
    }

    /* Input field */
    .dataTables_filter .search-box input {
        flex: 1;
        border: none;
        background: transparent;
        font-size: 15px;
        color: #161327;
        outline: none;
        line-height: 1.5;
        padding: 0;
        font-family: "Inter", sans-serif !important;
    }

    .dataTables_filter .search-box input::placeholder {
        color: #b3b3c6;
        font-size: 14px;
        font-weight: 400;
    }


    table {
        font-family: "Inter", sans-serif !important;
    }

    table {
        font-family: "Inter", sans-serif !important;
        width: 100% !important;
        border-collapse: collapse !important;
        background-color: #ffffff !important;
        /* border-radius: 1.5rem !important; */
        opacity: 1 !important;
        transition: none !important;
    }

    table thead th {
        /* position: sticky !important; */
        top: 0 !important;
        background-color: #EFECFD !important;
        color: #161327 !important;
        font-size: 12px !important;
        font-weight: 600 !important;
        z-index: 9999 !important;
        text-align: center !important;
        padding: 16px 20px 16px 20px !important;
        border-bottom: 1px solid #e5e7eb !important;
        border-top: none !important;
    }

    table>thead>tr>th {
        background: #EFECFD;
        color: #161327;
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        text-align: center !important;
        vertical-align: middle !important;
    }

    table th,
    table td {
        padding: 10px 14px !important;
        border-bottom: 1px solid #e5e7eb !important;
        font-size: 11px !important;
        font-weight: 400 !important;
        background: none !important;
        min-width: 150px !important;
        white-space: nowrap !important;
        text-align: center !important;
        vertical-align: middle !important;
    }

    /* Ensure table columns have adequate width */
    table {
        table-layout: auto !important;
        width: 100% !important;
        display: table !important;
    }

    /* Ensure DataTable wrapper is properly displayed */
    .dataTables_wrapper {
        display: block !important;
        width: 100% !important;
    }

    /* Ensure table body is visible */
    .dataTables_scrollBody {
        display: block !important;
        width: 100% !important;
    }

    /* Specific column width adjustments */
    table th:first-child,
    table td:first-child {
        min-width: 80px !important; /* Serial number column */
    }

    table th:nth-child(2),
    table td:nth-child(2) {
        min-width: 180px !important; /* Student name column */
    }

    table th:nth-child(3),
    table td:nth-child(3) {
        min-width: 140px !important; /* Class column */
    }

    /* Additional column width adjustments for common columns */
    table th:nth-child(4),
    table td:nth-child(4) {
        min-width: 120px !important; /* Section column */
    }

    table th:nth-child(5),
    table td:nth-child(5) {
        min-width: 130px !important; /* Admission number column */
    }

    table tbody tr:nth-child(even) {
        background-color: #FBFBFB !important;
    }

    table tbody tr:hover {
        background-color: #F4F4F5 !important;
    }

    table tfoot tr {
        /* background-color: #f3f4f6 !important; */
        font-weight: 500 !important;
    }

    .table-bordered>tbody>tr>td,
    .table-bordered>thead>tr>th {
        border: 0px !important;
    }

    .panel_heading_new_style_staff_border>.row {
        border-bottom: 0px !important;
        padding: 5px 0px 5px;
    }

    .custom-checkbox-label {
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        font-family: 'Inter', sans-serif;
    }

    .custom-checkbox {
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        width: 16px;
        height: 16px;
        border-radius: 4px;
        border: 2px solid #B0B1B1;
        background-color: white;
        position: relative;
        cursor: pointer;
        flex-shrink: 0;
        aspect-ratio: 1/1;
    }

    .custom-checkbox:checked {
        background-color: #623CE7;
        border-color: #623CE7;
    }

    .custom-checkbox:checked::after {
        content: '';
        position: absolute;
        top: 35%;
        left: 50%;
        width: 6px;
        height: 10px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: translate(-50%, -50%) rotate(45deg);
    }

    .custom-checkbox-label span {
        color: #161327;
        font-size: 15px;
        font-weight: 500;
        line-height: normal;
    }

    .custom-radio-label {
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
        font-family: 'Inter', sans-serif;
    }

    .custom-radio {
        /* Use default radio button appearance with custom color */
        width: 16px;
        height: 16px;
        cursor: pointer;
        flex-shrink: 0;
        accent-color: #623CE7;
    }

    .custom-radio-label span {
        color: #161327;
        font-size: 15px;
        font-weight: 500;
        line-height: normal;
    }


    .form-control {
        height: 40px;
    }

    .select_report {
        width: 300px !important;
    }

    .dropdown-toggle.selectpicker {
        height: 40px !important;
    }

    .bootstrap-select>.dropdown-toggle::after,
    .dropdown-toggle::after {
        content: "";
        display: inline-block !important;
        border: none !important;
        background: none !important;
    }

    .bootstrap-select.btn-group .btn .caret {
        display: none !important;
    }

    .bootstrap-select>.dropdown-toggle::after {
        content: "";
        display: inline-block;
        position: fixed;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        width: 1em;
        height: 1em;
        background: none;
    }

    .bootstrap-select>.dropdown-toggle {
        position: relative;
        background: none !important;
    }

    .bootstrap-select>.dropdown-toggle::before {
        content: "";
        display: inline-block !important;
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        width: 1em;
        height: 1em;
        z-index: 2;
        background-size: 12px;
        background: url('https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/icons/chevron-down.svg') no-repeat center center/contain;
    }

    .fee-type-error .bootstrap-select>.dropdown-toggle::before {
        background: url('https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/icons/chevron-down.svg') no-repeat center center/contain;
        filter: invert(23%) sepia(97%) saturate(7492%) hue-rotate(353deg) brightness(97%) contrast(108%);
    }

    .dataTables_length,
    .dataTables_filter {
        border-bottom: 0px !important;
    }

    #filtersContainer {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        transform-origin: top;
        overflow: visible;
        will-change: transform, opacity, max-height;
        position: relative;
        z-index: 1;
    }

    #filtersContainer.hiding {
        opacity: 0;
        transform: scaleY(0);
        max-height: 0 !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        margin-top: 0 !important;
        margin-bottom: 0 !important;
        border: none !important;
        overflow: hidden;
    }

    #filterToggleIcon {
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    #filterToggleIcon.rotated {
        transform: rotate(-90deg);
    }

    .card-body {
        transition: height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    #filtersContainer .bootstrap-select .dropdown-menu {
        z-index: 9999 !important;
    }

    #filtersContainer .bootstrap-select.open {
        z-index: 9990 !important;
    }

    /* Ensure dropdowns appear above table headers */
    .bootstrap-select .dropdown-menu {
        z-index: 9990 !important;
    }

    .dropdown-menu {
        z-index: 9990 !important;
    }

    /* Ensure select dropdowns are above table content */
    select.form-control {
        z-index: 9990 !important;
    }


    .bootstrap-select>.dropdown-toggle {
        z-index: 9990 !important;
    }

    #branches {
        height: 30px;
    }

    .modal-backdrop.fade {
        z-index: -1;
    }

    /* === Date Range Picker Calendar: Styled Same as tables === */

/* Ensure dropdowns appear above table headers */
#filtersContainer .dropdown-menu {
    z-index: 9999 !important;
}

    /* === Date Range Picker Calendar: Override General Table Styles === */

    /* Override general table styles for bootstrap-datetimepicker-widget */
    .bootstrap-datetimepicker-widget table {
        font-family: "Inter", sans-serif !important;
        width: auto !important; /* Override 100% width */
        border-collapse: collapse !important;
        background-color: #ffffff !important;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06) !important;
        font-size: 12px !important;
        table-layout: fixed !important; /* Override auto layout */
        display: table !important;
    }

    .bootstrap-datetimepicker-widget th,
    .bootstrap-datetimepicker-widget td {
        padding: 8px 10px !important;
        font-size: 11px !important;
        font-weight: 400 !important;
        color: #161327 !important;
        text-align: center !important; /* Override left alignment */
        border: none !important;
        background: none !important;
        border-radius: 0px;
        min-width: auto !important; /* Override 150px min-width */
        width: auto !important; /* Override any width constraints */
        white-space: nowrap !important;
    }

    .bootstrap-datetimepicker-widget thead th {
        background-color: #EFECFD !important;
        color: #161327 !important;
        font-weight: 600 !important;
        text-align: center !important; /* Override left alignment */
        padding: 8px 10px !important; /* Override large padding */
    }

    .bootstrap-datetimepicker-widget td.available:hover,
    .bootstrap-datetimepicker-widget th.available:hover {
        background-color: #F4F4F5 !important;
        cursor: pointer;
    }

    .bootstrap-datetimepicker-widget td.active,
    .bootstrap-datetimepicker-widget td.active:hover {
        background-color: #6D28D9 !important;
        color: white !important;
        font-weight: 600 !important;
    }

    .bootstrap-datetimepicker-widget td.in-range {
        background-color: #EFECFD !important;
        color: #161327 !important;
    }

    .bootstrap-datetimepicker-widget td.start-date,
    .bootstrap-datetimepicker-widget td.end-date {
        background-color: #7C3AED !important;
        color: white !important;
        font-weight: 600 !important;
    }

    .bootstrap-datetimepicker-widget td.off {
        color: #9CA3AF !important;
        pointer-events: none;
        opacity: 0.5;
    }

    /* Additional overrides to prevent table inheritance */
    .bootstrap-datetimepicker-widget table th:first-child,
    .bootstrap-datetimepicker-widget table td:first-child,
    .bootstrap-datetimepicker-widget table th:nth-child(2),
    .bootstrap-datetimepicker-widget table td:nth-child(2),
    .bootstrap-datetimepicker-widget table th:nth-child(3),
    .bootstrap-datetimepicker-widget table td:nth-child(3),
    .bootstrap-datetimepicker-widget table th:nth-child(4),
    .bootstrap-datetimepicker-widget table td:nth-child(4),
    .bootstrap-datetimepicker-widget table th:nth-child(5),
    .bootstrap-datetimepicker-widget table td:nth-child(5),
    .bootstrap-datetimepicker-widget table th:nth-child(6),
    .bootstrap-datetimepicker-widget table td:nth-child(6),
    .bootstrap-datetimepicker-widget table th:nth-child(7),
    .bootstrap-datetimepicker-widget table td:nth-child(7) {
        min-width: auto !important; /* Override all column width constraints */
        width: auto !important;
    }

    /* Override general table styles for daterangepicker */
    .daterangepicker table {
        font-family: "Inter", sans-serif !important;
        width: auto !important; /* Override 100% width */
        border-collapse: collapse !important;
        background-color: #ffffff !important;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06) !important;
        font-size: 12px !important;
        table-layout: fixed !important; /* Override auto layout */
        display: table !important;
    }

    .daterangepicker th,
    .daterangepicker td {
        padding: 8px 10px !important;
        font-size: 11px !important;
        font-weight: 400 !important;
        color: #161327 !important;
        text-align: center !important; /* Override left alignment */
        border: none !important;
        background: none !important;
        border-radius: 0px;
        min-width: auto !important; /* Override 150px min-width */
        width: auto !important; /* Override any width constraints */
        white-space: nowrap !important;
    }

    .daterangepicker thead th {
        background-color: #EFECFD !important;
        color: #161327 !important;
        font-weight: 600 !important;
        text-align: center !important; /* Override left alignment */
        padding: 8px 10px !important; /* Override large padding */
    }

    .daterangepicker td.available:hover,
    .daterangepicker th.available:hover {
        background-color: #F4F4F5 !important;
        cursor: pointer;
    }

    .daterangepicker td.active,
    .daterangepicker td.active:hover {
        background-color: #6D28D9 !important;
        color: white !important;
        font-weight: 600 !important;
    }

    .daterangepicker td.in-range {
        background-color: #EFECFD !important;
        color: #161327 !important;
    }

    .daterangepicker td.start-date,
    .daterangepicker td.end-date {
        background-color: #7C3AED !important;
        color: white !important;
        font-weight: 600 !important;
    }

    .daterangepicker td.off {
        color: #9CA3AF !important;
        pointer-events: none;
        opacity: 0.5;
    }

    .daterangepicker .ranges li.active,
    .daterangepicker .ranges li:hover {
        background: #623ce7;
        border: 1px solid #623ce7;
        color: #fff;
    }

    /* Additional overrides to prevent table inheritance */
    .daterangepicker table th:first-child,
    .daterangepicker table td:first-child,
    .daterangepicker table th:nth-child(2),
    .daterangepicker table td:nth-child(2),
    .daterangepicker table th:nth-child(3),
    .daterangepicker table td:nth-child(3),
    .daterangepicker table th:nth-child(4),
    .daterangepicker table td:nth-child(4),
    .daterangepicker table th:nth-child(5),
    .daterangepicker table td:nth-child(5) {
        min-width: auto !important; /* Override all column width constraints */
        width: auto !important;
    }

    .form-select {
        height: 40px;
    }

    .buttons-print,
    .buttons-excel,
    .buttons-colvis {
        background-color: #f9f7fe !important;
        border: 0px #f9f7fe !important;
    }

    /* Make DataTable button text bold globally */
    .dt-buttons .btn,
    .dt-buttons button,
    .buttons-print,
    .buttons-excel,
    .buttons-colvis,
    #expbtns {
        font-weight: bold !important;
    }

    /* Center-align checkboxes in balance_sms_v2.php */
    #fee_summary_data_wrapper table td:first-child,
    #fee_summary_data_summary_wrapper table td:first-child {
        text-align: center !important;
    }
    
    .dt-search-1 {
        all: unset;

    }

    /* Custom Checkbox Styles for DataTable Column Visibility */
    .dt-button-collection .dt-button {
        display: flex !important;
        align-items: center !important;
        cursor: pointer !important;
        font-family: 'Inter', sans-serif !important;
        padding: 8px 12px !important;
        border: none !important;
        background: none !important;
        text-align: left !important;
        width: 100% !important;
        position: relative !important;
    }

    .dt-button-collection .dt-button:hover {
        background-color: #f8f9fa !important;
    }

    /* Hide any default input checkboxes if they exist */
    .dt-button-collection .dt-button input[type="checkbox"] {
        display: none !important;
    }

    .dt-button-collection .dt-button input[type="checkbox"]:checked {
        background-color: #623CE7 !important;
        border-color: #623CE7 !important;
    }

    .dt-button-collection .dt-button input[type="checkbox"]:checked::after {
        content: '' !important;
        position: absolute !important;
        top: 16% !important;
        left: 80% !important;
        width: 4px !important;
        height: 8px !important;
        border: solid white !important;
        border-width: 0 2px 2px 0 !important;
        transform: translate(-50%, -50%) rotate(45deg) !important;
    }

    .dt-button-collection .dt-button-active:before {
        content: '' !important;
        display: inline-block !important;
        width: 12px !important;
        height: 12px !important;
        border-radius: 2px !important;
        border: 2px solid #623CE7 !important;
        background-color: #623CE7 !important;
        position: relative !important;
        margin-right: 10px !important;
        flex-shrink: 0 !important;
        vertical-align: middle !important;
    }

    .dt-button-collection .dt-button-active:after {
        content: '' !important;
        position: absolute !important;
        left: 16px !important;
        top: 80% !important;
        width: 4px !important;
        height: 8px !important;
        border: solid white !important;
        border-width: 0 2px 2px 0 !important;
        transform: translateY(-50%) rotate(45deg) !important;
        z-index: 1 !important;
    }

    /* Style inactive column buttons */
    .dt-button-collection .dt-button:not(.dt-button-active):before {
        content: '' !important;
        display: inline-block !important;
        width: 12px !important;
        height: 12px !important;
        border-radius: 2px !important;
        border: 2px solid #B0B1B1 !important;
        background-color: white !important;
        position: relative !important;
        margin-right: 10px !important;
        flex-shrink: 0 !important;
        vertical-align: middle !important;
    }

    .dt-button-collection .dt-button span {
        color: #161327 !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        line-height: normal !important;
    }

    .dt-button-collection {
        border-radius: 8px !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        border: 1px solid #e9ecef !important;
        padding: 8px 0 !important;
        min-width: 200px !important;
        max-height: 300px !important;
        overflow-y: auto !important;
        z-index: 99999 !important;
    }

    /* Unified grey scrollbar for column dropdown */
    .dt-button-collection::-webkit-scrollbar {
        width: 6px !important;
    }

    .dt-button-collection::-webkit-scrollbar-track {
        background: #f1f1f1 !important;
        border-radius: 3px !important;
    }

    .dt-button-collection::-webkit-scrollbar-thumb {
        background: #A09EAE !important;
        border-radius: 3px !important;
        transition: background 0.3s ease !important;
    }

    .dt-button-collection::-webkit-scrollbar-thumb:hover {
        background: #8B8A9A !important;
    }

    .dt-button-collection {
        scrollbar-width: thin !important;
        scrollbar-color: #A09EAE #f1f1f1 !important;
    }

    /* Pagination */
    .pagination,
    .page-item,
    .dataTables_paginate {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }

    .pagination .page-link,
    .page-item .page-link,
    .dataTables_paginate .paginate_button {
        text-decoration: none;
        border: none;
        background-color: transparent;
        font-weight: 500;
        font-size: 1.2rem;
        line-height: 1.6;
        letter-spacing: 0.3px;
        color: #623CE7;
        border-radius: 5px;
        padding: 0.6rem 0.6rem;
        min-width: 3rem;
        min-height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease-in-out;
        -webkit-font-smoothing: antialiased;
        margin: 0 2px !important;
    }

    .page-item.active .page-link,
    .page-item.active .page-link:focus,
    .page-item.active .page-link:active,
    .page-item.active .page-link:hover,
    .dataTables_paginate .paginate_button.current,
    .dataTables_paginate .paginate_button.current:focus,
    .dataTables_paginate .paginate_button.current:active,
    .dataTables_paginate .paginate_button.current:hover {
        background-color: #623CE7 !important;
        color: white !important;
        border-radius: 5px;
    }

    .page-item .page-link:hover,
    .dataTables_paginate .paginate_button:hover {
        background-color: #EFECFD !important;
        color: #623CE7 !important;
        border-radius: 5px;
    }

    .page-item .page-link:focus,
    .dataTables_paginate .paginate_button:focus {
        background-color: white !important;
        color: #623CE7 !important;
        outline: none !important;
        box-shadow: 0 0 0 2px #B7A5F4 !important;
    }

    .page-item .page-link:active,
    .dataTables_paginate .paginate_button:active {
        background-color: #36217F !important;
        color: white !important;
        border-radius: 5px;
    }

    .page-item.disabled .page-link,
    .dataTables_paginate .paginate_button.disabled {
        color: #A9A6A9 !important;
        background-color: transparent !important;
        cursor: default !important;
        pointer-events: none !important;
    }

    .page-item.disabled .page-link.next,
    .page-item.disabled .page-link.previous,
    .page-item.disabled .page-link.first,
    .page-item.disabled .page-link.last,
    .page-item.disabled .page-link.nav-arrow,
    .dataTables_paginate .paginate_button.disabled.first,
    .dataTables_paginate .paginate_button.disabled.previous,
    .dataTables_paginate .paginate_button.disabled.next,
    .dataTables_paginate .paginate_button.disabled.last {
        color: #A9A6A9 !important;
    }

    .page-item .page-link.next,
    .page-item .page-link.previous,
    .page-item .page-link.first,
    .page-item .page-link.last,
    .page-item .page-link.nav-arrow,
    .dataTables_paginate .paginate_button.first,
    .dataTables_paginate .paginate_button.previous,
    .dataTables_paginate .paginate_button.next,
    .dataTables_paginate .paginate_button.last {
        color: #623CE7 !important;
    }

    .page-item:not(.disabled) .page-link.next:hover,
    .page-item:not(.disabled) .page-link.previous:hover,
    .page-item:not(.disabled) .page-link.first:hover,
    .page-item:not(.disabled) .page-link.last:hover,
    .page-item:not(.disabled) .page-link.nav-arrow:hover,
    .dataTables_paginate .paginate_button.first:hover:not(.disabled),
    .dataTables_paginate .paginate_button.previous:hover:not(.disabled),
    .dataTables_paginate .paginate_button.next:hover:not(.disabled),
    .dataTables_paginate .paginate_button.last:hover:not(.disabled) {
        background-color: #EFECFD !important;
        color: #623CE7 !important;
        border-radius: 5px;
    }

    .page-item .page-link.next:focus,
    .page-item .page-link.previous:focus,
    .page-item .page-link.first:focus,
    .page-item .page-link.last:focus,
    .page-item .page-link.nav-arrow:focus,
    .page-item .page-link.next:active,
    .page-item .page-link.previous:active,
    .page-item .page-link.first:active,
    .page-item .page-link.last:active,
    .page-item .page-link.nav-arrow:active {
        background-color: white !important;
        color: #623CE7 !important;
        border-radius: 5px;
    }

    .dataTables_wrapper .dataTables_info {
        text-align: left;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        font-size: 14px;
        color: #6c757d;
    }

    .dataTables_wrapper .dataTables_paginate {
        text-align: right !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .dataTables_wrapper .row:last-child {
        margin-top: 10px;
    }

    /* Ensure pagination is always visible */
    .dataTables_paginate {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative !important;
        z-index: 1 !important;
    }

    .dataTables_paginate .pagination {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Pagination Count */

    .dt-info {
        display: block;
        font-family: 'Inter', sans-serif;
        font-size: 14px;
        font-weight: 400;
        color: #161327;

        padding: 8px 0;
        text-align: left;
    }


    /* Pagination Length Styles */
    select[id^="dt-length-"] {
        color: #161327;
        font-family: 'Inter', sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;

        width: 88px !important;
        height: 32px !important;
        border-radius: 4px;
        border: 1px solid #DEDCDF;
        background-color: #FFF;

        box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
        padding: 4px 32px 4px 8px;

        appearance: none;
        background: url('https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/icons/chevron-down.svg') no-repeat right 8px center / 12px;
    }

    label[for^="dt-length-"] {
        font-family: 'Inter', sans-serif;
        font-size: 14px;
        font-weight: 400;
        color: #161327;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    select[id^="dt-length-"]:focus {
        box-shadow: none !important;
        border: 1px solid #DEDCDF !important;
    }

    .card-header.panel_heading_new_style_staff_border.border-0 {
        /* margin-bottom: 20px; */
    }

    .col-md-12.mb-10 {
        margin-bottom: 10px;
    }

    /* New Search Box Can Remove Old One above Later */
    input[type="search"].input-search {
        font-family: "Inter", sans-serif !important;
        flex: 1;
        border: none;
        background: transparent;
        font-size: 15px;
        color: #161327;
        outline: none;
        line-height: 1.5;
        padding: 0;
        box-shadow: none;
    }

    input[type="search"].input-search::placeholder {
        color: #b3b3c6;
        font-size: 14px;
        font-weight: 400;
    }

    .search-box {
        display: flex;
        align-items: center;
        width: 263px;
        height: 40px;
        gap: 8px;
        border-radius: 4px;
        border: 1px solid #DEDCDF;
        background: #F9FAFB;
        padding: 0 14px 0 38px;
        position: relative;
        margin-right: -5px;
    }

    .search-box:focus-within {
        border-color: #623CE7;
    }

    .search-box .bi-search {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 20px;
        color: #d1d1e0;
        pointer-events: none;
        transition: color 0.2s;
    }

    .search-box:focus-within .bi-search {
        color: #623CE7;
    }

    #academic_year {
        height: 30px;
        margin-top: 11px;
    }

    /* Summary Table Styles */
    .container-fluid {
        padding-left: 0;
        padding-right: 0;
    }

    .summary-card {
        width: 100%;
        background: #CEC3F8;
        border-radius: 6px;
        padding: 0;
        margin-bottom: 2rem;
        overflow: hidden;
        margin-left: 0;
        margin-right: 0;
    }

    .summary-header {
        padding: 1rem;
        text-align: center;
        background: #CEC3F8;
    }

    .summary-wrapper {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: stretch;
        gap: 0;
        margin: 0;
        padding: 0;
    }

    .summary-table {
        flex: 1;
        min-width: 300px;
        background: white;
        border-right: none;
        margin: 0;
        padding: 0;
    }

    .summary-table:last-child {
        border-right: 1px solid transparent;
    }

    .summary-table table {
        width: 100%;
        height: 100%;
        table-layout: fixed;
        border-collapse: collapse !important;
        border-spacing: 0 !important;
        border: 1px solid rgba(177, 177, 177, 0.3) !important;
        background: white;
        margin: 0;
        padding: 0;
    }

    .summary-table th,
    .summary-table td {
        padding: 14px 16px;
        vertical-align: middle;
        word-wrap: break-word;
        border: 1px solid rgba(177, 177, 177, 0.3) !important;
    }

    .summary-table thead th {
        font-weight: bold;
        font-size: 14px;
        border-bottom: none !important;
        color: #000;
    }

    .summary-table tfoot th {
        /* background-color: #EFECFD !important; */
        font-weight: bold !important;
        color: #000;
    }

    .summary-table tfoot td {
        font-weight: bold !important;
        color: #000;
    }

    .summary-table tbody tr:nth-child(odd) {
        background-color: #FBFBFB;
    }

    /* .summary-table tbody tr:nth-child(even) {
        background-color: white;
    } */

    .summary-table:last-child table {
        border-radius: 0 0 6px 0;
    }

    .summary-table:first-child table {
        border-radius: 0 0 0 6px;
    }

    /* Fix for student_fees_summary_v2.php summary tables */
    .fee_summary {
        width: 100% !important;
        overflow-x: auto !important;
        max-width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
        display: block !important;
    }

    .fee_summary .summary-table {
        width: 100% !important;
        min-width: 100% !important;
        max-width: 100% !important;
        margin: 0 0 20px 0 !important;
        table-layout: auto !important;
        border-collapse: collapse !important;
        border-spacing: 0 !important;
    }

    .fee_summary .summary-table th,
    .fee_summary .summary-table td {
        padding: 10px 12px !important;
        vertical-align: middle !important;
        word-wrap: break-word !important;
        white-space: nowrap !important;
        min-width: auto !important;
        max-width: none !important;
        text-align: center !important;
        border: 1px solid #e5e7eb !important;
    }

    .fee_summary .summary-table th {
        background-color: #EFECFD !important;
        font-weight: 600 !important;
        font-size: 12px !important;
        color: #161327 !important;
    }

    .fee_summary .summary-table td {
        font-size: 11px !important;
        font-weight: 400 !important;
        color: #161327 !important;
    }

    .fee_summary .summary-table tbody tr:nth-child(even) {
        background-color: #FBFBFB !important;
    }

    .fee_summary .summary-table tbody tr:hover {
        background-color: #F4F4F5 !important;
    }

    .fee_summary .summary-table tfoot th,
    .fee_summary .summary-table tfoot td {
        font-weight: 500 !important;
        background-color: #f8f9fa !important;
    }

    .summary-card .custom-title {
        color: #161327;
        font-family: 'Inter', sans-serif;
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        text-transform: capitalize;
    }

    .summary-card .custom-dates {
        color: #161327;
        font-family: 'Inter', sans-serif;
        font-size: 18px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        text-transform: capitalize;
    }

    /* Label Styles */
    .label-text {
        color: #161327;
        font-family: 'Inter', sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 120%;
    }

    /* Unified Grey Scrollbar Styles */
    .table-responsive::-webkit-scrollbar,
    .fee_summary::-webkit-scrollbar,
    .dataTables_scrollBody::-webkit-scrollbar,
    .dataTables_wrapper::-webkit-scrollbar,
    .total_summary .table-responsive::-webkit-scrollbar {
        height: 6px;
        width: 6px;
        border-radius: 3px;
        background: #f1f1f1;
    }

    .table-responsive::-webkit-scrollbar-thumb,
    .fee_summary::-webkit-scrollbar-thumb,
    .dataTables_scrollBody::-webkit-scrollbar-thumb,
    .dataTables_wrapper::-webkit-scrollbar-thumb,
    .total_summary .table-responsive::-webkit-scrollbar-thumb {
        background: #A09EAE;
        border-radius: 3px;
        transition: background 0.3s ease;
    }

    .table-responsive::-webkit-scrollbar-track,
    .fee_summary::-webkit-scrollbar-track,
    .dataTables_scrollBody::-webkit-scrollbar-track,
    .dataTables_wrapper::-webkit-scrollbar-track,
    .total_summary .table-responsive::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .table-responsive::-webkit-scrollbar-thumb:hover,
    .fee_summary::-webkit-scrollbar-thumb:hover,
    .dataTables_scrollBody::-webkit-scrollbar-thumb:hover,
    .dataTables_wrapper::-webkit-scrollbar-thumb:hover,
    .total_summary .table-responsive::-webkit-scrollbar-thumb:hover {
        background: #8B8A9A;
    }

    .table-responsive,
    .fee_summary,
    .dataTables_scrollBody,
    .dataTables_wrapper,
    .total_summary .table-responsive {
        scrollbar-width: thin;
        scrollbar-color: #A09EAE #f1f1f1;
        border-radius: 3px;
    }

    /* DataTable Vertical Scrolling Styles */
    .dataTables_wrapper {
        max-height: none;
        overflow: visible;
    }

    .dataTables_scrollBody {
        max-height: none !important;
        overflow-y: visible !important;
    }

    #fee-container-scroll {
        max-height: none;
        overflow: visible;
    }

    /* Fix column overlapping when columns are hidden in DataTables */
    .dataTables_wrapper table {
        table-layout: auto !important;
        width: 100% !important;
    }
    
    .dataTables_wrapper table th,
    .dataTables_wrapper table td {
        min-width: auto !important;
        width: auto !important;
        max-width: none !important;
    }
    
    /* Specific DataTable containers */
    #fee_summary_data_wrapper table,
    #fee_summary_data_summary_wrapper table {
        table-layout: auto !important;
        width: 100% !important;
    }
    
    #fee_summary_data_wrapper table th,
    #fee_summary_data_wrapper table td,
    #fee_summary_data_summary_wrapper table th,
    #fee_summary_data_summary_wrapper table td {
        min-width: auto !important;
        width: auto !important;
        max-width: none !important;
    }
    
    /* Fix for when serial number column is removed */
    .dataTables_wrapper table th:first-child,
    .dataTables_wrapper table td:first-child {
        min-width: auto !important;
        width: auto !important;
    }
    
    /* Handle long names better */
    .dataTables_wrapper table th,
    .dataTables_wrapper table td {
        word-wrap: break-word !important;
        word-break: break-word !important;
        white-space: normal !important;
        overflow-wrap: break-word !important;
    }
    
    /* Specific fix for student name column when serial is hidden */
    .dataTables_wrapper table th.student-name-column,
    .dataTables_wrapper table td.student-name-column {
        min-width: 180px !important;
        width: 200px !important;
        white-space: normal !important;
    }

    /* Alumni student for iisb*/
    .alumni-row {
        color: #d30c0c !important;
    }

    .alumni-row td {
        color: #d30c0c !important;
    }

    /* Date Dropdown */

    .dropdown-toggle::before,
    #reportrange::before {
        content: "";
        display: inline-block;
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        width: 1em;
        height: 1em;
        z-index: 2;
        background-size: 12px;
        background: url('https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/icons/chevron-down.svg') no-repeat center center/contain;
    }

    #reportrange {
        background-color: #fff;
        height: 40px;
        position: relative;
        border: 1px solid #ced4da;
        border-radius: 4px;
    }

    /* Filter Validation Error Styling */
    .fee-type-error .bootstrap-select .dropdown-toggle,
    .fee-type-error select.form-control {
        background-color: #FDECEC !important;
        border-color: #EE443F !important;
        color: #EE443F !important;
    }

    .fee-type-error .bootstrap-select .dropdown-toggle .caret,
    .fee-type-error .bootstrap-select>.dropdown-toggle::after {
        color: #EE443F !important;
    }

    /* Remove dropdown arrow specifically from DataTables colvis buttons */
    .dt-buttons .buttons-colvis.dropdown-toggle::before,
    .dt-buttons .buttons-colvis::before,
    .buttons-colvis.dropdown-toggle::before {
        display: none !important;
    }

    /* Also remove any ::after arrows that might be added */
    .dt-buttons .buttons-colvis.dropdown-toggle::after,
    .dt-buttons .buttons-colvis::after,
    .buttons-colvis.dropdown-toggle::after {
        display: none !important;
    }
    /* Add spacing for DataTable buttons and search */
    .dt-buttons {
        margin-bottom: 15px !important;
    }

    .search-box{
        margin-bottom: 20px !important;
    }

    /* Add spacing for SMS button */
    .custom-sms-button {
        margin-bottom: 15px !important;
    }
</style>