<?php include(APPPATH . 'views/feesv2/reports/components/report_header.php'); ?>

<div class="card-body">
            <form class="form-horizontal">
                <div class="form-group" style="margin-left: 1.2rem !important;">
                    <label style="font-family: poppins; font-weight:400; font-size:1.1rem; margin-bottom: 8px; display: block;">Select a Date<font color="red">*</font></label>
                    <div style="width: 300px;">
                        <select class="form-control select" name="selected_date[]" id="selected_date" onchange="get_daily_transactions()">
                            <option value="">-- Select a Date --</option>
                            <?php foreach ($transaction_dates as $td): ?>
                                <option 
                                    value="<?= $td->tx_date ?>" 
                                    style="background-color: <?= $td->settlement_status === 'NOT_SETTLED' ? 'white' : 'green'; ?>; color: <?= $td->settlement_status === 'NOT_SETTLED' ? 'black' : 'white'; ?>;">
                                    <?= $td->tx_date ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </form>

            <div class="card-body">
                <div class="progress" id="progress" style="display: none; height: 1rem; border-radius: 0.375rem; background-color: #e9ecef;">
                <div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 70%;background-color: #0d6efd;border-radius: 0.375rem;">
                </div>
                </div>
            </div>


            <!-- <hr> -->

            <form id="demo-form" action="<?php echo site_url('Payment_controller/confirmSettlement'); ?>" method="post" class="form-horizontal" data-parsley-validate>
                <input type="hidden" name="trans_ids" id="trans_id" value="">
                <input type="hidden" name="settlement_ids" id="settlement_id" value="">
                <div id="confirmed" style="display: none;" class="text-right mb-2"><strong>Settlement Receipt Confirmed</strong></div>
                <div id="confirmBtn" style="display: none;" class="text-right mb-2">
                    Click here to acknowledge settlement receipt. &nbsp;&nbsp;
                    <button class="btn btn-danger" style="margin-bottom: 2px;">Settlement Received</button>
                </div>
            </form>

            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 24px; margin-bottom: 12px;">
                <div id="trans" style="font-weight: 400 !important; font-family: Poppins, sans-serif; text-transform: uppercase; font-size: 0.9rem; color: #555; border-left: 4px solid #0d6efd; padding-left: 8px;"></div>
                <div id="printBtnContainer" style="text-align: right;">
                    <button id="printBtn" class="print_btn" style="display: none;" onclick="printProfile()">
                        <?= $this->load->view('svg_icons/print.svg', [], true); ?> Print
                    </button>
                </div>
            </div>

            <div id="printArea">
                <div id="ajax_table"></div>
                <div id="trans_grandtotal" class="mt-2" style="display: inline-block;"></div>
                <div id="settle" class="mt-4" style="display: inline-block;"></div>
                <div id="table"></div>
            </div>
        </div>
    </div>
</div>
   
                
<script>
  function printProfile(){
        $("#demo-form").hide();
        var restorepage = document.body.innerHTML;
        var printcontent = document.getElementById('printArea').innerHTML;
        document.body.innerHTML = printcontent;
        window.print();
        document.body.innerHTML = restorepage;
        $("#demo-form").show();
  }
</script>

<script>
function get_daily_transactions(){
    $('#progress').show();
    $('#trans_table').html('');
    $('#settlement_table').html('');

    $('#ajax_table').html('');
    $('#trans_grandtotal').html('');
    $('#settle').html('');
    $('#trans').html('');
    $('#printBtn').hide();
    $("#confirmed").hide();
    $("#confirmBtn").hide();

    var date = $("#selected_date").val();
    // var input_array = input.split(",");
    // var date = input_array[0];
    // var verify = input_array[1];
    // var conformed_by = input_array[2];
    // var conformed_on = input_array[3];

    // $('#trans').html('<h4> Transactions on ' + date + '</h4>');
    $("#confirmBtn").hide();
    $("#confirmed").hide();
    $.ajax({
        url:'<?php echo site_url('payment_controller/get_daily_transactions') ?>',
        type:'post',
        data : {'date':date},
        success : function(data){
            $('#progress').hide();
            var data = JSON.parse(data);
            console.log(data);
            var transYear = data.transaction_list;
            var settlements = data.settlement;
            var settlement_amount = data.settlement_amount;
            var trans_amount = data.trans_amount;
            var not_settled_count = data.not_settled_count;
            var settled_count = data.settled_count;
            var isConfirmed = data.isConfirmed;
            var component_total = data.component_total;
            // console.log(data);

            var html  = '';
            var trans_ids = [];
            var trans_grandtotal = 0;
            // for(var i=0; i < transList.length; i++ ){
              for (var years in  transYear){
                var row = 0;
                var total = 0;
                var totalConcession = 0;
                var totalAdjustment_amount = 0;
                var totalfine_amount = 0;
                // html +='<h3 style="font-weight: 400 !important; font-family: Poppins, sans-serif; text-transform: uppercase; font-size: 1.5rem; color: #555; border-left: 4px solid #0d6efd; padding-left: 8px;margin-bottom: 12px; ">' + years + '</h3>';
                html += '<table class="table table-bordered">';
                html += '<thead>';
                html += '<tr>';
                html += '<th>#</th><th>Admission No</th><th>Name</th><th>Blueprint</th><th>Components</th><th>Amount</th>';
                html += '</tr></thead><tbody>';
                var transList = transYear[years];
                Object.keys(transList).forEach(function(i) {

                    trans_ids.push(i);
                    html += '<tr>';
                    row += 1;
                    total += parseFloat(transList[i].amount);
                    html += '<td>' + row + '</td>';
                    html += '<td>' + transList[i].admission_no + '</td>';
                    html += '<td>' + capitalizeFirstLetter(transList[i].stdName) + '</td>';               
                    html += '<td>' + transList[i].blueprint_name + '</td>';
                    var installments = transList[i].installment;
                    html += '<td>';
                    Object.keys(installments).forEach(function(key) {
                        html += installments[key]+'<br>';
                    });

                    if (transList[i].concession !='0.00') {
                        html += '<b>Concession </b> :'+transList[i].concession + '<br>';
                        totalConcession += parseFloat(transList[i].concession);
                    }
                    if (transList[i].adjustment_amount !='0.00') {
                        html += '<b>Adjustment </b> :'+transList[i].adjustment_amount + '<br>';
                        totalAdjustment_amount += parseFloat(transList[i].adjustment_amount);
                    }
                    if (transList[i].fine_amount !='0') {
                        html += '<b>Late Fee </b> :'+transList[i].fine_amount + '<br>';
                        totalfine_amount += parseFloat(transList[i].fine_amount);
                    }
                  
                    html += '</td>';
                    html += '<td>' + transList[i].amount + '</td>';
                    html += '</tr>';
                });
                 html += '</tbody><tfoot><th>#</th><th>Total ' + row + ' Transactions</th><th></th><th></th>';
                html += '<th>';
                Object.keys(component_total[years]).forEach(function(compName) {
                    html += compName+' = '+component_total[years][compName]+'<br>';
                });
                if (totalConcession != 0) {
                    html += 'Total Concession :'+totalConcession + '<br>';
                }
                if (totalAdjustment_amount != 0) {
                    html += 'Total Adjustment :'+totalAdjustment_amount + '<br>';
                }
                if (totalfine_amount != 0) {
                    html += 'Total Late Fee :'+totalfine_amount + '<br>';
                }
                html += '</th>';
                html += '<th style="color: #00701a;font-size:16px;font-weight:700;">' + total.toFixed(2) + '</th></tfoot></table>';
                trans_grandtotal += parseFloat(total);
            }  
           
            $("#trans_id").val(trans_ids);
            $('#ajax_table').html(html);
            $('#trans_grandtotal').html('<div style="font-size: 14px;"><strong>Grand Total: '+trans_grandtotal+'</strong></div>');
            $('#settle').html('<div style="font-size: 14px;"><strong>Settlement</strong></div>');

            if (trans_grandtotal > 0) {
                  $('#printBtn').show();
              } else {
                  $('#printBtn').hide();
              }

            var html1 = '<table class="table table-bordered">';
            html1 += '<tr>';
            var settle_ids = [];
            if(settlements.length == 0) {
                $('#settle').html('<div style="font-size: 14px;"><strong> &nbsp; & &nbsp Settlement not started</strong></div>');
            } else {
                for (var j = 0; j < settlements.length; j++) {
                    settle_ids.push(settlements[j].settlement_id);
                    html1 += '<td>';
                    html1 += '<strong>Account Name: </strong>'+settlements[j].account_name+'<br>';
                    html1 += '<strong>Account Number: </strong>'+settlements[j].account_number+'<br>';
                    html1 += '<strong>Bank Reference: </strong>'+((settlements[j].bank_reference)?settlements[j].bank_reference:'Not Processed')+'<br>';
                    html1 += '<strong>Amount: <span style="color: #00701a;font-size:16px;font-weight:700;">'+settlements[j].payout_amount+'</span></strong><br>';
                    html1 += '<strong>Settlement Date: </strong>'+settlements[j].settlement_datetime+'<br>';
                    html1 += '<strong>Status: '+settlements[j].status+'</strong><br>';
                    html1 += '</td>';
                }
               
                html1 += '</tr></table>';
                $("#settlement_id").val(settle_ids);
                // console.log(html1);
                if(settlement_amount != trans_amount) {
                    html1 += '<table class="table table-bordered"><tr><td><strong>Total Amount: <span style="color: #ff4335;font-size:16px;font-weight:700;">'+settlement_amount+'</span></strong></td>';
                } else {
                    html1 += '<table class="table table-bordered"><tr><td><strong>Total Amount: <span style="color: #00701a;font-size:16px;font-weight:700;">'+settlement_amount+'</span></strong></td>';
                }
                
                if(not_settled_count == 0) {
                    if(isConfirmed) {
                        $("#confirmed").show();
                    }else {
                        $("#confirmBtn").show();
                    }

                    html1 += '<td style="color: #00701a;font-size:16px;font-weight:700;"><strong>Settlement Completed</strong></td>';
                } else {
                    html1 += '<td style="color: #00701a;font-size:16px;font-weight:700;"><strong>Settlement Pending</strong></td>';
                }
                html1 += '<tr></table>';
                $('#settlement_table').html(html1);
            }
        },
        error: function() {
            $('#progress').hide(); // Hide progress bar on error
            alert('Failed to load data.');
        }
    });
}

function capitalizeFirstLetter(str) {
    return str.replace(/\w\S*/g, function(txt){
        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });
}

</script>
<style>
    table {
        font-family: 'inter', sans-serif !important;
    }

    table{
    width: 100%;
    border-collapse: collapse;
    background-color: #ffffff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
    opacity: 1 !important;
    transition: none !important;
    }
    table thead th{
    position: sticky !important;
    top: 0;
    background-color: #f1f5f9;
    z-index: 10 !important;
    color: #111827;
    font-size: 11px;
    font-weight: 500;
    z-index: 10;
    text-align: left;
    padding: 12px 16px;
    }
    table th, table td{
    padding: 10px 14px;
    border-bottom: 1px solid #e5e7eb;
    font-size: 11px;
    font-weight: 400;
    }

    table tbody tr:nth-child(even) {
    background-color: #f9fafb;
    }

    table tbody tr:hover {
    background-color: #f1f5f9;
    }
    table tfoot tr {
    background-color: #f3f4f6;
    font-weight: 500;
    }

    /* Fix dropdown z-index issues */
    .action-dropdown-menu {
        z-index: 1000 !important;
    }
    
    .dropdown-menu {
        z-index: 1000 !important;
    }
    
    .bootstrap-select .dropdown-menu {
        z-index: 1000 !important;
    }
    .print_btn {
        all: unset;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75em 1.5em;
        border: 2px solid #623CE7;
        color: #623CE7;
        background-color: transparent;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.2s ease;
        gap: 0.5em;
        border-radius: 4px;
    }

</style>