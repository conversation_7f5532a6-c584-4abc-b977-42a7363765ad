<?php include(APPPATH . 'views/feesv2/reports/components/report_header.php'); ?>



<div class="card-body" style="padding-bottom: 0px;">
        <div class="row g-3 mb-3">
            <div class="col-md-4">
                <label class="form-label">Date Range</label>
                <div class="d-flex align-items-end">
                    <input type="text" id="reportrange" class="form-select" placeholder="Select Date Range" readonly />
                    <input type="hidden" id="from_date">
                    <input type="hidden" id="to_date">
                    <button class="getreport_btn" id="search" onclick="get_challan_transactions()" style="margin-left: 5px; white-space: nowrap;">Get Report</button>
                </div>
            </div>
            <div class="col-md-4" style="padding-left: 10px !important;">
                <label class="form-label">Generate Challan</label>
                <div class="d-flex align-items-end">
                    <input type="text" id="reportrange_generate" class="form-select" placeholder="Select Date Range" readonly />
                    <input type="hidden" id="from_date_generate">
                    <input type="hidden" id="to_date_generate">
                    <button class="getreport_btn" id="generate_challa_button" onclick="generate_challa_details()" style="margin-left: 5px; white-space: nowrap;">Generate Challan</button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-sm-12 col-md-12">
      <div style="display: none;" class="progress" id="progress">
        <div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%;"></div>
      </div>
    </div>

    <div class="card-body">
      <div class="row" style="margin: 0px;display: block;">
        <div class="card-body studentData leaveData table-responsive" id="settlement_table"></div>
      </div>
    </div>
  </div>

</div>


<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<style type="text/css">
    table {
        font-family: 'Inter', sans-serif !important;
    }
    .gap-left {
      margin-left: 20px !important;
    }

    #settlement_table{
    width: 100%;
    border-collapse: collapse;
    }
    #settlement_table thead th{
    background-color: #f8f9fa;
    color: #495057;
    font-size: 12px;
    font-weight: 600;
    text-align: left;
    padding: 8px 12px;
    border-bottom: 2px solid #dee2e6;
    }
    #settlement_table th, #settlement_table td{
    padding: 8px 12px;
    border-bottom: 1px solid #dee2e6;
    font-size: 12px;
    }

    #settlement_table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
    }

    #settlement_table tbody tr:hover {
    background-color: #f1f5f9;
    }
    #settlement_table tfoot tr {
    background-color: #f3f4f6;
    font-weight: 500;
    }

/* Fix date picker z-index to appear above data table headers */
.daterangepicker {
  z-index: 99999 !important;
}

.daterangepicker * {
  z-index: 99999 !important;
}

/* Fix SweetAlert2 popup z-index to appear above data table headers */
.swal2-container {
  z-index: 999999 !important;
}

.swal2-popup {
  z-index: 999999 !important;
}

.swal2-backdrop {
  z-index: 999998 !important;
}
</style>

<script>
</script>

<script type="text/javascript">
  $("#reportrange").daterangepicker({
    ranges: {
     'Last 7 Days': [moment().subtract(6, 'days'), moment()],
     'Last 30 Days': [moment().subtract(29, 'days'), moment()],
     'This Month': [moment().startOf('month'), moment().endOf('month')],
     'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'MM.DD.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment()            
  },function(start, end) {
    $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date').val(start.format('DD-MM-YYYY'));
    $('#to_date').val(end.format('DD-MM-YYYY'));
  });
  $("#reportrange span").html(moment().subtract(6, 'days').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

  $('#from_date').val(moment().subtract(6, 'days'));
  $('#to_date').val(moment().format('DD-MM-YYYY'));


  $("#reportrange_generate").daterangepicker({
    ranges: {
     'Last 7 Days': [moment().subtract(6, 'days'), moment()],
     'Last 30 Days': [moment().subtract(29, 'days'), moment()]
    },
    dateLimit: {
      'months': 1,
      'days': -1
    },
    opens: 'right',
    buttonClasses: ['btn btn-default'],
    applyClass: 'btn-small btn-primary',
    cancelClass: 'btn-small',
    format: 'MM.DD.YYYY',
    separator: ' to ',
    startDate: moment(),
    endDate: moment()    
  },function(start, end) {
    $('#reportrange_generate span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
    $('#from_date_generate').val(start.format('DD-MM-YYYY'));
    $('#to_date_generate').val(end.format('DD-MM-YYYY'));
  });
  $("#reportrange_generate span").html(moment().subtract(6, 'days').format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));

  // $('#from_date_generate').val(moment().subtract(6, 'days'));
  // $('#to_date_generate').val(moment().format('DD-MM-YYYY'));
</script>


<script type="text/javascript">


  function numberToCurrency(amount) {
    var formatter = new Intl.NumberFormat('en-IN', {
      currency: 'INR',
    });
    return formatter.format(amount);
  }


 function get_challan_transactions(){
  
    var fromdate = $("#from_date").val();
    var toDate = $("#to_date").val();

    var $btn = $("#search");
    var originalText = $btn.val();
    $btn.prop('disabled', true).val('Please wait...');


    $('#trans').html('<h4><strong>Transactions on From ' + fromdate + ' to '+toDate+'</strong></h4>');
    $("#confirmBtn").hide();
    $("#confirmed").hide();
    $("#progress").show();
    $.ajax({
        url:'<?php echo site_url('payment_controller/getChallanTransactionDetailsByStudent') ?>',
        type:'post',
        data : {'fromdate':fromdate,'toDate':toDate},
        success : function(data){
            var data = JSON.parse(data);
            $("#btns_display").show();
            $('#settlement_table').html(prepare_student_table(data));

            $('#customers2').DataTable({
                "language": {
                "search": "",
                "searchPlaceholder": "Enter Search..."
                },
                dom: '<"d-flex justify-content-end align-items-center mb-1"<"d-flex align-items-center"fB>>rtip',
                paging: false,
                scrollX: true,
                scrollY: '400px',
                scrollCollapse: true,
                oLanguage: {
                    sSearch: ""
                },
                buttons: [
                    {
                        extend: 'excel',
                        text: `<button class="btn btn-outline-primary" id="expbtns"><?= $this->load->view('svg_icons/excel_2.svg', [], true); ?> Excel</button>`,
                        filename: 'Challan Transaction Report',
                        exportOptions: {
                            columns: ':visible'
                        },
                    },
                    {
                        extend: 'print',
                        text: `<button class="btn btn-outline-primary" id="expbtns" style="margin-right:-7px;"><?= $this->load->view('svg_icons/print.svg', [], true); ?> Print</button>`,
                        filename: 'Challan Transaction Report',
                        exportOptions: {
                            columns: ':visible'
                        },
                    }
                ]
            });
            
            // Style the search input to match the button design
            styleSearchInput();
            $("#progress").hide();
            $btn.prop('disabled', false).val(originalText);
        },
        error: function() {
            // Hide progress bar even if there's an error
            $("#progress").hide();
            alert('Failed to fetch data.');
            $btn.prop('disabled', false).val(originalText);
        }
    });
}

</script>

<script>
    function prepare_student_table (std) {
        var html = '';
        
        if(std == ''){
            html += "<h4 style='margin-left:20px'>No data</h4>";
        }
        else {
        html += '<table id="customers2" class="table table-bordered datatable"><thead><th style="width:2%">#</th><th style="width:8%">Order Id</th><th>Name</th><th>Phone Number</th><th>email</th><th style="width:4%">Amount</th><th style="width:10%">Date (in GMT) </th><th>Payment Status</th><th style="width:8%">Transaction Id</th><th>Transaction Status</th></thead><tbody>';
        // console.log(std);
        for (i=0;i < std.length; i++) {
            html += "<tr><td>" + (i+1) + "</td>";
            html += "<td>" + std[i].order_id  + "</td>";
            html += "<td><a>" + capitalizeFirstLetter(std[i].customer_name) + "</a></td>";
            html += "<td><a>" + std[i].customer_phone + "</a></td>";
            html += "<td><a>" + std[i].customer_email + "</a></td>";
            html += "<td>" + std[i].amount_orig  + "</td>";
            html += "<td>" + std[i].payment_datetime + "</td>";
           
            html += "<td>" + std[i].response_message + "</td>";
            html += "<td>" + std[i].transaction_id + "</td>";
            html += "<td>" + std[i].payment_status  + "</td>";
            html += "</tr>";
        }
        html += '</tbody></table>';
    }
    return html;
}

function capitalizeFirstLetter(str) {
    return str.replace(/\w\S*/g, function(txt){
        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });
  }

// Function to style search input
function styleSearchInput() {
    // Method 1: Target the new DataTables DOM structure
    let $input = $('.dt-search input[type="search"]');
    if ($input.length) {      
        // Add placeholder
        $input.attr('placeholder', 'Search');
        
        // Style the input
        if ($input.parent().hasClass('search-box')) {
            $input.unwrap();
        }
        $input.siblings('.bi-search').remove();
        $input.addClass('input-search');
        if (!$input.parent().hasClass('search-box')) {
            $input.wrap('<div class="search-box position-relative" style="display: inline-block; margin-right: 1px; margin-bottom: 5px;"></div>');
            $input.parent().prepend('<i class="bi bi-search"></i>');
        }
        return true;
    }
    return false;
}
  
  function generate_challa_details(){
  var fromdate_challan = $("#from_date_generate").val();
  var toDate_challan = $("#to_date_generate").val();
  $('#generate_challa_button').prop('disabled',true).html('Please wait..');

    $.ajax({
        url:'<?php echo site_url('payment_controller/get_get_challan_transactions_details') ?>',
        type:'post',
        data : {'fromdate_challan':fromdate_challan,'toDate_challan':toDate_challan},
        success : function(data){
          if(data){
            Swal.fire({
              title: "Challan Generated",
              // text: "Challan Generated",
              icon: "success"
            });
          }else{
            Swal.fire({
              title: "Already Exists",
              text: "Challan already exists for this date range.",
              icon: "info"
            });
          }
          $('#generate_challa_button').prop('disabled',false).html('Generate Challan');

        }
    });
}
</script>