<link href="https://fonts.googleapis.com/css2?family=Oswald:wght@400;600&display=swap" rel="stylesheet">

<div class="col-md-12">
    <!-- Locked Order Notification Banner -->
    <div id="locked-order-banner" style="display: none;" class="locked-order-banner mb-4">
        <div class="locked-order-content">
           
            <div class="lock-message">
                <h5>Order Actions Locked</h5>
                <p id="lock-reason">This order's status prevents making any changes to ID cards.</p>
            </div>
        </div>
    </div>
    <?php 
        $stakeholderId = ($this->authorization->isAuthorized('ID_CARDS.MANAGE_ID_CARD_ORDERS')) 
            ? true 
            : (($this->authorization->getAvatarStakeHolderId() == 0) ? true : false);
        ?>
    <div id="entity-container" class="entity-container">
        <!-- Filter Bar -->
        <div class="filter-bar">
            <div class="status-tabs">
                <button class="status-tab active" data-status="all">All <span class="status-count"><?php if($stakeholderId){ echo isset($status_counts->total) ?  $status_counts->total : 0; } ?></span></button>
                <button class="status-tab" data-status="in review">In Review <span class="status-count"><?php if($stakeholderId){ echo isset($status_counts->in_review) ? $status_counts->in_review : 0; }?></span></button>
                <button class="status-tab" data-status="approved">Approved <span class="status-count"><?php if($stakeholderId){ echo isset($status_counts->approved) ? $status_counts->approved : 0; } ?></span></button>
                <button class="status-tab" data-status="removed">Removed <span class="status-count"><?php if($stakeholderId){ echo isset($status_counts->removed) ? $status_counts->removed : 0 ; }?></span></button>
                <button class="status-tab" data-status="modify">Modify <span class="status-count"><?php if($stakeholderId){ echo isset($status_counts->modify) ? $status_counts->modify : 0; } ?></span></button>
                <button class="status-tab" data-status="re_ordered">Re-Ordered <span class="status-count"><?php if($stakeholderId){ echo isset($status_counts->re_ordered) ? $status_counts->re_ordered : 0; } ?></span></button>
            </div>
            <div class="search-filters">
                <input type="text" class="search-input" id="search-entities" placeholder="<?= $order_details->id_card_for == 'Staff' ? 'Search Staff' : 'Search Students' ?>">
                <!-- <select class="filter-select" id="class-filter">
                    <option value=""><?php // $order_details->id_card_for == 'Staff' ? 'Department' : 'Class' ?></option>
                    <?php //if($order_details->id_card_for == 'Staff'): ?>
                    <option value="IT">IT</option>
                    <option value="HR">HR</option>
                    <option value="Admin">Admin</option>
                    <?php //else: ?>
                    <option value="1">Class 1</option>
                    <option value="2">Class 2</option>
                    <option value="3">Class 3</option>
                    <?php //endif; ?>
                </select>
                <select class="filter-select" id="section-filter">
                    <option value=""><?php //$order_details->id_card_for == 'Staff' ? 'Designation' : 'Section' ?></option>
                    <?php //if($order_details->id_card_for == 'Staff'): ?>
                    <option value="Manager">Manager</option>
                    <option value="Developer">Developer</option>
                    <option value="Assistant">Assistant</option>
                    <?php //else: ?>
                    <option value="A">A</option>
                    <option value="B">B</option>
                    <option value="C">C</option>
                    <?php //endif; ?>
                </select> -->
            </div>
        </div>

        <!-- Entity Table -->
        <div class="entity-table-container">
            <div class="entity-table-wrapper">
                <!-- Table will be inserted here by construct_entity_table function -->
            </div>
        </div>

        <div class="entity-count">
            Showing <span id="visible-count"></span> of <span id="total-count"></span> <?= $order_details->id_card_for == 'Staff' ? 'Staff' : 'Students' ?>
            <div id="loading-progress" class="loading-progress-inline" style="display: none;">
                <div class="progress-bar-container">
                    <div class="progress-bar" id="data-progress-bar"></div>
                </div>
                <span id="progress-text">Loading...</span>
            </div>

        </div>
    </div>
</div>

<!-- Enhanced Loading Overlay -->
<div id="enhanced-loading-overlay" style="display: none;">
    <div class="enhanced-loading-content">
        <div class="loading-animation">
            <div class="spinner-border" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
        <h4 id="loading-title">Loading Data</h4>
        <p id="loading-message">Please wait while we fetch your data...</p>
        <div class="loading-progress-bar">
            <div class="progress">
                <div class="progress-bar progress-bar-striped progress-bar-animated"
                     id="main-progress-bar" role="progressbar" style="width: 0%"></div>
            </div>
            <small id="loading-stats" class="text-muted">Preparing...</small>
        </div>
    </div>
</div>

<!-- Notification Container -->
<div id="notification-container" class="notification-container"></div>

<!-- ID Card Preview Modal -->
<div id="idCardModal" class="id-card-modal">
    <div class="id-card-modal-content">
        <!-- Close button at the top right corner -->
        <span class="id-card-close ">&times;</span>
        <h3 id="idCardModalTitle" class="my-2" >ID Card Preview</h3>
        <!-- Navigation buttons -->
        <div class="id-card-navigation">
            <button id="prevStaffBtn" class="id-card-nav-btn icon-btn" title="Previous">
                <i class="fa fa-chevron-left"></i>
            </button>

            <button id="nextStaffBtn" class="id-card-nav-btn icon-btn" title="Next">
                <i class="fa fa-chevron-right"></i>
            </button>
        </div>

        <!-- Modal content loading indicator -->
        <div id="modalLoadingIndicator" style="display: none;">
            <div class="modal-loading-spinner">
                <i class="fa fa-spinner fa-spin fa-3x"></i>
                <p>Loading ID card data...</p>
            </div>
        </div>

        <div class="card">
        <div class="card-header d-flex align-items-center" style="gap: 12px; min-height: 32px;">
    <!-- Status badge will be injected here by JS -->
    <div id="entityStatusBadge"
        class="status-button"
        style="font-size: 14px; font-weight: 500; padding: 4px 10px; border-radius: 20px; min-width: 100px; display: inline-block;">
        <div class="dot"></div>
        <!-- Status will be set dynamically -->
    </div>

    <div id="entityPositionIndicator" style="font-size: 12px; color: #888; margin-left: 12px; display: none;"></div>

    <!-- Adjustment Controls Menu -->
    <div class="adjustment-menu ml-auto">
        <div class="dropdown">
            <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" id="adjustmentMenuBtn" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fa fa-cog"></i> Adjust
            </button>
            <div class="dropdown-menu dropdown-menu-right p-3" aria-labelledby="adjustmentMenuBtn" style="min-width: 300px;">
                <!-- Photo Adjustment Section -->
                <h6 class="dropdown-header">
                    <i class="fa fa-image text-primary"></i> Photo Adjustment
                </h6>

                <!-- Photo Zoom -->
                <div class="form-group mb-2">
                    <label class="small mb-1">Zoom</label>
                    <div class="d-flex align-items-center">
                        <button class="btn btn-sm btn-outline-secondary mr-2" id="zoomOutBtnInline" title="Zoom Out">
                            <i class="fa fa-search-minus"></i>
                        </button>
                        <input type="range" class="form-control-range flex-grow-1" id="zoomSliderInline" min="0.5" max="3" step="0.1" value="1">
                        <button class="btn btn-sm btn-outline-secondary ml-2" id="zoomInBtnInline" title="Zoom In">
                            <i class="fa fa-search-plus"></i>
                        </button>
                    </div>
                </div>

                <!-- Photo Horizontal Alignment -->
                <div class="form-group mb-2">
                    <label class="small mb-1">Horizontal Alignment</label>
                    <div class="d-flex align-items-center">
                        <button class="btn btn-sm btn-outline-secondary mr-2" id="alignLeftBtnInline" title="Move Left">
                            <i class="fa fa-arrow-left"></i>
                        </button>
                        <input type="range" class="form-control-range flex-grow-1" id="alignmentSliderInline" min="0" max="100" step="5" value="50">
                        <button class="btn btn-sm btn-outline-secondary ml-2" id="alignRightBtnInline" title="Move Right">
                            <i class="fa fa-arrow-right"></i>
                        </button>
                    </div>
                </div>

                <!-- Photo Vertical Alignment -->
                <div class="form-group mb-2">
                    <label class="small mb-1">Vertical Alignment</label>
                    <div class="d-flex align-items-center">
                        <button class="btn btn-sm btn-outline-secondary mr-2" id="alignUpBtnInline" title="Move Up">
                            <i class="fa fa-arrow-up"></i>
                        </button>
                        <input type="range" class="form-control-range flex-grow-1" id="verticalAlignmentSliderInline" min="0" max="100" step="5" value="50">
                        <button class="btn btn-sm btn-outline-secondary ml-2" id="alignDownBtnInline" title="Move Down">
                            <i class="fa fa-arrow-down"></i>
                        </button>
                    </div>
                </div>

                <!-- Photo Reset -->
                <div class="form-group mb-3">
                    <button class="btn btn-sm btn-outline-warning btn-block" id="resetPhotoBtnInline">
                        <i class="fa fa-undo"></i> Reset Photo
                    </button>
                </div>

                <div class="dropdown-divider"></div>

                <!-- Name Adjustment Section -->
                <h6 class="dropdown-header">
                    <i class="fa fa-font text-warning"></i> Text Size <span id="currentNameSize" class="badge badge-info ml-2">14px</span>
                </h6>

                <!-- Field Selector -->
                <div class="form-group mb-2">
                    <label class="small mb-1">Select Field to Adjust</label>
                    <select class="form-control form-control-sm" id="fieldSelector">
                        <option value="name">Name</option>
                        <option value="father_name">Father Name</option>
                        <option value="mother_name">Mother Name</option>
                    </select>
                </div>

                <!-- Font Size -->
                <div class="form-group mb-2">
                    <label class="small mb-1">Font Size (8px - 36px)</label>
                    <div class="d-flex align-items-center">
                        <button class="btn btn-sm btn-outline-secondary mr-2" id="decreaseNameSize" title="Decrease">
                            <i class="fa fa-minus"></i>
                        </button>
                        <input type="range" class="form-control-range flex-grow-1" id="nameSizeSlider" min="8" max="36" step="1" value="14">
                        <button class="btn btn-sm btn-outline-secondary ml-2" id="increaseNameSize" title="Increase">
                            <i class="fa fa-plus"></i>
                        </button>
                    </div>
                </div>

                <!-- Name Reset -->
                <div class="form-group mb-0">
                    <button class="btn btn-sm btn-outline-warning btn-block" id="resetNameSize">
                        <i class="fa fa-undo"></i> Reset Name Size
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 text-center mb-4">
                        <h5 class="mb-3">Front</h5>
                        <div id="frontCardPreview" class="card-preview mx-auto">
                            <!-- Front preview will be rendered here -->
                        </div>
                    </div>
                    <div class="col-md-6 text-center mb-4">
                        <h5 class="mb-3">Back</h5>
                        <div id="backCardPreview" class="card-preview mx-auto">
                            <!-- Back preview will be rendered here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress bar for approval process -->
        <div id="approval-progress-container" style="display: none;">
            <div class="progress-status">Processing: <span id="progress-status-text">Preparing...</span></div>
            <div class="progress-bar-container">
                <div id="approval-progress-bar" class="progress-bar"></div>
            </div>
            <div class="progress-percentage">Progress: <span id="progress-percentage">0%</span></div>
        </div>

        <div class="id-card-actions">
            <button class="id-card-action-btn id-card-approve" disabled>
                <i class="fa fa-spinner fa-spin"></i> LOADING...</button>
            <button class="id-card-action-btn id-card-modify" disabled>
                <i class="fa fa-spinner fa-spin"></i> LOADING...</button>
            <button class="id-card-action-btn id-card-remove" disabled>
                <i class="fa fa-spinner fa-spin"></i> LOADING...</button>

        </div>

        <!-- New: Custom Loading Overlay for Approval Progress -->
        <div id="customApprovalLoadingOverlay" style="display:none;">
            <div class="custom-approval-loading-bg"></div>
            <div class="custom-approval-loading-content">
                <div class="custom-approval-title">Processing your ID card</div>
                <div class="custom-approval-desc">We're working on your ID card! Just hang tight for a bit...</div>
                <div class="custom-approval-progress-img">
                    <!-- Circular progress bar replaces the image -->
                    <div id="customApprovalProgressCircle" class="circular-progress">
                        <svg viewBox="0 0 100 100" width="120" height="120">
                            <circle class="progress-bg" cx="50" cy="50" r="45"/>
                            <circle class="progress-bar" cx="50" cy="50" r="45"/>
                        </svg>
                        <div class="custom-approval-progress-text" id="customApprovalProgressText">0%</div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<style>


/* Enhanced entity table container */
.entity-table-container {
    height: 350px;
    overflow: scroll;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    position: relative;
}

/* Ensure table takes full width */
.entity-table-container .entity-table {
    width: 100%;
    margin-bottom: 0;
}



/* Improved scroll indicators */
.entity-table-container::-webkit-scrollbar {
    width: 8px;
}

.entity-table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.entity-table-container::-webkit-scrollbar-thumb {
    background: #007bff;
    border-radius: 4px;
}

.entity-table-container::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}



/* Loading progress indicator */
.loading-progress {
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    display: none;
}

/* Smooth scrolling for better UX */
.entity-table-container {
    scroll-behavior: smooth;
}

/* Enhanced table styles for better performance */
.entity-table {
    margin-bottom: 0;
}

.entity-table tbody tr {
    transition: background-color 0.2s ease;
}

.entity-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Status badge improvements */
.status-badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Enhanced Loading Overlay */
#enhanced-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(2px);
}

.enhanced-loading-content {
    text-align: center;
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    width: 90%;
}

.loading-animation {
    margin-bottom: 20px;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
    color: #007bff;
}

#loading-title {
    color: #333;
    margin-bottom: 10px;
    font-weight: 600;
}

#loading-message {
    color: #666;
    margin-bottom: 20px;
    font-size: 14px;
}

.loading-progress-bar {
    margin-top: 20px;
}

.loading-progress-bar .progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.loading-progress-bar .progress-bar {
    background: linear-gradient(90deg, #007bff, #0056b3);
    transition: width 0.3s ease;
}

#loading-stats {
    display: block;
    margin-top: 8px;
    font-size: 12px;
}

/* Inline Loading Progress */
.loading-progress-inline {
    margin-top: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.progress-bar-container {
    width: 100%;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 5px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    transition: width 0.3s ease;
    border-radius: 3px;
}

#progress-text {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

/* Notification Container */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 350px;
}

.notification {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    padding: 16px;
    border-left: 4px solid #007bff;
    animation: slideInRight 0.3s ease;
    position: relative;
}

.notification.success {
    border-left-color: #28a745;
}

.notification.error {
    border-left-color: #dc3545;
}

.notification.warning {
    border-left-color: #ffc107;
}

.notification .notification-title {
    font-weight: 600;
    margin-bottom: 4px;
    color: #333;
}

.notification .notification-message {
    font-size: 14px;
    color: #666;
    margin: 0;
}

.notification .notification-close {
    position: absolute;
    top: 8px;
    right: 12px;
    background: none;
    border: none;
    font-size: 18px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification .notification-close:hover {
    color: #666;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive improvements */
@media (max-width: 768px) {
    .entity-table-container {
        max-height: 400px;
    }

    .chunk-loading-indicator {
        padding: 15px;
    }

    .loading-spinner {
        font-size: 12px;
    }

    .enhanced-loading-content {
        padding: 30px 20px;
    }

    .notification-container {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}
</style>

<script>
    let order_id='<?php echo $order_id ?>';
    let template_id='<?php echo $template ?>';
    let id_card_for='<?= $order_details->id_card_for ?>';
    let order_status = '<?= $order_details->status ?>';
    let selectedStatus = 'all'; // Add this variable at the top level

    // Face API Models Loading
    let faceApiModelsLoaded = false;
    // Check if order is in a locked state
    const isOrderLocked = ['order_submitted', 'in_printing', 'delivered'].includes(order_status);

    // Show locked order banner if order is in a locked state
    if (isOrderLocked) {
        // Set the appropriate message based on order status
        let lockReason = '';
        let lockIcon = '';

        switch (order_status) {
            case 'order_submitted':
                lockReason = 'This order has been submitted for payment processing. No further changes can be made to ID cards at this stage.';
                lockIcon = 'fa-credit-card';
                break;
            case 'in_printing':
                lockReason = 'This order is currently in the printing process. ID cards cannot be modified once printing has begun.';
                lockIcon = 'fa-print';
                break;
            case 'delivered':
                lockReason = 'This order has been delivered. ID cards cannot be modified after delivery.';
                lockIcon = 'fa-check-circle';
                break;
            default:
                lockReason = 'This order\'s status prevents making any changes to ID cards.';
                lockIcon = 'fa-lock';
        }

        // Update the banner content
        $('#lock-reason').text(lockReason);
        $('.lock-icon i').removeClass('fa-lock').addClass(lockIcon);

        // Show the banner
        $('#locked-order-banner').show();
    }

    // Static field mappings
    const FIELD_MAPPINGS = [
        { field: '[[NAME]]', key: 'name' },
        { field: '[[ID]]', key: 'employee_code' },
        { field: '[[DEPARTMENT]]', key: 'department' },
        { field: '[[DESIGNATION]]', key: 'designation' },
        { field: '[[CONTACT]]', key: 'contact' },
        { field: '[[PARENT_NAME]]', key: 'parent_name' },
        { field: '[[BLOOD_GROUP]]', key: 'blood_group' },
        { field: '[[EMERGENCY_CONTACT]]', key: 'emergency_contact' },
        { field: '[[DATE_OF_BIRTH]]', key: 'dob' },
        { field: '[[STUDENT_ADDRESS]]', key: 'address' },
        { field: '[[FATHER_ADDRESS]]', key: 'father_address' },
        { field: '[[MOTHER_ADDRESS]]', key: 'mother_address' },
        { field: '[[FATHER_PHOTO]]', key: 'father_photo' },
        { field: '[[MOTHER_PHOTO]]', key: 'mother_photo' },
        { field: '[[GRADE]]', key: 'grade' },
        { field: '[[GRADE_SECTION]]', key: 'grade_section' },
        { field: '[[QR_CODE]]', key: 'qr_code' },
        { field: '[[BAR_CODE]]', key: 'bar_code' },
        { field: '[[SIGNATURE]]', key: 'signature' },
        { field: '[[LOGO]]', key: 'logo' },
        { field: '[[ADDRESS]]', key: 'address' },
        { field: '[[EMAIL]]', key: 'email' },
        { field: '[[PHONE]]', key: 'phone' },
        { field: '[[WEBSITE]]', key: 'website' },
        { field: '[[SOCIAL_MEDIA]]', key: 'social_media' },
        { field: '[[QR_CODE_URL]]', key: 'qr_code_url' },
        { field: '[[BAR_CODE_URL]]', key: 'bar_code_url' },
        { field: '[[SIGNATURE_URL]]', key: 'signature_url' },
        { field: '[[LOGO_URL]]', key: 'logo_url' },
        { field: '[[ADDRESS_URL]]', key: 'address_url' },
        { field: '[[EMAIL_URL]]', key: 'email_url' },
        { field: '[[PHONE_URL]]', key: 'phone_url' },
        { field: '[[WEBSITE_URL]]', key: 'website_url' },
        { field: '[[SOCIAL_MEDIA_URL]]', key: 'social_media_url' },
        { field: '[[COMBINATION]]', key: 'combination' },
        { field: '[[ENROLLMENT_NO]]', key: 'enrollment_no' },
        { field: '[[ALPHA_ROLL_NO]]', key: 'alpha_rollnum' },
        { field: '[[ADMISSION_NO]]', key: 'admission_no' },
        { field: '[[RELATION_TYPE]]', key: 'relation_type' },
        { field: '[[SIBLING_NAME]]', key: 'sibling_name' },
        { field: '[[NAME_CLASS]]', key: 'name_class' },
        { field: '[[STUDENT_PICKING_ROUTE]]', key: 'picking_route' },
        { field: '[[STUDENT_DROPPING_ROUTE]]', key: 'dropping_route' },
        { field: '[[SPOUSE_NAME]]', key: 'spouse_name' },
        { field: '[[STAFF_TYPE]]', key: 'staff_type' },
        { field: '[[QUALIFICATION]]', key: 'qualification' },
        { field: '[[CLASS_SECTION]]', key: 'class_section' },
        { field: '[[ADISSION_ACADEMIC_YEAR]]', key: 'admission_acad_year' },
        { field: '[[IDENTIFICATION_CODE]]', key: 'identification_code' },

    ];

    // Helper function to check if face detection is ready
    function isFaceDetectionReady() {
        return typeof faceapi !== 'undefined' &&
               faceApiModelsLoaded &&
               faceapi.nets.tinyFaceDetector.isLoaded;
    }

    // Enhanced AI-powered face detection function with gap detection
async function applyFaceDetection(photoId, photoUrl) {
    if (!photoUrl) {
        return;
    }

    const img = document.getElementById(photoId);
    if (!img) {
        console.warn('Face detection: Image element not found:', photoId);
        return;
    }

    // Set default positioning first
    $(img).css({
        'object-position': 'center top',
        'object-fit': 'cover'
    });

    // Apply photo quality enhancement for better 300 DPI output
    if (img.complete && img.naturalWidth > 0) {
        enhancePhotoQuality(img);
    } else {
        img.onload = function() {
            enhancePhotoQuality(img);
        };
    }

    // Skip face detection for external URLs or invalid URLs
    if (photoUrl.includes('wasabisys.com') || !isValidUrl(photoUrl)) {
        // Still apply gap detection for external images
        setTimeout(() => detectPhotoGapsAndAlign(img, photoUrl), 100);
        return;
    }

    // Special handling for local test images
    if (photoUrl.includes('1.jpeg') || photoUrl.includes('2.jpeg')) {
        console.log('Applying enhanced detection for test image:', photoUrl);
        setTimeout(() => detectPhotoGapsAndAlign(img, photoUrl), 100);
        return;
    }

    // Check if Face API is ready for use
    if (!isFaceDetectionReady()) {
        // If models are still loading, retry after a delay
        if (typeof faceapi !== 'undefined' && !faceApiModelsLoaded) {
            setTimeout(() => applyFaceDetection(photoId, photoUrl), 2000);
            return;
        }
        // Use enhanced fallback positioning with gap detection
        detectPhotoGapsAndAlign(img, photoUrl);
        return;
    }

    img.crossOrigin = "anonymous";

    // Wait for image to load before processing
    if (!img.complete) {
        img.onload = function() {
            performAdvancedFaceDetection(img, photoUrl);
        };
        img.onerror = function() {
            // Image load error, using gap detection fallback
            detectPhotoGapsAndAlign(img, photoUrl);
        };
    } else {
        performAdvancedFaceDetection(img, photoUrl);
    }
}

// Enhanced face detection with gap analysis
async function performAdvancedFaceDetection(img, photoUrl) {
    try {
        // Create a processing image for face detection
        const processImg = new Image();
        processImg.crossOrigin = "anonymous";

        // Wait for processing image to load
        await new Promise((resolve, reject) => {
            processImg.onload = resolve;
            processImg.onerror = () => {
                console.error('Error loading image for face detection');
                reject(new Error('Image load failed'));
            };
            processImg.src = photoUrl;
        });

        // Detect faces in the image
        const detections = await faceapi.detectAllFaces(
            processImg,
            new faceapi.TinyFaceDetectorOptions({ scoreThreshold: 0.3 })
        );

        if (detections && detections.length > 0) {
            // Get the first (most confident) face detected
            const face = detections[0];
            const box = face.box;

            // Get image dimensions
            const imgWidth = processImg.naturalWidth || processImg.width;
            const imgHeight = processImg.naturalHeight || processImg.height;

            if (imgWidth === 0 || imgHeight === 0) {
                console.warn('Image has zero dimensions, using fallback positioning');
                detectPhotoGapsAndAlign(img, photoUrl);
                return;
            }

            // Analyze gaps around the detected face
            const gapAnalysis = analyzePhotoGaps(processImg, box, imgWidth, imgHeight);

            // Apply intelligent positioning based on face detection and gap analysis
            const positioning = calculateOptimalPositioning(box, gapAnalysis, imgWidth, imgHeight);

            // Preserve existing object-fit, only update object-position
            const currentObjectFit = $(img).css('object-fit') || getComputedStyle(img).objectFit;
            $(img).css({
                'object-position': `${positioning.x}% ${positioning.y}%`,
                'object-fit': currentObjectFit === 'initial' || currentObjectFit === 'auto' ? 'cover' : currentObjectFit
            });

            console.log('Advanced face positioning applied:', positioning);

        } else {
            // No face detected, use gap detection fallback
            detectPhotoGapsAndAlign(img, photoUrl);
        }

    } catch (error) {
        console.error('Error in advanced face detection:', error);
        // Fallback to gap detection
        detectPhotoGapsAndAlign(img, photoUrl);
    }
}

// Enhanced AI-powered photo border detection and alignment function
function detectPhotoGapsAndAlign(img, photoUrl) {
    try {
        if (!img.naturalWidth || !img.naturalHeight) {
            // Wait for image to load if dimensions not available
            if (!img.complete) {
                img.onload = function() {
                    detectPhotoGapsAndAlign(img, photoUrl);
                };
                return;
            }
        }

        // Create canvas for advanced border analysis
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Set canvas size to match image
        canvas.width = img.naturalWidth || img.width;
        canvas.height = img.naturalHeight || img.height;

        // Draw image to canvas for pixel analysis
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        // Perform comprehensive border and content analysis
        const borderAnalysis = performAdvancedBorderDetection(canvas, ctx);

        // Apply AI-powered photo alignment based on border detection
        const positioning = calculateAIPhotoAlignment(img, borderAnalysis, canvas.width, canvas.height);

        // Apply enhanced object-fit and positioning
        applyOptimalPhotoPositioning(img, positioning, borderAnalysis);

        // Enhanced logging and visual feedback
        logPhotoDetectionResults(borderAnalysis, positioning, canvas.width, canvas.height);

        // Add visual debugging overlay if debug mode is enabled
        if (window.location.search.includes('debug=1')) {
            addVisualDebugOverlay(img, borderAnalysis, positioning);
        }

    } catch (error) {
        console.error('Error in AI border detection, using enhanced fallback:', error);
        // Enhanced fallback with basic border detection
        applyEnhancedFallbackPositioning(img);
    }
}

// Advanced border detection using AI-like analysis
function performAdvancedBorderDetection(canvas, ctx) {
    const width = canvas.width;
    const height = canvas.height;

    // Multi-layer analysis for border detection
    const edgeAnalysis = analyzeImageEdges(ctx, width, height);
    const contentAnalysis = detectPhotoContent(ctx, width, height);
    const borderAnalysis = detectPhotoBorders(ctx, width, height);

    // Determine photo type and alignment issues
    const photoType = classifyPhotoType(edgeAnalysis, contentAnalysis, width, height);
    const alignmentIssues = detectAlignmentIssues(edgeAnalysis, contentAnalysis, borderAnalysis);

    return {
        hasBorder: borderAnalysis.detected,
        borderThickness: borderAnalysis.thickness,
        contentBounds: contentAnalysis.bounds,
        photoType: photoType,
        alignmentIssues: alignmentIssues,
        edgeGaps: edgeAnalysis,
        contentCenter: contentAnalysis.center,
        qualityScore: contentAnalysis.quality
    };
}


// function applyFaceDetection(photoId, photoUrl) {
//     if (!photoUrl) {
//         return;
//     }

//     const img = document.getElementById(photoId);
//     if (!img) {
//         return;
//     }

//     $(img).css({
//         'object-position': 'center top'
//     });


//     if (photoUrl.includes('wasabisys.com') || !isValidUrl(photoUrl)) {
//         return;
//     }


//     img.crossOrigin = "anonymous";


//     if (!img.complete) {
//         img.onload = function() {
//             centerFaceInImage(img);
//         };
//         img.onerror = function() {
//             // console.log('Image load error, using default positioning');
//         };
//     } else {

//         centerFaceInImage(img);
//     }
// }


// function centerFaceInImage(img) {
//     try {

//         if (!img.naturalWidth || !img.naturalHeight) {
//             return;
//         }


//         if (img.naturalHeight > img.naturalWidth) {
//             $(img).css({
//                 'object-position': 'center 25%',
//                 'object-fit': 'fill'
//             });
//         } else {

//             $(img).css({
//                 'object-position': 'center 35%',
//                 'object-fit': 'fill'
//             });
//         }
//     } catch (error) {
//         // console.log('Error in centerFaceInImage:', error);
//     }
// }


// Analyze gaps around detected face for optimal positioning
function analyzePhotoGaps(processImg, faceBox, imgWidth, imgHeight) {
    try {
        // Calculate gaps around the face
        const topGap = faceBox.y;
        const bottomGap = imgHeight - (faceBox.y + faceBox.height);
        const leftGap = faceBox.x;
        const rightGap = imgWidth - (faceBox.x + faceBox.width);

        // Calculate face center
        const faceCenterX = faceBox.x + (faceBox.width / 2);
        const faceCenterY = faceBox.y + (faceBox.height / 2);

        return {
            top: topGap,
            bottom: bottomGap,
            left: leftGap,
            right: rightGap,
            faceCenterX: faceCenterX,
            faceCenterY: faceCenterY,
            faceWidth: faceBox.width,
            faceHeight: faceBox.height,
            totalWidth: imgWidth,
            totalHeight: imgHeight
        };
    } catch (error) {
        console.error('Error analyzing photo gaps:', error);
        return null;
    }
}

// Calculate optimal positioning based on face detection and gap analysis
function calculateOptimalPositioning(faceBox, gapAnalysis, imgWidth, imgHeight) {
    if (!gapAnalysis) {
        // Fallback to center positioning
        return { x: 50, y: 50 };
    }

    // Calculate face center as percentage
    let faceXPercent = (gapAnalysis.faceCenterX / imgWidth) * 100;
    let faceYPercent = (gapAnalysis.faceCenterY / imgHeight) * 100;

    // Adjust positioning based on gap analysis
    // If there's more space on one side, shift slightly towards center
    const leftRightRatio = gapAnalysis.left / (gapAnalysis.left + gapAnalysis.right);
    const topBottomRatio = gapAnalysis.top / (gapAnalysis.top + gapAnalysis.bottom);

    // Fine-tune horizontal positioning
    if (leftRightRatio < 0.3) {
        // Face is too far left, shift right slightly
        faceXPercent = Math.min(90, faceXPercent + 5);
    } else if (leftRightRatio > 0.7) {
        // Face is too far right, shift left slightly
        faceXPercent = Math.max(10, faceXPercent - 5);
    }

    // Fine-tune vertical positioning (prefer showing more of the top)
    if (topBottomRatio < 0.2) {
        // Face is too high, shift down slightly
        faceYPercent = Math.min(85, faceYPercent + 5);
    } else if (topBottomRatio > 0.6) {
        // Face is too low, shift up
        faceYPercent = Math.max(15, faceYPercent - 10);
    }

    // Ensure positioning stays within reasonable bounds
    faceXPercent = Math.max(10, Math.min(90, faceXPercent));
    faceYPercent = Math.max(15, Math.min(85, faceYPercent));

    return {
        x: Math.round(faceXPercent),
        y: Math.round(faceYPercent)
    };
}

// Advanced edge analysis for AI border detection
function analyzeImageEdges(ctx, width, height) {
    try {
        // Dynamic edge thickness based on image size
        const edgeThickness = Math.max(10, Math.min(30, Math.floor(Math.min(width, height) * 0.08)));

        // Multi-point edge sampling for better accuracy
        const topGap = analyzeAdvancedEdgeGap(ctx, 0, 0, width, edgeThickness, 'horizontal');
        const bottomGap = analyzeAdvancedEdgeGap(ctx, 0, height - edgeThickness, width, edgeThickness, 'horizontal');
        const leftGap = analyzeAdvancedEdgeGap(ctx, 0, 0, edgeThickness, height, 'vertical');
        const rightGap = analyzeAdvancedEdgeGap(ctx, width - edgeThickness, 0, edgeThickness, height, 'vertical');

        // Calculate edge uniformity and detect borders
        const edgeUniformity = calculateEdgeUniformity(ctx, width, height, edgeThickness);

        return {
            top: topGap,
            bottom: bottomGap,
            left: leftGap,
            right: rightGap,
            uniformity: edgeUniformity,
            thickness: edgeThickness
        };
    } catch (error) {
        console.error('Error analyzing image edges:', error);
        return { top: 0, bottom: 0, left: 0, right: 0, uniformity: 0, thickness: 10 };
    }
}

// Enhanced photo content detection
function detectPhotoContent(ctx, width, height) {
    try {
        // Grid-based content analysis
        const gridSize = 12;
        const cellWidth = width / gridSize;
        const cellHeight = height / gridSize;

        let contentBounds = { top: height, bottom: 0, left: width, right: 0 };
        let totalContentScore = 0;
        let contentCells = 0;

        // Analyze each grid cell for content
        for (let row = 0; row < gridSize; row++) {
            for (let col = 0; col < gridSize; col++) {
                const x = col * cellWidth;
                const y = row * cellHeight;

                const cellScore = analyzeCellContent(ctx, x, y, cellWidth, cellHeight);

                // If cell has significant content, update bounds
                if (cellScore > 0.3) {
                    contentBounds.top = Math.min(contentBounds.top, y);
                    contentBounds.bottom = Math.max(contentBounds.bottom, y + cellHeight);
                    contentBounds.left = Math.min(contentBounds.left, x);
                    contentBounds.right = Math.max(contentBounds.right, x + cellWidth);

                    totalContentScore += cellScore;
                    contentCells++;
                }
            }
        }

        // Calculate content center
        const centerX = (contentBounds.left + contentBounds.right) / 2;
        const centerY = (contentBounds.top + contentBounds.bottom) / 2;

        return {
            bounds: contentBounds,
            center: { x: centerX, y: centerY },
            quality: contentCells > 0 ? totalContentScore / contentCells : 0
        };
    } catch (error) {
        console.error('Error detecting photo content:', error);
        return {
            bounds: { top: 0, bottom: height, left: 0, right: width },
            center: { x: width / 2, y: height / 2 },
            quality: 0.5
        };
    }
}

// Advanced edge gap analysis with multiple sampling points
function analyzeAdvancedEdgeGap(ctx, x, y, width, height, direction) {
    try {
        const imageData = ctx.getImageData(x, y, width, height);
        const data = imageData.data;

        let uniformPixels = 0;
        let totalPixels = 0;
        let colorVariance = 0;
        const threshold = 35;

        // Enhanced sampling with color variance analysis
        for (let i = 0; i < data.length; i += 12) { // More frequent sampling
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];

            const brightness = (r + g + b) / 3;
            const variance = Math.abs(r - brightness) + Math.abs(g - brightness) + Math.abs(b - brightness);

            colorVariance += variance;

            // Enhanced background detection (white, light gray, or uniform colors)
            if ((brightness > 190 && variance < threshold) ||
                (variance < 15 && brightness > 150)) {
                uniformPixels++;
            }
            totalPixels++;
        }

        const gapRatio = totalPixels > 0 ? uniformPixels / totalPixels : 0;
        const avgVariance = totalPixels > 0 ? colorVariance / totalPixels : 0;

        // Return enhanced gap analysis
        return {
            ratio: gapRatio,
            variance: avgVariance,
            isUniform: gapRatio > 0.7 && avgVariance < 20
        };
    } catch (error) {
        return { ratio: 0, variance: 0, isUniform: false };
    }
}

// Detect photo borders and frames
function detectPhotoBorders(ctx, width, height) {
    try {
        const borderScanWidth = Math.min(15, Math.floor(width * 0.05));
        const borderScanHeight = Math.min(15, Math.floor(height * 0.05));

        // Scan for consistent border patterns
        const topBorder = scanForBorder(ctx, 0, 0, width, borderScanHeight, 'horizontal');
        const bottomBorder = scanForBorder(ctx, 0, height - borderScanHeight, width, borderScanHeight, 'horizontal');
        const leftBorder = scanForBorder(ctx, 0, 0, borderScanWidth, height, 'vertical');
        const rightBorder = scanForBorder(ctx, width - borderScanWidth, 0, borderScanWidth, height, 'vertical');

        const borderCount = [topBorder, bottomBorder, leftBorder, rightBorder].filter(b => b.detected).length;

        return {
            detected: borderCount >= 2,
            thickness: Math.max(topBorder.thickness, bottomBorder.thickness, leftBorder.thickness, rightBorder.thickness),
            borders: { top: topBorder, bottom: bottomBorder, left: leftBorder, right: rightBorder }
        };
    } catch (error) {
        return { detected: false, thickness: 0, borders: {} };
    }
}

// Classify photo type based on analysis
function classifyPhotoType(edgeAnalysis, contentAnalysis, width, height) {
    const aspectRatio = width / height;
    const contentWidth = contentAnalysis.bounds.right - contentAnalysis.bounds.left;
    const contentHeight = contentAnalysis.bounds.bottom - contentAnalysis.bounds.top;
    const contentRatio = contentWidth / contentHeight;

    // Determine photo characteristics
    if (edgeAnalysis.top.ratio > 0.8 || edgeAnalysis.bottom.ratio > 0.8 ||
        edgeAnalysis.left.ratio > 0.8 || edgeAnalysis.right.ratio > 0.8) {
        return 'scanned_document'; // Scanned photo with white borders
    } else if (contentRatio > 1.2) {
        return 'landscape'; // Wide photo
    } else if (contentRatio < 0.8) {
        return 'portrait'; // Tall photo
    } else if (contentAnalysis.quality > 0.7) {
        return 'closeup'; // Close-up photo
    } else {
        return 'standard'; // Standard photo
    }
}

// Detect specific alignment issues
function detectAlignmentIssues(edgeAnalysis, contentAnalysis, borderAnalysis) {
    const issues = [];

    // Check for off-center horizontal alignment
    const centerX = contentAnalysis.center.x;
    const imageWidth = contentAnalysis.bounds.right;
    const horizontalOffset = Math.abs(centerX - imageWidth / 2) / (imageWidth / 2);

    if (horizontalOffset > 0.2) {
        issues.push(centerX < imageWidth / 2 ? 'left_aligned' : 'right_aligned');
    }

    // Check for off-center vertical alignment
    const centerY = contentAnalysis.center.y;
    const imageHeight = contentAnalysis.bounds.bottom;
    const verticalOffset = Math.abs(centerY - imageHeight / 2) / (imageHeight / 2);

    if (verticalOffset > 0.2) {
        issues.push(centerY < imageHeight / 2 ? 'top_aligned' : 'bottom_aligned');
    }

    // Check for excessive margins
    if (edgeAnalysis.top.ratio > 0.6) issues.push('top_margin');
    if (edgeAnalysis.bottom.ratio > 0.6) issues.push('bottom_margin');
    if (edgeAnalysis.left.ratio > 0.6) issues.push('left_margin');
    if (edgeAnalysis.right.ratio > 0.6) issues.push('right_margin');

    // Check for closeup issues
    if (contentAnalysis.quality > 0.8 && (edgeAnalysis.top.ratio < 0.1 && edgeAnalysis.bottom.ratio < 0.1)) {
        issues.push('too_closeup');
    }

    return issues;
}

// Calculate AI-powered photo alignment
function calculateAIPhotoAlignment(img, borderAnalysis, width, height) {
    const { photoType, alignmentIssues, contentBounds, contentCenter } = borderAnalysis;

    let positioning = { x: 50, y: 50 }; // Default center

    // Apply photo type specific adjustments
    switch (photoType) {
        case 'scanned_document':
            positioning = handleScannedDocumentAlignment(alignmentIssues, contentBounds, width, height);
            break;
        case 'closeup':
            positioning = handleCloseupAlignment(alignmentIssues, contentCenter, width, height);
            break;
        case 'portrait':
            positioning = handlePortraitAlignment(alignmentIssues, contentBounds, width, height);
            break;
        case 'landscape':
            positioning = handleLandscapeAlignment(alignmentIssues, contentBounds, width, height);
            break;
        default:
            positioning = handleStandardAlignment(alignmentIssues, contentCenter, width, height);
    }

    // Apply specific issue corrections
    positioning = applyAlignmentCorrections(positioning, alignmentIssues);

    return positioning;
}

// Calculate content score for a cell based on contrast and detail
function calculateCellContentScore(ctx, x, y, width, height) {
    try {
        const imageData = ctx.getImageData(x, y, width, height);
        const data = imageData.data;

        let totalVariance = 0;
        let pixelCount = 0;

        // Calculate variance in the cell (higher variance = more content)
        for (let i = 0; i < data.length; i += 12) { // Sample pixels
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];

            if (i + 12 < data.length) {
                const nextR = data[i + 12];
                const nextG = data[i + 13];
                const nextB = data[i + 14];

                const variance = Math.abs(r - nextR) + Math.abs(g - nextG) + Math.abs(b - nextB);
                totalVariance += variance;
                pixelCount++;
            }
        }

        return pixelCount > 0 ? totalVariance / pixelCount : 0;
    } catch (error) {
        return 0;
    }
}

// Calculate positioning based on gap analysis
function calculatePositioningFromGaps(gapAnalysis, imgWidth, imgHeight) {
    if (!gapAnalysis) {
        return { x: 50, y: 50 };
    }

    // Convert content center to percentage
    let centerXPercent = (gapAnalysis.contentCenterX / imgWidth) * 100;
    let centerYPercent = (gapAnalysis.contentCenterY / imgHeight) * 100;

    // Adjust based on detected gaps
    // If there are significant gaps on edges, adjust positioning to compensate

    // Horizontal adjustment
    const leftGapRatio = gapAnalysis.left;
    const rightGapRatio = gapAnalysis.right;

    if (leftGapRatio > 0.7) {
        // Significant left gap, shift content right
        centerXPercent = Math.min(90, centerXPercent + 15);
    } else if (rightGapRatio > 0.7) {
        // Significant right gap, shift content left
        centerXPercent = Math.max(10, centerXPercent - 15);
    }

    // Vertical adjustment
    const topGapRatio = gapAnalysis.top;
    const bottomGapRatio = gapAnalysis.bottom;

    if (topGapRatio > 0.7) {
        // Significant top gap, shift content down
        centerYPercent = Math.min(85, centerYPercent + 15);
    } else if (bottomGapRatio > 0.7) {
        // Significant bottom gap, shift content up
        centerYPercent = Math.max(15, centerYPercent - 15);
    }

    // For portrait photos, prefer showing the upper portion
    if (imgHeight > imgWidth) {
        centerYPercent = Math.max(15, centerYPercent - 10);
    }

    // Ensure positioning stays within reasonable bounds
    centerXPercent = Math.max(10, Math.min(90, centerXPercent));
    centerYPercent = Math.max(15, Math.min(85, centerYPercent));

    return {
        x: Math.round(centerXPercent),
        y: Math.round(centerYPercent)
    };
}

// Enhanced photo alignment for common issues
function handleCommonPhotoAlignmentIssues(img, gapAnalysis) {
    if (!gapAnalysis) return null;

    const { top, bottom, left, right, totalWidth, totalHeight } = gapAnalysis;

    // Detect common alignment patterns
    let adjustedPositioning = null;

    // Pattern 1: Photo has significant top margin (common in scanned documents)
    if (top > 0.6 && bottom < 0.3) {
        console.log('Detected top-heavy photo, adjusting downward');
        adjustedPositioning = { x: 50, y: 70 }; // Move down to show more content
    }

    // Pattern 2: Photo is left-aligned with right margin
    else if (left < 0.2 && right > 0.6) {
        console.log('Detected left-aligned photo, centering horizontally');
        adjustedPositioning = { x: 65, y: 40 }; // Move right and up slightly
    }

    // Pattern 3: Photo is right-aligned with left margin
    else if (right < 0.2 && left > 0.6) {
        console.log('Detected right-aligned photo, centering horizontally');
        adjustedPositioning = { x: 35, y: 40 }; // Move left and up slightly
    }

    // Pattern 4: Photo has bottom margin (person cut off at bottom)
    else if (bottom > 0.5 && top < 0.3) {
        console.log('Detected bottom margin, adjusting upward');
        adjustedPositioning = { x: 50, y: 30 }; // Move up to show more content
    }

    // Pattern 5: Centered but with uniform margins (needs fine-tuning)
    else if (Math.abs(left - right) < 0.2 && Math.abs(top - bottom) < 0.2 && top > 0.3) {
        console.log('Detected centered photo with margins, optimizing for portrait');
        adjustedPositioning = { x: 50, y: 35 }; // Slightly higher for better portrait framing
    }

    return adjustedPositioning;
}

// Handle scanned document alignment (photos with white borders)
function handleScannedDocumentAlignment(issues, contentBounds, width, height) {
    const contentCenterX = (contentBounds.left + contentBounds.right) / 2;
    const contentCenterY = (contentBounds.top + contentBounds.bottom) / 2;

    // Convert to percentage positioning
    let x = (contentCenterX / width) * 100;
    let y = (contentCenterY / height) * 100;

    // Adjust for typical scanned document issues
    if (issues.includes('top_margin')) y = Math.max(y - 15, 25);
    if (issues.includes('left_margin')) x = Math.max(x - 10, 30);
    if (issues.includes('right_margin')) x = Math.min(x + 10, 70);

    return { x: Math.round(x), y: Math.round(y) };
}

// Handle closeup photo alignment
function handleCloseupAlignment(issues, contentCenter, width, height) {
    let x = 50, y = 45; // Slightly higher for better face positioning

    if (issues.includes('too_closeup')) {
        y = 40; // Move up to show more of the face
    }
    if (issues.includes('left_aligned')) {
        x = 60; // Move right to center
    }
    if (issues.includes('right_aligned')) {
        x = 40; // Move left to center
    }

    return { x, y };
}

// Handle portrait photo alignment
function handlePortraitAlignment(issues, contentBounds, width, height) {
    let x = 50, y = 45;

    // For portrait photos, focus on upper portion
    const contentHeight = contentBounds.bottom - contentBounds.top;
    if (contentHeight > height * 0.8) {
        y = 35; // Show more of the upper body/face
    }

    if (issues.includes('left_aligned')) x = 55;
    if (issues.includes('right_aligned')) x = 45;

    return { x, y };
}

// Handle landscape photo alignment
function handleLandscapeAlignment(issues, contentBounds, width, height) {
    let x = 50, y = 50;

    // For landscape photos, center more precisely
    if (issues.includes('top_aligned')) y = 60;
    if (issues.includes('bottom_aligned')) y = 40;

    return { x, y };
}

// Handle standard photo alignment
function handleStandardAlignment(issues, contentCenter, width, height) {
    let x = (contentCenter.x / width) * 100;
    let y = (contentCenter.y / height) * 100;

    // Ensure reasonable bounds
    x = Math.max(30, Math.min(70, x));
    y = Math.max(25, Math.min(75, y));

    return { x: Math.round(x), y: Math.round(y) };
}

// Apply alignment corrections based on detected issues
function applyAlignmentCorrections(positioning, issues) {
    let { x, y } = positioning;

    // Correct specific alignment issues
    if (issues.includes('left_aligned')) {
        x = Math.min(x + 15, 70); // Move right
    }
    if (issues.includes('right_aligned')) {
        x = Math.max(x - 15, 30); // Move left
    }
    if (issues.includes('top_aligned')) {
        y = Math.min(y + 10, 75); // Move down
    }
    if (issues.includes('bottom_aligned')) {
        y = Math.max(y - 10, 25); // Move up
    }

    // Handle margin issues
    if (issues.includes('top_margin')) {
        y = Math.max(y - 20, 20); // Compensate for top margin
    }
    if (issues.includes('bottom_margin')) {
        y = Math.min(y + 15, 80); // Compensate for bottom margin
    }
    if (issues.includes('left_margin')) {
        x = Math.max(x - 15, 25); // Compensate for left margin
    }
    if (issues.includes('right_margin')) {
        x = Math.min(x + 15, 75); // Compensate for right margin
    }

    return { x: Math.round(x), y: Math.round(y) };
}

// Fallback function for basic face positioning when AI detection fails (maintained for compatibility)
function centerFaceInImage(img) {
    // Use the enhanced gap detection instead of basic centering
    detectPhotoGapsAndAlign(img, img.src);
}

// Apply optimal photo positioning with enhanced object-fit
function applyOptimalPhotoPositioning(img, positioning, borderAnalysis) {
    try {
        // Determine optimal object-fit based on photo type
        let objectFit = 'cover'; // Default

        if (borderAnalysis.photoType === 'scanned_document') {
            objectFit = 'contain'; // Preserve document borders
        } else if (borderAnalysis.photoType === 'closeup') {
            objectFit = 'cover'; // Ensure face fills the frame
        } else if (borderAnalysis.alignmentIssues.includes('too_closeup')) {
            objectFit = 'cover'; // Maintain coverage for closeups
        }

        // Apply positioning and fit
        $(img).css({
            'object-position': `${positioning.x}% ${positioning.y}%`,
            'object-fit': objectFit,
            'transition': 'object-position 0.3s ease-in-out' // Smooth transition
        });

        // Add visual feedback for debugging (remove in production)
        if (window.location.search.includes('debug=1')) {
            console.log('Applied positioning:', {
                position: `${positioning.x}% ${positioning.y}%`,
                objectFit: objectFit,
                photoType: borderAnalysis.photoType,
                issues: borderAnalysis.alignmentIssues
            });
        }

    } catch (error) {
        console.error('Error applying photo positioning:', error);
        // Fallback to basic positioning
        $(img).css({
            'object-position': 'center center',
            'object-fit': 'cover'
        });
    }
}

// Enhanced fallback positioning for error cases
function applyEnhancedFallbackPositioning(img) {
    try {
        // Basic analysis without canvas operations
        const imgWidth = img.naturalWidth || img.width;
        const imgHeight = img.naturalHeight || img.height;
        const aspectRatio = imgWidth / imgHeight;

        let positioning = { x: 50, y: 50 };

        // Simple heuristics based on aspect ratio
        if (aspectRatio > 1.3) {
            // Landscape - center horizontally, slightly up
            positioning = { x: 50, y: 45 };
        } else if (aspectRatio < 0.8) {
            // Portrait - center horizontally, focus on upper portion
            positioning = { x: 50, y: 40 };
        }

        $(img).css({
            'object-position': `${positioning.x}% ${positioning.y}%`,
            'object-fit': 'cover'
        });

    } catch (error) {
        console.error('Error in enhanced fallback:', error);
        $(img).css({
            'object-position': 'center center',
            'object-fit': 'cover'
        });
    }
}

// Helper function to analyze cell content
function analyzeCellContent(ctx, x, y, width, height) {
    try {
        const imageData = ctx.getImageData(x, y, width, height);
        const data = imageData.data;

        let contentScore = 0;
        let totalPixels = 0;

        // Analyze pixel variance and content density
        for (let i = 0; i < data.length; i += 16) { // Sample every 4th pixel
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];

            const brightness = (r + g + b) / 3;

            // Score based on non-background characteristics
            if (brightness < 220) { // Not pure white/light background
                contentScore += 1;

                // Bonus for skin tones and typical photo colors
                if (r > g && g > b && r > 100 && r < 200) {
                    contentScore += 0.5; // Likely skin tone
                }
            }

            totalPixels++;
        }

        return totalPixels > 0 ? contentScore / totalPixels : 0;
    } catch (error) {
        return 0;
    }
}

// Helper function to calculate edge uniformity
function calculateEdgeUniformity(ctx, width, height, edgeThickness) {
    try {
        const edges = [
            { x: 0, y: 0, w: width, h: edgeThickness }, // Top
            { x: 0, y: height - edgeThickness, w: width, h: edgeThickness }, // Bottom
            { x: 0, y: 0, w: edgeThickness, h: height }, // Left
            { x: width - edgeThickness, y: 0, w: edgeThickness, h: height } // Right
        ];

        let totalUniformity = 0;

        edges.forEach(edge => {
            const imageData = ctx.getImageData(edge.x, edge.y, edge.w, edge.h);
            const data = imageData.data;

            let uniformPixels = 0;
            let totalPixels = 0;

            for (let i = 0; i < data.length; i += 12) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];

                const brightness = (r + g + b) / 3;
                const variance = Math.abs(r - brightness) + Math.abs(g - brightness) + Math.abs(b - brightness);

                if (brightness > 200 && variance < 25) {
                    uniformPixels++;
                }
                totalPixels++;
            }

            totalUniformity += totalPixels > 0 ? uniformPixels / totalPixels : 0;
        });

        return totalUniformity / edges.length;
    } catch (error) {
        return 0;
    }
}

// Helper function to scan for borders
function scanForBorder(ctx, x, y, width, height, direction) {
    try {
        const imageData = ctx.getImageData(x, y, width, height);
        const data = imageData.data;

        let borderPixels = 0;
        let totalPixels = 0;
        let avgBrightness = 0;

        for (let i = 0; i < data.length; i += 16) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];

            const brightness = (r + g + b) / 3;
            avgBrightness += brightness;

            // Detect border-like pixels (very light or very dark)
            if (brightness > 230 || brightness < 30) {
                borderPixels++;
            }

            totalPixels++;
        }

        avgBrightness = totalPixels > 0 ? avgBrightness / totalPixels : 0;
        const borderRatio = totalPixels > 0 ? borderPixels / totalPixels : 0;

        return {
            detected: borderRatio > 0.8 && (avgBrightness > 200 || avgBrightness < 50),
            thickness: borderRatio > 0.8 ? (direction === 'horizontal' ? height : width) : 0
        };
    } catch (error) {
        return { detected: false, thickness: 0 };
    }
}

// Enhanced logging for photo detection results
function logPhotoDetectionResults(borderAnalysis, positioning, width, height) {
    const logData = {
        timestamp: new Date().toISOString(),
        imageSize: `${width}x${height}`,
        photoType: borderAnalysis.photoType,
        borderDetected: borderAnalysis.hasBorder,
        borderThickness: borderAnalysis.borderThickness,
        alignmentIssues: borderAnalysis.alignmentIssues,
        contentBounds: {
            width: borderAnalysis.contentBounds.right - borderAnalysis.contentBounds.left,
            height: borderAnalysis.contentBounds.bottom - borderAnalysis.contentBounds.top,
            centerX: borderAnalysis.contentCenter.x,
            centerY: borderAnalysis.contentCenter.y
        },
        appliedPositioning: positioning,
        qualityScore: borderAnalysis.qualityScore
    };

    console.log('🎯 AI Photo Detection Results:', logData);

    // Store detection history for analysis (limit to last 10 detections)
    if (!window.photoDetectionHistory) {
        window.photoDetectionHistory = [];
    }
    window.photoDetectionHistory.push(logData);
    if (window.photoDetectionHistory.length > 10) {
        window.photoDetectionHistory.shift();
    }
}

// Add visual debug overlay to show detection results
function addVisualDebugOverlay(img, borderAnalysis, positioning) {
    try {
        // Remove existing overlay
        const existingOverlay = img.parentElement.querySelector('.debug-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // Create debug overlay
        const overlay = document.createElement('div');
        overlay.className = 'debug-overlay';
        overlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            border: 2px solid #ff0000;
            background: rgba(255, 0, 0, 0.1);
            z-index: 1000;
        `;

        // Add content bounds indicator
        const contentIndicator = document.createElement('div');
        const bounds = borderAnalysis.contentBounds;
        const imgRect = img.getBoundingClientRect();
        const parentRect = img.parentElement.getBoundingClientRect();

        contentIndicator.style.cssText = `
            position: absolute;
            border: 2px solid #00ff00;
            background: rgba(0, 255, 0, 0.1);
            left: ${(bounds.left / img.naturalWidth) * 100}%;
            top: ${(bounds.top / img.naturalHeight) * 100}%;
            width: ${((bounds.right - bounds.left) / img.naturalWidth) * 100}%;
            height: ${((bounds.bottom - bounds.top) / img.naturalHeight) * 100}%;
        `;

        // Add positioning indicator
        const positionIndicator = document.createElement('div');
        positionIndicator.style.cssText = `
            position: absolute;
            width: 10px;
            height: 10px;
            background: #ffff00;
            border: 2px solid #000;
            border-radius: 50%;
            left: ${positioning.x}%;
            top: ${positioning.y}%;
            transform: translate(-50%, -50%);
        `;

        // Add info panel
        const infoPanel = document.createElement('div');
        infoPanel.style.cssText = `
            position: absolute;
            top: 5px;
            left: 5px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px;
            font-size: 10px;
            border-radius: 3px;
            max-width: 200px;
        `;
        infoPanel.innerHTML = `
            <div>Type: ${borderAnalysis.photoType}</div>
            <div>Position: ${positioning.x}%, ${positioning.y}%</div>
            <div>Issues: ${borderAnalysis.alignmentIssues.join(', ') || 'None'}</div>
            <div>Quality: ${(borderAnalysis.qualityScore * 100).toFixed(1)}%</div>
        `;

        overlay.appendChild(contentIndicator);
        overlay.appendChild(positionIndicator);
        overlay.appendChild(infoPanel);

        // Make parent element relative if not already
        if (getComputedStyle(img.parentElement).position === 'static') {
            img.parentElement.style.position = 'relative';
        }

        img.parentElement.appendChild(overlay);

        // Auto-remove overlay after 5 seconds
        setTimeout(() => {
            if (overlay.parentElement) {
                overlay.remove();
            }
        }, 5000);

    } catch (error) {
        console.error('Error adding debug overlay:', error);
    }
}

// Function to export detection history for analysis
function exportPhotoDetectionHistory() {
    if (window.photoDetectionHistory && window.photoDetectionHistory.length > 0) {
        const dataStr = JSON.stringify(window.photoDetectionHistory, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `photo-detection-history-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        console.log('Photo detection history exported');
    } else {
        console.log('No detection history available');
    }
}

// Test function for AI photo detection system
function testAIPhotoDetection() {
    console.log('🧪 Testing AI Photo Detection System...');

    // Find all photo elements on the page
    const photoElements = document.querySelectorAll('img[id*="photo"], .photo-in-circle, .high-quality-photo');

    if (photoElements.length === 0) {
        console.log('❌ No photo elements found for testing');
        return;
    }

    console.log(`📸 Found ${photoElements.length} photo elements to test`);

    // Test each photo element
    photoElements.forEach((img, index) => {
        if (img.src && img.complete) {
            console.log(`🔍 Testing photo ${index + 1}:`, img.src);

            // Force re-detection with debug mode
            const originalUrl = window.location.search;
            if (!originalUrl.includes('debug=1')) {
                window.history.replaceState({}, '', window.location.pathname + '?debug=1');
            }

            // Trigger detection
            setTimeout(() => {
                detectPhotoGapsAndAlign(img, img.src);
            }, index * 500); // Stagger tests
        } else {
            console.log(`⏳ Photo ${index + 1} not ready for testing`);
        }
    });

    // Restore original URL after testing
    setTimeout(() => {
        if (window.location.search.includes('debug=1')) {
            window.history.replaceState({}, '', window.location.pathname);
        }
    }, photoElements.length * 500 + 2000);
}

// Function to manually trigger photo detection for specific element
function manualPhotoDetection(photoId) {
    const img = document.getElementById(photoId);
    if (img && img.src) {
        console.log('🔧 Manual detection triggered for:', photoId);
        detectPhotoGapsAndAlign(img, img.src);
    } else {
        console.error('❌ Photo element not found or has no source:', photoId);
    }
}

// Add keyboard shortcut for testing (Ctrl+Shift+P)
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        e.preventDefault();
        testAIPhotoDetection();
    }
});

// Add console commands for debugging
window.photoDetectionDebug = {
    test: testAIPhotoDetection,
    manual: manualPhotoDetection,
    history: () => console.table(window.photoDetectionHistory || []),
    export: exportPhotoDetectionHistory,
    clear: () => { window.photoDetectionHistory = []; console.log('Detection history cleared'); }
};

console.log('🎯 AI Photo Detection System loaded. Use Ctrl+Shift+P to test or photoDetectionDebug.test() in console');

// Prevent Parsley.js errors on this page since we don't have forms that need validation
$(document).ready(function() {
    // Only initialize Parsley if there are forms that actually need it
    if ($('form[data-parsley-validate]').length > 0) {
        $('form[data-parsley-validate]').parsley();
    }

    // Prevent global Parsley auto-binding errors
    if (typeof window.Parsley !== 'undefined') {
        // Override the global auto-bind to prevent errors
        window.Parsley.options.excluded = 'input[type=button], input[type=submit], input[type=reset], input[type=hidden], [disabled], :hidden';
    }
});

function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

// Load Face API models
async function loadFaceApiModels() {
    try {
        await Promise.all([
            faceapi.nets.tinyFaceDetector.loadFromUri('<?= base_url("assets/models/models") ?>'),
            faceapi.nets.faceLandmark68Net.loadFromUri('<?= base_url("assets/models/models") ?>'),
            faceapi.nets.faceRecognitionNet.loadFromUri('<?= base_url("assets/models/models") ?>'),
            faceapi.nets.faceExpressionNet.loadFromUri('<?= base_url("assets/models/models") ?>'),
        ]);
        faceApiModelsLoaded = true;
        console.log('Face API Models Loaded Successfully');
        return true;
    } catch (error) {
        console.error('Error loading Face API models:', error);
        faceApiModelsLoaded = false;
        return false;
    }
}

const cropFaceFromPhoto = async (url) => {
    try {
        const img = await faceapi.fetchImage(url);

        const detection = await faceapi
            .detectSingleFace(img, new faceapi.TinyFaceDetectorOptions())
            .withFaceLandmarks();

        if (!detection) {
            console.warn('No face detected.');
            return url;
        }

        const { x, y, width, height } = detection.detection.box;

        const imgWidth = img.width;
        const imgHeight = img.height;

        // Desired padding ratios
        const padRatioX = 0.4; // 40% of face width
        const padRatioYTop = 0.55; // 50% of face height (above head)
        const padRatioYBottom = 0.4; // 40% of face height (below chin)

        // Max padding in pixels
        const maxPadX = width * padRatioX;
        const maxPadTop = height * padRatioYTop;
        const maxPadBottom = height * padRatioYBottom;

        // Calculate how much space is actually available
        const padX = Math.min(x, imgWidth - (x + width), maxPadX);
        const padTop = Math.min(y, maxPadTop);
        const padBottom = Math.min(imgHeight - (y + height), maxPadBottom);

        // Define crop box
        const cropX = x - padX;
        const cropY = y - padTop;
        const cropWidth = width + padX * 2;
        const cropHeight = height + padTop + padBottom;

        // Prepare canvas
        const canvas = document.createElement('canvas');
        canvas.width = cropWidth * 2;
        canvas.height = cropHeight * 2;

        const ctx = canvas.getContext('2d');

        ctx.save();
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        ctx.filter = 'contrast(1.1) brightness(1.05)';

        // Optional: circular mask
        ctx.save();
        ctx.beginPath();
        ctx.arc(canvas.width / 2, canvas.height / 2, Math.min(canvas.width, canvas.height) / 2, 0, Math.PI * 2);
        ctx.closePath();
        ctx.clip();

        // Draw face portion
        ctx.drawImage(
            img,
            cropX, cropY,
            cropWidth, cropHeight,
            0, 0,
            canvas.width, canvas.height
        );

        ctx.restore();

        return canvas.toDataURL('image/jpeg', 0.95);
    } catch (err) {
        console.error('Face cropping error:', err);
        return url;
    }
};





// Function to enhance canvas quality for 300 DPI output with advanced processing
function enhanceCanvasQuality(sourceCanvas) {
    try {
        // Create enhanced canvas with higher resolution for 300 DPI
        const enhancedCanvas = document.createElement('canvas');
        const scaleFactor = 2.5; // Increased scale for better 300 DPI quality

        enhancedCanvas.width = sourceCanvas.width * scaleFactor;
        enhancedCanvas.height = sourceCanvas.height * scaleFactor;

        const ctx = enhancedCanvas.getContext('2d');

        // Enable maximum quality rendering settings
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        ctx.textRenderingOptimization = 'optimizeQuality';

        // Scale the context for high-resolution rendering
        ctx.scale(scaleFactor, scaleFactor);

        // Apply enhanced quality filters for ID card photos
        ctx.filter = 'contrast(1.08) brightness(1.02) saturate(1.12) sharpen(0.3)';

        // Draw the source canvas with high-quality scaling
        ctx.drawImage(sourceCanvas, 0, 0);

        // Apply additional post-processing for photo enhancement
        const imageData = ctx.getImageData(0, 0, enhancedCanvas.width, enhancedCanvas.height);
        const data = imageData.data;

        // Enhanced pixel-level processing for better photo quality
        for (let i = 0; i < data.length; i += 4) {
            // Enhance contrast and clarity
            data[i] = Math.min(255, data[i] * 1.05);     // Red
            data[i + 1] = Math.min(255, data[i + 1] * 1.05); // Green
            data[i + 2] = Math.min(255, data[i + 2] * 1.08); // Blue (slight enhancement)
            // Alpha channel remains unchanged
        }

        ctx.putImageData(imageData, 0, 0);

        console.log(`Enhanced canvas for 300 DPI: ${enhancedCanvas.width}x${enhancedCanvas.height}px`);
        return enhancedCanvas;
    } catch (error) {
        console.warn('Canvas enhancement failed, using original:', error);
        return sourceCanvas;
    }
}

// Function to enhance image quality before processing with 300 DPI optimization
function enhanceImageForProcessing(img) {
    // Apply advanced CSS enhancements for maximum rendering quality
    img.style.imageRendering = 'high-quality';
    img.style.imageRendering = '-webkit-optimize-contrast';
    img.style.imageRendering = 'crisp-edges';
    img.style.imageRendering = 'pixelated'; // For sharp edges

    // Set high-quality interpolation
    img.style.imageInterpolation = 'high';
    img.style.msInterpolationMode = 'bicubic';

    // Apply enhanced quality filters optimized for ID card photos
    const currentFilter = img.style.filter || '';
    if (!currentFilter.includes('contrast')) {
        img.style.filter = (currentFilter + ' contrast(1.08) brightness(1.02) saturate(1.12) blur(0px) sharpen(0.2)').trim();
    }

    // Ensure maximum quality scaling
    img.style.transform = img.style.transform || 'scale(1)';
    img.style.transformOrigin = 'center center';

    // Force high-quality rendering
    img.setAttribute('loading', 'eager');
    img.setAttribute('decoding', 'sync');
}

// Function to enhance photo quality with advanced image processing
function enhancePhotoQuality(img) {
    try {
        // Create a canvas for image processing
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Set canvas size to match image natural dimensions for maximum quality
        canvas.width = img.naturalWidth || img.width;
        canvas.height = img.naturalHeight || img.height;

        // Enable high-quality rendering
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';

        // Apply quality enhancement filters
        ctx.filter = 'contrast(1.1) brightness(1.03) saturate(1.15) blur(0px)';

        // Draw the image with enhancements
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        // Get image data for pixel-level processing
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        // Advanced pixel processing for photo enhancement
        for (let i = 0; i < data.length; i += 4) {
            // Enhance skin tones and overall photo quality
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];

            // Apply subtle skin tone enhancement
            data[i] = Math.min(255, r * 1.08);     // Red enhancement
            data[i + 1] = Math.min(255, g * 1.05); // Green balance
            data[i + 2] = Math.min(255, b * 1.02); // Blue balance
            // Alpha remains unchanged
        }

        // Put enhanced image data back
        ctx.putImageData(imageData, 0, 0);

        // Convert to high-quality data URL
        const enhancedDataUrl = canvas.toDataURL('image/png', 1.0);

        // Update the image source with enhanced version
        img.src = enhancedDataUrl;

        console.log('Photo quality enhanced for 300 DPI processing');

    } catch (error) {
        console.warn('Photo enhancement failed:', error);
    }
}

// Initialize Face API when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Check if face-api is available
    if (typeof faceapi !== 'undefined') {
        loadFaceApiModels();
    } else {
        console.warn('Face API library not available, face detection will be disabled');
    }
});

// Global variables for progress-based loading
let currentPage = 1;
let isLoading = false;
let hasMoreData = true;
let totalRecords = 0;
let loadedRecords = 0;
const recordsPerChunk = 100; // Larger chunks for faster loading
let loadingStartTime = null;
let maxLoadingTime = 300000; // 5 minutes maximum loading time



// Show loading with progress bar
showEnhancedLoading('Loading Data', 'Preparing to load all records...');

$(document).ready(function() {
    // Initialize progress-based data loading
    initializeProgressDataLoading();

    // Ensure status tabs are working
    console.log('Status tabs found:', $('.status-tab').length);
    if ($('.status-tab').length === 0) {
        console.error('No status tabs found! Check HTML structure.');
    }
});

/**
 * Initialize progress-based data loading system
 */
function initializeProgressDataLoading() {
    // Reset pagination variables
    currentPage = 1;
    isLoading = false;
    hasMoreData = true;
    totalRecords = 0;
    loadedRecords = 0;
    window.entityData = [];

    // Start loading all data with progress
    loadAllDataWithProgress();
}

/**
 * Load all data with progress indication
 */
function loadAllDataWithProgress() {
    if (isLoading) {
        return;
    }

    isLoading = true;
    loadingStartTime = Date.now(); // Track loading start time
    console.log('Starting progressive data loading...');

    // Load first chunk to get total count
    loadEntityChunk(1, true);
}

/**
 * Load a chunk of entity data for progress loading
 */
function loadEntityChunk(page = 1, isInitialLoad = false) {
    console.log('Loading chunk - Page:', page, 'Records:', loadedRecords + '/' + totalRecords);

    $.ajax({
        url: '<?php echo site_url('idcards/Idcards_controller/getEntitiesForOrderChunked'); ?>',
        type: 'POST',
        data: {
            order_id: order_id,
            id_card_for: id_card_for,
            status_filter: selectedStatus,
            page: page,
            limit: recordsPerChunk,
            search: $('#search-entities').val() || ''
        },
        dataType: 'json',
        success: function(response) {
            try {
                if (response.success && response.data) {
                    // Update pagination info
                    totalRecords = response.pagination.total_records;
                    hasMoreData = response.pagination.has_more;
                    currentPage = response.pagination.current_page;

                    if (isInitialLoad) {
                        // First load - initialize everything
                        window.entityData = response.data;
                        loadedRecords = response.data.length;

                        // Update status counts
                        updateStatusCountsFromResponse(response.status_counts);

                        // Build initial table
                        const tableHtml = construct_entity_table(window.entityData, id_card_for);
                        $('.entity-table-wrapper').html(tableHtml);

                        // Initialize event handlers
                        initializeEventHandlers();

                        // Update progress and continue loading
                        updateEnhancedLoadingProgress(
                            Math.round((loadedRecords / totalRecords) * 100),
                            `Loaded ${loadedRecords} of ${totalRecords} records...`
                        );

                        // Continue loading remaining data automatically
                        if (hasMoreData) {
                            setTimeout(() => {
                                continueProgressiveLoading();
                            }, 100);
                        } else {
                            // All data loaded in first chunk
                            isLoading = false; // Ensure loading state is reset
                            hideLoading();
                            hideEnhancedLoading(); // Explicitly hide enhanced loading overlay
                            $('#enhanced-loading-overlay').fadeOut(300); // Force hide overlay
                        }
                    } else {
                        // Subsequent loads - append data
                        window.entityData = window.entityData.concat(response.data);
                        loadedRecords += response.data.length;

                        // Append new rows to existing table
                        appendEntityRows(response.data);

                        // Update progress
                        updateEnhancedLoadingProgress(
                            Math.round((loadedRecords / totalRecords) * 100),
                            `Loaded ${loadedRecords} of ${totalRecords} records...`
                        );

                        // Continue loading if more data exists
                        if (hasMoreData) {
                            console.log('Scheduling next chunk load...');
                            setTimeout(() => {
                                continueProgressiveLoading();
                            }, 100); // Slightly slower for stability
                        } else {
                            // All data loaded
                            console.log('All data loading completed!');
                            isLoading = false; // Ensure loading state is reset
                            hideLoading();
                            hideEnhancedLoading(); // Explicitly hide enhanced loading overlay
                            $('#enhanced-loading-overlay').fadeOut(300); // Force hide overlay
                        }
                    }

                    // Update progress indicator
                    updateLoadingProgress();

                } else {
                    console.error('Error in chunked data response:', response);
                    handleChunkLoadError(response.message || 'Failed to load data');
                }
            } catch (e) {
                console.error('Error processing chunked data:', e);
                handleChunkLoadError('Error processing data: ' + e.message);
            }

            // Always reset loading state after each chunk
            isLoading = false;
        },
        error: function(xhr, status, error) {
            console.error('AJAX error in chunked loading:', error);
            console.error('XHR status:', xhr.status, 'Response:', xhr.responseText);
            handleChunkLoadError('Network error: ' + error);
            isLoading = false;
        }
    });
}

/**
 * Handle chunk loading errors
 */
function handleChunkLoadError(errorMessage) {
    if (currentPage === 1) {
        // First load failed - show error message
        hideLoading();
        $('.entity-table-wrapper').html('<div class="alert alert-danger">Error loading data: ' + errorMessage + '</div>');
    } else {
        // Subsequent load failed - show notification
        hideChunkLoadingIndicator();
        showNotification('error', 'Failed to load more data: ' + errorMessage);
    }
}

/**
 * Continue progressive loading of remaining data
 */
function continueProgressiveLoading() {
    console.log('continueProgressiveLoading called - hasMoreData:', hasMoreData, 'isLoading:', isLoading, 'currentPage:', currentPage);

    // Safety check for maximum loading time
    if (loadingStartTime && (Date.now() - loadingStartTime) > maxLoadingTime) {
        console.error('Loading timeout reached, stopping progressive loading');
        isLoading = false;
        hideLoading();
        showNotification('error', 'Loading Timeout', 'Loading took too long and was stopped. Please refresh the page.');
        return;
    }

    // Safety check for reasonable page limits
    if (currentPage > 50) { // Max 50 pages = 5000 records
        console.error('Too many pages loaded, stopping for safety');
        isLoading = false;
        hideLoading();
        showNotification('error', 'Loading Limit', 'Maximum loading limit reached. Please contact support.');
        return;
    }

    if (hasMoreData && !isLoading) {
        console.log('Loading next chunk:', currentPage + 1);
        loadEntityChunk(currentPage + 1, false);
    } else {
        console.log('Cannot continue loading:', { hasMoreData, isLoading, currentPage });

        // If we're stuck, reset and try once more
        if (hasMoreData && isLoading) {
            console.log('Detected stuck loading, resetting...');
            isLoading = false;
            setTimeout(() => {
                continueProgressiveLoading();
            }, 1000);
        }
    }
}





/**
 * Update loading progress indicator
 */
function updateLoadingProgress() {
    const progressPercentage = totalRecords > 0 ? Math.round((loadedRecords / totalRecords) * 100) : 0;
    const progressText = `Loaded ${loadedRecords} of ${totalRecords} records (${progressPercentage}%)`;

    // Update entity count display
    $('#visible-count').text(loadedRecords);
    $('#total-count').text(totalRecords);

    // Update inline progress bar
    if (totalRecords > recordsPerChunk) {
        $('#loading-progress').show();
        $('#data-progress-bar').css('width', progressPercentage + '%');
        $('#progress-text').text(progressText);
    } else {
        $('#loading-progress').hide();
    }

    // Update main progress bar if visible
    $('#main-progress-bar').css('width', progressPercentage + '%');
    $('#loading-stats').text(progressText);



    // Show completion notification
    if (!hasMoreData && loadedRecords > 0) {
        $('#loading-progress').hide();
        console.log('All data loaded successfully:', loadedRecords, 'records');

        // Force close loading overlay after completion
        setTimeout(() => {
            hideEnhancedLoading();
            $('#enhanced-loading-overlay').fadeOut(300);
            console.log('Forced loading overlay to close');
        }, 500);
    }
}

/**
 * Enhanced loading overlay functions
 */
function showEnhancedLoading(title = 'Loading Data', message = 'Please wait while we fetch your data...') {
    $('#loading-title').text(title);
    $('#loading-message').text(message);
    $('#main-progress-bar').css('width', '0%');
    $('#loading-stats').text('Preparing...');
    $('#enhanced-loading-overlay').fadeIn(300);
}

function hideEnhancedLoading() {
    $('#enhanced-loading-overlay').fadeOut(300);
    // Force hide with direct style change as backup
    setTimeout(() => {
        $('#enhanced-loading-overlay').hide().css('display', 'none');
    }, 400);
}

function updateEnhancedLoadingProgress(percentage, message) {
    $('#main-progress-bar').css('width', percentage + '%');
    $('#loading-stats').text(message);
}

/**
 * Notification system
 */
function showNotification(type = 'info', title = '', message = '', duration = 5000) {
    const notificationId = 'notification-' + Date.now();
    const iconClass = {
        'success': 'fa-check-circle',
        'error': 'fa-exclamation-circle',
        'warning': 'fa-exclamation-triangle',
        'info': 'fa-info-circle'
    }[type] || 'fa-info-circle';

    const notificationHtml = `
        <div id="${notificationId}" class="notification ${type}">
            <button class="notification-close" onclick="closeNotification('${notificationId}')">&times;</button>
            <div class="notification-title">
                <i class="fa ${iconClass}"></i> ${title}
            </div>
            <div class="notification-message">${message}</div>
        </div>
    `;

    $('#notification-container').append(notificationHtml);

    // Auto-remove notification after duration
    if (duration > 0) {
        setTimeout(() => {
            closeNotification(notificationId);
        }, duration);
    }
}

function closeNotification(notificationId) {
    $(`#${notificationId}`).fadeOut(300, function() {
        $(this).remove();
    });
}

/**
 * Override default loading functions to use enhanced loading
 */
function showLoading(message = 'Loading...') {
    showEnhancedLoading('Loading Data', message);
}

function hideLoading() {
    hideEnhancedLoading();
}

/**
 * Debug functions
 */




/**
 * Update status counts from server response
 */
function updateStatusCountsFromResponse(statusCounts) {
    if (statusCounts) {
        $('.status-tabs .status-tab[data-status="all"] .status-count').text(statusCounts.total || 0);
        $('.status-tabs .status-tab[data-status="in review"] .status-count').text(statusCounts.in_review || 0);
        $('.status-tabs .status-tab[data-status="approved"] .status-count').text(statusCounts.approved || 0);
        $('.status-tabs .status-tab[data-status="removed"] .status-count').text(statusCounts.removed || 0);
        $('.status-tabs .status-tab[data-status="modify"] .status-count').text(statusCounts.modify || 0);
        $('.status-tabs .status-tab[data-status="re_ordered"] .status-count').text(statusCounts.re_ordered || 0);
    }
}

/**
 * Append new entity rows to existing table
 */
function appendEntityRows(newData) {
    if (!newData || newData.length === 0) {
        return;
    }

    const tbody = $('.entity-table tbody');
    if (tbody.length === 0) {
        // No existing table, rebuild completely
        const tableHtml = construct_entity_table(window.entityData, id_card_for);
        $('.entity-table-wrapper').html(tableHtml);
        return;
    }

    // Append new rows
    newData.forEach(function(entity, index) {
        const rowHtml = constructEntityRow(entity, id_card_for);
        tbody.append(rowHtml);
    });

    // Reinitialize any necessary event handlers for new rows
    initializeNewRowEventHandlers();
}

/**
 * Construct a single entity row HTML
 */
function constructEntityRow(entity, id_card_for) {
    if (!entity || typeof entity !== 'object') {
        return '';
    }

    const status = (entity.status || 'in review').replace(/\s+/g, '-');
    const entityId = id_card_for.toLowerCase() === 'staff' ?
        (entity.sm_id || entity.id) :
        (id_card_for.toLowerCase() === 'parent' ?
            (entity.id) :
            (entity.sa_id));

    let html = '<tr data-entity-id="' + entityId + '" data-status="' + status + '">';

    if (id_card_for.toLowerCase() === 'staff') {
        html += '<td>' + (entity.staff_name || '') + '</td>';
        html += '<td class="status-cell" style="text-align:center">';
        html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
        html += '</td>';
        html += '<td>' + (entity.employee_code || '') + '</td>';
        html += '<td style="white-space: nowrap;">' + (entity.dob || '') + '</td>';
        html += '<td>' + (entity.contact || '') + '</td>';
    } else if (id_card_for.toLowerCase() === 'parent') {
        // Handle parent rows based on avatar_type
        if (entity.avatar_type == 'Mother') {
            html += '<td>' + (entity.name || '') + '</td>';
            html += '<td class="status-cell" style="text-align:center">';
            html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
            html += '</td>';
            html += '<td>Mother</td>';
            html += '<td>' + (entity.name || '') + '</td>';
            html += '<td>' + (entity.class_section || '') + '</td>';
            html += '<td>' + (entity.mother_contact || '') + '</td>';
        } else if (entity.avatar_type == 'Father') {
            html += '<td>' + (entity.father_name || '') + '</td>';
            html += '<td class="status-cell" style="text-align:center">';
            html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
            html += '</td>';
            html += '<td>Father</td>';
            html += '<td>' + (entity.name || '') + '</td>';
            html += '<td>' + (entity.class_section || '') + '</td>';
            html += '<td>' + (entity.father_contact || '') + '</td>';
        }
        // Add other parent types as needed...
    } else {
        // Student rows
        html += '<td>' + (entity.name || '') + '</td>';
        html += '<td class="status-cell" style="text-align:center">';
        html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
        html += '</td>';
        html += '<td>' + (entity.class_section || '') + '</td>';
        html += '<td style="white-space: nowrap;">' + (entity.dob || '') + '</td>';
        html += '<td>' + (entity.preferred_contact_no || '') + '</td>';
    }

    // Add action buttons
    html += '<td class="actions-cell">';
    html += '<div class="button-container">';

    const isOrderLocked = ['order_submitted', 'in_printing', 'delivered'].includes(order_status);
    const style = status.toLowerCase().trim() === 'modify' ? '' : 'display:none';
    const profileId = entityId;

    if (isOrderLocked) {
        html += '<button class="action-btn edit-btn locked-action" disabled ' +
                'title="Actions are locked - Order is in ' + capitalizeFirstLetter(order_status.replace('_', ' ')) + ' status">' +
                '<i class="fa fa-lock"></i></button>';
        html += '<button class="action-btn info-btn" ' +
                'title="View ID card (approval disabled)" ' +
                'data-entity="' + encodeURIComponent(JSON.stringify(entity)) + '">' +
                '<i class="fa fa-info-circle"></i></button>';
    } else {
        html += '<button style="' + style + '" class="action-btn edit-btn" ' +
                'onclick="navigateToProfile(\'' + id_card_for.toLowerCase() + '\', \'' + profileId + '\')" ' +
                'title="Edit"><i class="fa fa-pencil"></i></button>';
        html += '<button class="action-btn info-btn" ' +
                'title="View and approve ID card" ' +
                'data-entity="' + encodeURIComponent(JSON.stringify(entity)) + '">' +
                '<i class="fa fa-info-circle"></i></button>';
    }

    html += '</div></td></tr>';

    return html;
}

/**
 * Initialize event handlers for newly added rows
 */
function initializeNewRowEventHandlers() {
    // Re-bind info button click handlers for new rows
    $('.info-btn').off('click.newRows').on('click.newRows', async function() {
        // Show modal immediately
        $('#idCardModal').fadeIn(300);

        // Initialize visible entity IDs
        initializeVisibleEntityIds();

        // Get entity ID and find its index
        const entityRow = $(this).closest('tr');
        const entityId = entityRow.data('entity-id');
        window.currentEntityIndex = window.visibleEntityIds.indexOf(entityId);

        // Load entity data
        await window.loadEntityData(entityId);
    });
}
    function initializeVisibleEntityIds() {
        window.visibleEntityIds = [];
        $('.entity-table tbody tr:visible').each(function() {
            window.visibleEntityIds.push($(this).data('entity-id'));
        });
    }

    function construct_entity_table(data,id_card_for) {
            if (!Array.isArray(data) || data.length === 0) {
                return '<div class="alert alert-info">No data available to display.</div>';
            }

            // Filter data based on status
            const filteredData = selectedStatus === 'all'
                ? data
                : data.filter(entity => {
                    const status = (entity.status || 'in review').toLowerCase();
                    const hyphenStatus = status.replace(/ /g, '-');
                    const underscoreStatus = status.replace(/-/g, '_');
                    return selectedStatus === hyphenStatus || selectedStatus === underscoreStatus;
                });

            var html = '';
            var count = filteredData.length; // Use filtered count

            $('#visible-count').text(count);

            html += '<table class="entity-table">';
            html += '<thead><tr>';

            if (id_card_for.toLowerCase() === 'staff') {
                html += '<th>Name</th>';
                html += '<th class="status-cell">Status</th>';
                html += '<th>Employee ID</th>';
                html += '<th style="white-space: nowrap;">DOB</th>';
                html += '<th>Mobile No.</th>';
            } else if (id_card_for.toLowerCase() === 'parent') {
                html += '<th>Parent Name</th>';
                html += '<th class="status-cell">Status</th>';
                html += '<th>Relation</th>';
                html += '<th>Student Name</th>';
                html += '<th>Class</th>';
                html += '<th>Mobile No.</th>';
            } else {
                html += '<th>Student Name</th>';
                html += '<th class="status-cell">Status</th>';
                html += '<th>Class</th>';
                html += '<th style="white-space: nowrap;">DOB</th>';
                html += '<th>Mobile No.</th>';
            }

            html += '<th class="actions-cell">Actions</th>';
            html += '</tr></thead>';
            html += '<tbody>';

            // Update the loop to use filteredData instead of data
            for (var i = 0; i < filteredData.length; i++) {
                var entity = filteredData[i];

                if (!entity || typeof entity !== 'object') {
                    continue;
                }

                var status = entity.status || 'in review';
                status = status.replace(/\s+/g, '-')
                var entityId = id_card_for.toLowerCase() === 'staff' ? 
                    (entity.sm_id || entity.id || i) : 
                    (id_card_for.toLowerCase() === 'parent' ? 
                        (entity.id || i) : 
                        (entity.sa_id || i));

                html += '<tr data-entity-id="' + entityId + '" data-status="' + status + '">';

                if (id_card_for.toLowerCase() === 'staff') {
                    html += '<td>' + (entity.staff_name || '') + '</td>';
                    html += '<td class="status-cell" style="text-align:center">';
                    html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
                    html += '</td>';
                    html += '<td>' + (entity.employee_code || '') + '</td>';
                    html += '<td style="white-space: nowrap;">' + entity.dob + '</td>';
                    html += '<td>' + (entity.contact || '') + '</td>';
                } else if (id_card_for.toLowerCase() === 'parent') {
                    if(entity.avatar_type == 'Mother'){
                        html += '<td>' + (entity.name || '') + '</td>';
                        html += '<td class="status-cell" style="text-align:center">';
                        html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
                        html += '</td>';
                        html += '<td>Mother</td>';
                        html += '<td>' + (entity.name || '') + '</td>';
                        html += '<td>' + (entity.class_section || '') + '</td>';
                        html += '<td>' + (entity.mother_contact || '') + '</td>';
                    }else if(entity.avatar_type == 'Father'){
                        html += '<td>' + (entity.father_name || '') + '</td>';
                        html += '<td class="status-cell" style="text-align:center">';
                        html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
                        html += '</td>';
                        html += '<td>Father</td>';
                        html += '<td>' + (entity.name || '') + '</td>';
                        html += '<td>' + (entity.class_section || '') + '</td>';
                        html += '<td>' + (entity.father_contact || '') + '</td>';
                    }else if(entity.avatar_type == 'Guardian'){
                        html += '<td>' + (entity.guardian_name || '') + '</td>';
                        html += '<td class="status-cell" style="text-align:center">';
                        html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
                        html += '</td>';
                        html += '<td>Guardian</td>';
                        html += '<td>' + (entity.name || '') + '</td>';
                        html += '<td>' + (entity.class_section || '') + '</td>';
                        html += '<td>' + (entity.guardian_contact || '') + '</td>';
                    }else if(entity.avatar_type == 'Guardian_2'){
                        html += '<td>' + (entity.guardian_2_name || '') + '</td>';
                        html += '<td class="status-cell" style="text-align:center">';
                        html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
                        html += '</td>';
                        html += '<td>Guardian 2</td>';
                        html += '<td>' + (entity.name || '') + '</td>';
                        html += '<td>' + (entity.class_section || '') + '</td>';
                        html += '<td>' + (entity.guardian_2_contact || '') + '</td>';
                    }else if(entity.avatar_type == 'Driver'){
                        html += '<td>' + (entity.driver_name || '') + '</td>';
                        html += '<td class="status-cell" style="text-align:center">';
                        html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
                        html += '</td>';
                        html += '<td>Guardian 2</td>';
                        html += '<td>' + (entity.name || '') + '</td>';
                        html += '<td>' + (entity.class_section || '') + '</td>';
                        html += '<td>' + (entity.driver_contact || '') + '</td>';
                    }else if(entity.avatar_type == 'Driver_2'){
                        html += '<td>' + (entity.driver_2_name || '') + '</td>';
                        html += '<td class="status-cell" style="text-align:center">';
                        html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
                        html += '</td>';
                        html += '<td>Guardian 2</td>';
                        html += '<td>' + (entity.name || '') + '</td>';
                        html += '<td>' + (entity.class_section || '') + '</td>';
                        html += '<td>' + (entity.driver_2_contact || '') + '</td>';
                    }
                } else {
                    html += '<td>' + (entity.name || '') + '</td>';
                    html += '<td class="status-cell" style="text-align:center" >';
                    html += '<span class="status-badge status-' + status + '">' + capitalizeFirstLetter(status) + '</span>';
                    html += '</td>';
                    html += '<td>' + (entity.class_section || '') + '</td>';
                    html += '<td style="white-space: nowrap;">' + entity.dob + '</td>';
                    html += '<td>' + (entity.preferred_contact_no || '') + '</td>';
                }

                html += '<td class="actions-cell">';
                html += '<div class="button-container">';
                // Check if order is in a locked state (order_submitted, in_printing, or delivered)
                const isOrderLocked = ['order_submitted', 'in_printing', 'delivered'].includes(order_status);
                var style = "display:none";
                    if (status.toLowerCase().trim() === 'modify') {
                        style = "";
                    }
                const profileId = id_card_for.toLowerCase() === 'staff' ? 
                    (entity.sm_id || entity.id || i) : 
                    (id_card_for.toLowerCase() === 'parent' ? 
                        (entity.sa_id || i) : 
                        (entity.sa_id || i));
                        
                if (isOrderLocked) {
                    html += '<button  class="action-btn edit-btn locked-action" disabled ' +
                            'title="Actions are locked - Order is in ' + capitalizeFirstLetter(order_status.replace('_', ' ')) + ' status">' +
                            '<i class="fa fa-lock"></i></button>';

                    html += '<button class="action-btn info-btn" ' +
                            'title="View ID card (approval disabled)" ' +
                            'data-entity="' + encodeURIComponent(JSON.stringify(entity)) + '">' +
                            '<i class="fa fa-info-circle"></i>' +
                            '</button>';
                } else {
                    html += '<button style="' + style + '" class="action-btn edit-btn" ' +
                        'onclick="navigateToProfile(\'' + id_card_for.toLowerCase() + '\', \'' + profileId + '\')" ' + 'title="Edit"><i class="fa fa-pencil"></i></button>';
                    html += '<button class="action-btn info-btn" ' +
                            'title="View and approve ID card" ' +
                            'data-entity="' + encodeURIComponent(JSON.stringify(entity)) + '">' +
                            '<i class="fa fa-info-circle"></i>' +
                            '</button>';
                }

                html += '</div>';
                html += '</td>';

                html += '</tr>';
            }

            html += '</tbody></table>';

            return html;
        }

        function capitalizeFirstLetter(string) {
            return string.charAt(0).toUpperCase() + string.slice(1);
        }

        function formatDate(dateString) {
            if (!dateString) return '';

            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return dateString;

                const day = date.getDate().toString().padStart(2, '0');
                const month = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][date.getMonth()];
                const year = date.getFullYear();

                return `${day}-${month}-${year}`;
            } catch (e) {
                return dateString;
            }
        }

        function navigateToProfile(type, id) {
            const staffUrl = '<?php echo site_url("staff/Staff_controller/addMoreStaffInfo"); ?>';
            const studentUrl = '<?php echo site_url("student/Student_controller/addMoreStudentInfo"); ?>';

            if (type === 'staff') {
                window.open(`${staffUrl}/${id}`, '_blank');
            } else if (type === 'student') {
                window.open(`${studentUrl}/${id}`, '_blank');
            } else {
                console.error('Unknown type:', type);
            }
        }


        function updateStatusCounts(data) {
            let inReviewCount = 0;
            let approvedCount = 0;
            let rejectedCount = 0;
            let reOrderedCount = 0;

            // Use provided data or fall back to window.entityData if no data is provided
            const entityData = data || window.entityData || [];

            if (Array.isArray(entityData)) {
                entityData.forEach(entity => {
                    const status = entity.status || 'in review';
                    if (status === 'in review') inReviewCount++;
                    else if (status === 'approved') approvedCount++;
                    else if (status === 'removed') rejectedCount++;
                    else if (status === 're-ordered') reOrderedCount++;
                });
            }

            $('.status-tabs .status-tab[data-status="in review"] .status-count').text(inReviewCount);
            $('.status-tabs .status-tab[data-status="approved"] .status-count').text(approvedCount);
            $('.status-tabs .status-tab[data-status="removed"] .status-count').text(rejectedCount);
            $('.status-tabs .status-tab[data-status="re_ordered"] .status-count').text(reOrderedCount);
        }

        // Function to refresh entity data from the server
        function refreshEntityData(callback) {
            showLoading('Refreshing data...');

            $.ajax({
                url: '<?php echo site_url("idcards/Idcards_controller/get_entities_for_order"); ?>',
                type: 'POST',
                data: {
                    order_id: order_id,
                    id_card_for: id_card_for,
                    status_filter: 'all'
                },
                dataType: 'json',
                success: function(response) {
                    hideLoading();
                    if (response && response.success && response.data) {
                        // Update the global entity data
                        window.entityData = response.data;
                        // console.log('Entity data refreshed from server:', response.data.length, 'records');

                        if (typeof callback === 'function') {
                            callback(response.data);
                        }
                    } else {
                        console.error('Failed to refresh entity data:', response);
                        showNotification('error', 'Failed to refresh data from server');
                    }
                },
                error: function(xhr, status, error) {
                    hideLoading();
                    console.error('Error refreshing entity data:', error);
                    showNotification('error', 'Error connecting to server');
                }
            });
        }

        function initializeEventHandlers() {
            // Remove any existing click handlers to prevent duplicates
            $('.status-tab').off('click.statusFilter');

            // Add click handler with namespace
            $('.status-tab').on('click.statusFilter', function() {
                console.log('Status tab clicked:', $(this).data('status'));

                $('.status-tab').removeClass('active');
                $(this).addClass('active');
                selectedStatus = $(this).data('status');

                console.log('Selected status changed to:', selectedStatus);

                // Reload data with new status filter using progress loading
                showEnhancedLoading('Loading Data', 'Filtering records by status...');
                initializeProgressDataLoading();
            });

            // Debounced search functionality
            let searchTimeout;
            $('#search-entities').on('input', function() {
                const searchTerm = $(this).val();

                // Clear previous timeout
                clearTimeout(searchTimeout);

                // Set new timeout for debounced search
                searchTimeout = setTimeout(function() {
                    // Reload data with search filter using progress loading
                    showEnhancedLoading('Searching', 'Searching through all records...');
                    initializeProgressDataLoading();
                }, 500); // 500ms delay
            });

            $('#class-filter').change(function() {
                filterEntitiesByClass($(this).val());
            });

            $('#section-filter').change(function() {
                filterEntitiesBySection($(this).val());
            });

            // Store current entity data globally for navigation
            window.currentEntityIndex = -1;
            window.visibleEntityIds = [];

            // Function to navigate to previous staff
            function navigateToPreviousStaff() {
                if (window.currentEntityIndex > 0) {
                    window.currentEntityIndex--;
                    const entityId = window.visibleEntityIds[window.currentEntityIndex];

                    // Get the current status directly from the table row
                    const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                    if (entityRow.length) {
                        const currentStatus = entityRow.attr('data-status') || 'in review';

                        // Load the entity data
                        window.loadEntityData(entityId);

                        // Update the status badge and buttons
                        updateEntityStatusBadge();
                        setModalActionButtonStates(currentStatus);
                    } else {
                        window.loadEntityData(entityId);
                    }
                }
            }

            // Function to navigate to next staff
            function navigateToNextStaff() {
                if (window.currentEntityIndex < window.visibleEntityIds.length - 1) {
                    window.currentEntityIndex++;
                    const entityId = window.visibleEntityIds[window.currentEntityIndex];

                    // Get the current status directly from the table row
                    const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                    if (entityRow.length) {
                        const currentStatus = entityRow.attr('data-status') || 'in review';

                        // Load the entity data
                        window.loadEntityData(entityId);

                        // Update the status badge and buttons
                        updateEntityStatusBadge();
                        setModalActionButtonStates(currentStatus);
                    } else {
                        window.loadEntityData(entityId);
                    }
                }
            }

            // Function to update navigation buttons state
            function updateNavigationButtons() {
                // Disable/enable previous button
                $('#prevStaffBtn').prop('disabled', window.currentEntityIndex <= 0);

                // Disable/enable next button
                $('#nextStaffBtn').prop('disabled', window.currentEntityIndex >= window.visibleEntityIds.length - 1);
                updateEntityPositionIndicator();
                updateEntityStatusBadge();
            }

            // Initialize visible entity IDs
            function initializeVisibleEntityIds() {
                window.visibleEntityIds = [];
                $('.entity-table tbody tr:visible').each(function() {
                    window.visibleEntityIds.push($(this).data('entity-id'));
                });
            }

            // Set up navigation button click handlers
            $('#prevStaffBtn').on('click', navigateToPreviousStaff);
            $('#nextStaffBtn').on('click', navigateToNextStaff);

            // Info button click handler
            $(document).on('click', '.info-btn', async function() {
                // Show modal immediately
                $('#idCardModal').fadeIn(300);

                // Initialize visible entity IDs
                initializeVisibleEntityIds();

                // Get entity ID and find its index
                const entityRow = $(this).closest('tr');
                const entityId = entityRow.data('entity-id');
                window.currentEntityIndex = window.visibleEntityIds.indexOf(entityId);

                // Load entity data
                await window.loadEntityData(entityId);
            });

            $(document).ajaxComplete(function() {
                if (!$.fn.DataTable.isDataTable('.entity-table')) {
                    $('.entity-table').DataTable({
                        paging: false,
                        lengthChange: false,
                        searching: false,
                        ordering: true,
                        info: true,
                        autoWidth: false,
                        stateSave: true,
                        stateDuration: 60 * 60 * 24,
                    });
                }
            });
        }

        function filterEntitiesByStatus(status) {
            if ($.fn.DataTable.isDataTable('.entity-table')) {
                const dataTable = $('.entity-table').DataTable();

                const currentPage = dataTable.page.info().page;

                if (status === 'all') {
                    dataTable.search('').columns().search('').draw();
                } else {
                    $.fn.dataTable.ext.search.push(
                        function(settings, data, dataIndex) {
                            const rowStatus = $(dataTable.row(dataIndex).node()).attr('data-status');
                            return rowStatus === status;
                        }
                    );

                    dataTable.draw();

                    $.fn.dataTable.ext.search.pop();
                }

                if (dataTable.page.info().pages > currentPage) {
                    dataTable.page(currentPage).draw('page');
                }
            } else {
                if (status === 'all') {
                    $('.entity-table tbody tr').show();
                } else {
                    $('.entity-table tbody tr').hide();
                    $('.entity-table tbody tr[data-status="' + status + '"]').show();
                }
            }

            updateVisibleCount();
            updateStatusCounts();
        }

        function filterEntitiesBySearch(searchTerm) {
            $('.entity-table tbody tr').each(function() {
                const name = $(this).find('td:nth-child(1)').text().toLowerCase();
                const id = $(this).find('td:nth-child(2)').text().toLowerCase();

                if (name.includes(searchTerm) || id.includes(searchTerm)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });

            updateVisibleCount();
        }

        function filterEntitiesByClass(classValue) {
            if (!classValue) {
                $('.entity-table tbody tr').show();
            } else {
                $('.entity-table tbody tr').each(function() {
                    let classColumnIndex;
                    if (id_card_for.toLowerCase() === 'staff') {
                        classColumnIndex = 2;
                    } else {
                        classColumnIndex = 3;
                    }

                    const classText = $(this).find('td:nth-child(' + classColumnIndex + ')').text();
                    if (classText.includes(classValue)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            }

            updateVisibleCount();
        }

        function filterEntitiesBySection(sectionValue) {
            if (!sectionValue) {
                $('.entity-table tbody tr').show();
            } else {
                $('.entity-table tbody tr').each(function() {
                    let sectionColumnIndex;
                    if (id_card_for.toLowerCase() === 'staff') {

                        sectionColumnIndex = 3;
                    } else {
                        sectionColumnIndex = 3;
                    }

                    const sectionText = $(this).find('td:nth-child(' + sectionColumnIndex + ')').text();
                    if (sectionText.includes(sectionValue)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            }

            updateVisibleCount();
        }

        function updateVisibleCount() {
            const visibleCount = $('.entity-table tbody tr:visible').length;
            const entityType = id_card_for.toLowerCase() === 'staff' ? 'Staff' : 'Students';
            $('#visible-count').text(visibleCount);
            $('.entity-count').text(`Showing ${visibleCount} ${entityType}`);
        }

        // Function to disable action buttons during loading
        function disableActionButtons() {
            $('.id-card-action-btn').prop('disabled', true);
            $('.id-card-approve').html('<i class="fa fa-spinner fa-spin"></i> LOADING...');
            $('.id-card-modify').html('<i class="fa fa-spinner fa-spin"></i> LOADING...');
            $('.id-card-remove').html('<i class="fa fa-spinner fa-spin"></i> LOADING...');
        }

        // Function to enable action buttons after loading
        function enableActionButtons(status) {
            // Clear any pending timeout
            if (window.buttonEnableTimeout) {
                clearTimeout(window.buttonEnableTimeout);
                window.buttonEnableTimeout = null;
            }

            // Reset button text
            $('.id-card-approve').html('<i class="fa fa-check"></i> APPROVE');
            $('.id-card-modify').html('<i class="fa fa-pencil"></i> MODIFY');
            $('.id-card-remove').html('<i class="fa fa-trash"></i> REMOVE');

            // Set proper disabled state based on status
            setModalActionButtonStates(status || 'in review');
        }

        // Function to increment loaded shape elements counter
        function incrementShapeElementsLoaded() {
            window.shapeElementsLoaded++;

            // If all shape elements are loaded, enable the action buttons
            if (window.shapeElementsLoaded >= window.shapeElementsLoading) {
                // Get current status from the entity row
                const entityId = window.visibleEntityIds[window.currentEntityIndex];
                const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                const currentStatus = entityRow.attr('data-status') || 'in review';

                // Enable buttons with proper state
                enableActionButtons(currentStatus);

                // Reset counters for next load
                window.shapeElementsLoading = 0;
                window.shapeElementsLoaded = 0;
            }
        }

        function renderPreview(entityData) {
            // Show loading indicator in the preview areas
            $('#frontCardPreview').html('<div class="preview-loading"><i class="fa fa-spinner fa-spin fa-2x"></i><p>Loading preview...</p></div>');
            $('#backCardPreview').html('<div class="preview-loading"><i class="fa fa-spinner fa-spin fa-2x"></i><p>Loading preview...</p></div>');

            // Reset shape element counters
            window.shapeElementsLoading = 0;
            window.shapeElementsLoaded = 0;

            // Disable action buttons during loading
            disableActionButtons();

            // Set a timeout to ensure buttons are enabled even if there's an issue with loading
            window.buttonEnableTimeout = setTimeout(function() {
                if ($('.id-card-approve').prop('disabled')) {
                    console.warn('Timeout reached for loading ID card elements, enabling buttons');
                    const entityId = window.visibleEntityIds[window.currentEntityIndex];
                    const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                    const currentStatus = entityRow.attr('data-status') || 'in review';
                    enableActionButtons(currentStatus);
                }
            }, 5000); // 5 second timeout

            $.ajax({
                url: '<?php echo site_url("idcards/Idcards_controller/getIdCardTemplate"); ?>',
                type: 'post',
                data: { order_id: order_id },
                success: function(response) {
                    try {
                        const templateData = JSON.parse(response);

                        if (templateData && templateData.template) {
                            const template = templateData.template;

                            let frontDesign = JSON.parse(template.front_design);
                            let backDesign = JSON.parse(template.back_design);

                            $('#frontCardPreview').empty();
                            renderDesign('#frontCardPreview', frontDesign, entityData);

                            $('#backCardPreview').empty();
                            renderDesign('#backCardPreview', backDesign, entityData);

                            // If no shape elements were found, enable buttons immediately
                            if (window.shapeElementsLoading === 0) {
                                const entityId = window.visibleEntityIds[window.currentEntityIndex];
                                const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                                const currentStatus = entityRow.attr('data-status') || 'in review';
                                enableActionButtons(currentStatus);
                            }
                        } else {
                            renderSimplePreview(entityData);
                        }
                    } catch (e) {
                        console.error('Error parsing template data:', e);
                        renderSimplePreview(entityData);

                        // Enable buttons on error
                        const entityId = window.visibleEntityIds[window.currentEntityIndex];
                        const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                        const currentStatus = entityRow.attr('data-status') || 'in review';
                        enableActionButtons(currentStatus);
                    }
                },
                error: function(err) {
                    console.error('Error fetching template data:', err);
                    renderSimplePreview(entityData);

                    // Enable buttons on error
                    const entityId = window.visibleEntityIds[window.currentEntityIndex];
                    const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                    const currentStatus = entityRow.attr('data-status') || 'in review';
                    enableActionButtons(currentStatus);
                }
            });
        }

        // Track loading state of shape elements
        window.shapeElementsLoading = 0;
        window.shapeElementsLoaded = 0;

        function renderDesign(container, design, entityData) {
            const $container = $(container);
            $container.addClass(`card-size-${design.styles?.size || 'portrait'}`);

            if (design.styles && design.styles.backgroundColor) {
                $container.css('background-color', design.styles.backgroundColor);
            }

            // Count shape elements that need to be loaded
            if (design.elements) {
                design.elements.forEach(element => {
                    if (element.type === 'shape') {
                        window.shapeElementsLoading++;
                    }
                });
            }

            // Disable action buttons until all shapes are loaded
            disableActionButtons();

            design.elements?.forEach(element => {
                // For address fields, use relative positioning and auto height
                let isAddressField =
                    element.type === 'field' &&
                    (
                        element.properties.fieldName === '[[ADDRESS]]' ||
                        element.properties.fieldName === '[[STUDENT_ADDRESS]]' ||
                        element.properties.fieldName === '[[FATHER_ADDRESS]]' ||
                        element.properties.fieldName === '[[MOTHER_ADDRESS]]'
                    );

                const $element = $('<div>').addClass('element').css({
                    position: isAddressField ? 'relative' : 'absolute',
                    left: element.x + 'px',
                    top: element.y + 'px',
                    width: element.width + 'px',
                    height: isAddressField ? 'auto' : (element.height + 'px'),
                    zIndex: element.zIndex || 0
                });
                switch (element.type) {
                    case 'text':
                        $element.html(`<div class="element-content">${element.properties.text || ''}</div>`);
                        $element.find('.element-content').css({
                            'font-family': element.properties.font,
                            'font-size': `${element.properties.size}px`,
                            'color': element.properties.color,
                            'font-weight': element.properties.bold ? element.properties.bold : 'normal',
                            'font-style': element.properties.italic ? 'italic' : 'normal',
                            'text-decoration': element.properties.underline ? 'underline' : 'none',
                            'text-align': element.properties.textAlign || 'left',
                            'background-color': element.properties.backgroundColor || 'transparent',
                            'border': element.properties.strokeWidth ?
                                `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                            'padding': '2px 5px'
                        });
                        break;

                    case 'field':
                        const value = getFieldValue(element.properties.fieldName, entityData);
                        $element.html(`<div class="element-content">${value}</div>`);

                        const fixedHeight = element.height || 48;

                        if (
                            element.properties.fieldName === '[[ADDRESS]]' ||
                            element.properties.fieldName === '[[STUDENT_ADDRESS]]' ||
                            element.properties.fieldName === '[[FATHER_ADDRESS]]' ||
                            element.properties.fieldName === '[[MOTHER_ADDRESS]]'
                        ) {
                            $element.find('.element-content').css({
                                'font-family': element.properties.font,
                                'font-size': `${element.properties.size}px`,
                                'color': element.properties.color,
                                'font-weight': element.properties.bold ? element.properties.bold : 'normal',
                                'font-style': element.properties.italic ? 'italic' : 'normal',
                                'text-decoration': element.properties.underline ? 'underline' : 'none',
                                'text-align': element.properties.textAlign || 'left',
                                'background-color': element.properties.backgroundColor || 'transparent',
                                'border': element.properties.strokeWidth ?
                                    `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                                'padding': '12px', 
                                'width': '100%',
                                'min-height': fixedHeight + 'px',
                                'height': 'auto',
                                'max-height': 'none',
                                'box-sizing': 'border-box',
                                'overflow': 'hidden',
                                'white-space': 'pre-line', 
                                'word-break': 'break-word',
                                'line-height': '1.2',
                                'display': 'block',
                                'text-transform': element.properties.textTransform || 'capitalize'
                            });
                        } else if (
                            element.properties.fieldName === '[[NAME]]'
                            
                        ) {
                            let fontSize = element.properties.size || 18;
                            let nameValue = value || '';
                            let textAlign = element.properties.textAlign || 'left';
                            let elementWidth = element.width || 215;
                            let maxFontSize = fontSize;
                            let minFontSize = 6;

                            $element.addClass('name-field');

                            // Start with maxFontSize
                            let appliedFontSize = maxFontSize;

                            // Create a temporary, invisible element to measure width
                            const $temp = $('<div>')
                                .text(nameValue)
                                .css({
                                    'position': 'absolute',
                                    'visibility': 'hidden',
                                    'white-space': 'nowrap',
                                    'font-family': element.properties.font,
                                    'font-weight': element.properties.bold ? element.properties.bold : 'normal',
                                    'font-style': element.properties.italic ? 'italic' : 'normal',
                                    'font-size': element.properties.size + 'px', 
                                    'text-transform': element.properties.textTransform || 'capitalize',
                                })
                                .appendTo('body');

                            // Shrink font until the text fits within 95% of the available width
                            while ($temp.outerWidth() > elementWidth * 0.95 && appliedFontSize > minFontSize) {
                                appliedFontSize--;
                                $temp.css('font-size', appliedFontSize + 'px');
                            }

                            $temp.remove(); // Cleanup temp element

                            if(element.properties.fontImportant !== '!important'){
                               fontSize = appliedFontSize;
                                // Optional y-shift for smaller fonts
                                if (fontSize < 10) {
                                    element.y = (element.y || 0) + 1;
                                }
                            }

                            // Apply top position
                            $element.css({ 'top': element.y });
                           
                            // Apply styles to content
                            $element.find('.element-content').css({
                                'font-family': element.properties.font,
                                'font-size':  element.properties.size + 'px', 
                                'color': element.properties.color,
                                'font-weight': element.properties.bold ? element.properties.bold : 'normal',
                                'font-style': element.properties.italic ? 'italic' : 'normal',
                                'text-decoration': element.properties.underline ? 'underline' : 'none',
                                'text-align': textAlign,
                                'background-color': element.properties.backgroundColor || 'transparent',
                                'border': element.properties.strokeWidth ?
                                    `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                                'padding': '6px 8px',
                                'width': '100%',
                                'height': 'auto',
                                'max-height': 'none',
                                'box-sizing': 'border-box',
                                'overflow': 'visible',
                                'white-space': 'normal',
                                'word-break': 'break-word',
                                'line-height': '1.2',
                                'display': 'block',
                                'text-transform': element.properties.textTransform || 'capitalize',
                            });
                            console.log('nameField',$element);
                        } else if (
                            element.properties.fieldName === '[[PARENT_NAME]]'
                        ) {
                            let parentNameValue = value || '';
                            let textAlign = element.properties.textAlign || 'left';
                            let elementWidth = element.width || 215;
                            let maxFontSize = element.properties.size;
                            let minFontSize = 6;
                            let fontSize = element.properties.size || maxFontSize;

                            $element.addClass('parent-name-field');
                            $element.css({ 'text-align': textAlign });

                            // Start with max font size
                            let appliedFontSize = maxFontSize;

                            // Temporary hidden div to measure width
                            const $temp = $('<div>')
                                .text(parentNameValue)
                                .css({
                                    'position': 'absolute',
                                    'visibility': 'hidden',
                                    'white-space': 'nowrap',
                                    'font-family': element.properties.font,
                                    'font-weight': element.properties.bold ? element.properties.bold : 'normal',
                                    'font-style': element.properties.italic ? 'italic' : 'normal',
                                    'font-size': appliedFontSize + 'px',
                                    'text-transform': element.properties.textTransform || 'capitalize'
                                })
                                .appendTo('body');

                            // Reduce font size until it fits within container width
                            while ($temp.outerWidth() > elementWidth * 0.95 && appliedFontSize > minFontSize) {
                                appliedFontSize--;
                                $temp.css('font-size', appliedFontSize + 'px');
                            }

                            $temp.remove(); // Cleanup

                            // Apply calculated font size
                            fontSize = appliedFontSize;

                            if (fontSize < 10) {
                                element.y = (element.y || 0) + 1;
                            }

                            // Apply styles
                            $element.find('.element-content').css({
                                'font-family': element.properties.font,
                                'font-size': `${fontSize}px`,
                                'color': element.properties.color,
                                'font-weight': element.properties.bold ? element.properties.bold : 'normal',
                                'font-style': element.properties.italic ? 'italic' : 'normal',
                                'text-decoration': element.properties.underline ? 'underline' : 'none',
                                'text-align': textAlign,
                                'background-color': element.properties.backgroundColor || 'transparent',
                                'border': element.properties.strokeWidth ?
                                    `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                                'padding': '6px 8px',
                                'width': '100%',
                                'height': fixedHeight + 'px',
                                'max-height': fixedHeight + 'px',
                                'box-sizing': 'border-box',
                                'overflow': 'hidden',
                                'white-space': 'normal',
                                'word-break': 'break-word',
                                'line-height': '1.2',
                                'display': 'block',
                                'text-transform': element.properties.textTransform || 'capitalize',
                            });

                        }else if (element.properties.fieldName === '[[QR_CODE]]') {
                            if (entityData.qr_code) {
                                // Display QR code number below the QR code image
                                $element.find('.element-content').css({
                                    'flex-direction': 'column',
                                    'align-items': 'center',
                                    'justify-content': 'center'
                                });

                                // Use a QR code image if available, else generate using API
                                let qrCodeImg = '';
                                if (entityData.qr_code_url) {
                                    qrCodeImg = `<img src="${entityData.qr_code_url}" alt="QR Code" style="max-width:100%;max-height:100px;display:block;margin-bottom:4px;">`;
                                    $element.find('.element-content').html(qrCodeImg);
                                } else {
                                    $element.find('.element-content').html('<i class="fa fa-spinner fa-spin fa-2x"></i>');
                                    generate_qr_code('qr_code', entityData.qr_code, element.properties.qr_size, function(generatedQRCode) {
                                        qrCodeImg = `<img src="${generatedQRCode}" alt="QR Code">`;
                                        $element.find('.element-content').html(qrCodeImg);
                                    });
                                }
                            } else {
                                $element.find('.element-content').html('<span style="color:#aaa;">No QR Code</span>');
                            }
                        }else if (element.properties.fieldName === '[[FATHER_PHOTO]]') {
                            if (entityData.father_photo) {
                                const imageClass = element.properties.shapeType === 'circle' ?
                                'element-content photo-in-circle' : 'element-content';
                                let father_image = '';
                                if (entityData.father_photo) {
                                    $element.find('.element-content').html('<i class="fa fa-spinner fa-spin fa-2x"></i>');

                                    father_image = `<img src="${entityData.father_photo}" class="${imageClass}" alt="motherphoto" style="max-width:100%;max-height:100px;display:block;margin-bottom:4px;">`;
                                    $element.find('.element-content').html(father_image);
                                }

                            } else {
                                $element.find('.element-content').html('<span style="color:#aaa;"></span>');
                            }
                        }else if (element.properties.fieldName === '[[MOTHER_PHOTO]]') {
                            if (entityData.mother_photo) {
                                const imageClass = element.properties.shapeType === 'circle' ?
                                'element-content photo-in-circle' : 'element-content';
                                let mother_image = '';
                                if (entityData.mother_photo) {
                                    $element.find('.element-content').html('<i class="fa fa-spinner fa-spin fa-2x"></i>');

                                    mother_image = `<img src="${entityData.mother_photo}" class="${imageClass}" alt="motherphoto" style="max-width:100%;max-height:100px;display:block;margin-bottom:4px;">`;
                                    $element.find('.element-content').html(mother_image);
                                }

                            } else {
                                $element.find('.element-content').html('<span style="color:#aaa;"></span>');
                            }
                        }else if (element.properties.fieldName === '[[BAR_CODE]]') {
                            if (entityData.qr_code) {
                                // Display QR code number below the QR code image
                                $element.find('.element-content').css({
                                    'flex-direction': 'column',
                                    'align-items': 'center',
                                    'justify-content': 'center'
                                });

                                // Use a QR code image if available, else generate using API
                                let qrCodeImg = '';
                                if (entityData.qr_code_url) {
                                    qrCodeImg = `<img src="${entityData.qr_code_url}" alt="QR Code" style="max-width:100%;max-height:100px;display:block;margin-bottom:4px;">`;
                                    $element.find('.element-content').html(qrCodeImg);
                                } else {
                                    $element.find('.element-content').html('<i class="fa fa-spinner fa-spin fa-2x"></i>');
                                    generate_qr_code('bar_code', entityData.qr_code, element.properties.qr_size, function(generatedQRCode) {
                                        qrCodeImg = `<img src="${generatedQRCode}" alt="QR Code">`;
                                        $element.find('.element-content').html(qrCodeImg);
                                    });
                                }
                            } else {
                                $element.find('.element-content').html('<span style="color:#aaa;">No QR Code</span>');
                            }
                        }else {
                            let fontSize = element.properties.size || 11;
                            let NameValue = value || '';
                            let textAlign = element.properties.textAlign || 'left';
                            let elementWidth = element.width || 215;
                            
                            const charWidthFactor = 0.58;
                            const containerWidthInChars = Math.floor(elementWidth / (fontSize * charWidthFactor));
                            // Adjust font size based on both text length and available width
                            if (NameValue.length > containerWidthInChars * 0.8) {
                                // Text is approaching container width limit
                                const ratio = containerWidthInChars / NameValue.length;
                              
                               // More granular font size adjustments
                                if (ratio < 0.3) {
                                    // Extremely long text
                                    fontSize = Math.max(6, fontSize * 0.45);
                                } else if (ratio < 0.4) {
                                    // Very long text
                                    fontSize = Math.max(7, fontSize * 0.5);

                                } else if (ratio < 0.5) {
                                    // Long text
                                    fontSize = Math.max(8, fontSize * 0.6);

                                } else if (ratio < 0.6) {
                                    // Moderately long text
                                    fontSize = Math.max(9, fontSize * 0.5);
                                } else if (ratio < 0.7) {
                                    // Slightly long text
                                    fontSize = Math.max(9, fontSize * 0.5);
                                } else if (ratio < 0.8) {
                                    // Just a bit too long
                                    fontSize = Math.max(9, fontSize * 0.6);
                                } else if (ratio < 0.93) {
                                    fontSize = Math.max(8, fontSize * 0.7);
                                } else if (ratio < 0.95) {
                                    fontSize = Math.max(8, fontSize * 0.8);
                                } else if (ratio <= 1) {
                                    // Check for ratio less than 1.05
                                    fontSize = Math.max(8.5, fontSize * 0.8);
                                }else if (ratio < 1.04) {
                                    fontSize = Math.max(6, fontSize * 0.8);
                                }else if (ratio < 1.05) {
                                    // Check for ratio less than 1.05
                                    fontSize = Math.max(8, fontSize * 0.8);
                                } else if (ratio < 1.2) {
                                    fontSize = Math.max(9.5, fontSize * 0.8);
                                } else if (ratio < 1.5) {
                                    fontSize = Math.max(8, fontSize * 0.9);
                                }
                            }
                            // Apply base styling for all other fields
                            $element.find('.element-content').css({
                                'font-family': element.properties.font,
                                'font-size': element.properties.fontImportant == '!important' ? element.properties.size + 'px' : `${fontSize}px`, 
                                'color': element.properties.color,
                                'font-weight': element.properties.bold ? element.properties.bold : 'normal',
                                'font-style': element.properties.italic ? 'italic' : 'normal',
                                'text-decoration': element.properties.underline ? 'underline' : 'none',
                                'text-align': element.properties.textAlign || 'left',
                                'background-color': element.properties.backgroundColor || 'transparent',
                                'border': element.properties.strokeWidth ?
                                    `${element.properties.strokeWidth}px solid ${element.properties.strokeColor}` : 'none',
                                'padding': '6px 8px', // more vertical padding for spacing
                                'width': '100%',
                                'height': fixedHeight + 'px',
                                'max-height': fixedHeight + 'px',
                                'box-sizing': 'border-box',
                                'overflow': 'hidden',
                                'white-space': 'normal',
                                'word-break': 'break-word',
                                'line-height': '1.2',
                                'display': 'block',
                                'text-transform': element.properties.textTransform || 'capitalize'
                            });
                        }
                        break;

                    case 'shape':
                        $element.html('<div class="element-content shape-container"><i class="fa fa-spinner fa-spin fa-2x"></i></div>');
                        const $shapeContent = $element.find('.element-content');
                        $shapeContent.addClass(`shape-${element.properties.shapeType}`);
                        let photoUrl = entityData.photo || element.properties.defaultPhoto || '<?= base_url("assets/img/icons/profile.png") ?>';
                        if (photoUrl.includes('wasabisys.com')) {
                            $.ajax({
                                url: '<?php echo site_url("idcards/Idcards_controller/getImageAsBase64"); ?>',
                                type: 'POST',
                                data: {
                                    image_url: photoUrl,
                                    enhance_quality: true,  // Enable quality enhancement
                                    target_dpi: 300        // Target 300 DPI
                                },
                                dataType: 'json',
                                success: function(response) {
                                    if (response.success && response.base64) {
                                        const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
                                        $shapeContent.html(`<img id="${imgId}" src="${response.base64}" class="photo-in-shape high-quality-photo">`);

                                        // Apply image enhancements
                                        const imgElement = document.getElementById(imgId);
                                        if (imgElement) {
                                            enhanceImageForProcessing(imgElement);
                                        }

                                        // Add small delay to ensure DOM is updated
                                        setTimeout(() => {
                                            applyFaceDetection(imgId, response.base64);
                                            applySavedPhotoAdjustment(imgId);
                                        }, 100);
                                        incrementShapeElementsLoaded();

                                        console.log(`Enhanced photo loaded: ${response.enhanced ? 'Yes' : 'No'}, Size: ${(response.original_size/1024).toFixed(1)}KB`);
                                    } else {
                                        console.warn('Base64 conversion failed, using original URL');
                                        const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
                                        $shapeContent.html(`<img id="${imgId}" src="${photoUrl}" class="photo-in-shape">`);
                                        setTimeout(() => {
                                            applyFaceDetection(imgId, photoUrl);
                                            applySavedPhotoAdjustment(imgId);
                                        }, 100);
                                        incrementShapeElementsLoaded();
                                    }
                                },
                                error: function(xhr, status, error) {
                                    console.error('Error fetching enhanced image:', error);
                                    const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
                                    $shapeContent.html(`<img id="${imgId}" src="${photoUrl}" class="photo-in-shape">`);
                                    setTimeout(() => {
                                        applyFaceDetection(imgId, photoUrl);
                                        applySavedPhotoAdjustment(imgId);
                                    }, 100);
                                    incrementShapeElementsLoaded();
                                }
                            });
                        } else {
                            const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
                            $shapeContent.html(`<img id="${imgId}" src="${photoUrl}" class="photo-in-shape high-quality-photo">`);

                            // Apply image enhancements for local images too
                            setTimeout(() => {
                                const imgElement = document.getElementById(imgId);
                                if (imgElement) {
                                    enhanceImageForProcessing(imgElement);
                                }
                                applyFaceDetection(imgId, photoUrl);
                                applySavedPhotoAdjustment(imgId);
                            }, 100);
                            incrementShapeElementsLoaded();
                        }
                        $shapeContent.css({
                            'background-color': element.properties.fillColor || 'transparent',
                            'border': `${element.properties.borderWidth || 0}px solid ${element.properties.borderColor || '#000000'}`,
                            'overflow': 'hidden',
                            'border-radius': `${element.properties.borderRadius || ''}px`,
                        });
                        break;

                    case 'image':
                        if (element.properties.fieldName === '[[PHOTO]]') {
                            const photoUrl = entityData.photo || element.properties.src || '<?= base_url("assets/images/default-profile.jpg") ?>';
                            const imageClass = element.properties.shapeType === 'circle' ?
                                'element-content photo-in-circle high-quality-photo' : 'element-content high-quality-photo';
                            const imgId = 'photo-' + Date.now() + '-' + Math.floor(Math.random() * 1000);

                            // Enhanced photo loading for image elements
                            if (photoUrl.includes('wasabisys.com')) {
                                // Use enhanced controller method for external images
                                $.ajax({
                                    url: '<?php echo site_url("idcards/Idcards_controller/getImageAsBase64"); ?>',
                                    type: 'POST',
                                    data: {
                                        image_url: photoUrl,
                                        enhance_quality: true,
                                        target_dpi: 300
                                    },
                                    dataType: 'json',
                                    success: function(response) {
                                        if (response.success && response.base64) {
                                            $element.html(`<img id="${imgId}" src="${response.base64}" class="${imageClass}">`);
                                            const imgElement = document.getElementById(imgId);
                                            if (imgElement) {
                                                enhanceImageForProcessing(imgElement);
                                            }
                                            setTimeout(() => {
                                                applyFaceDetection(imgId, response.base64);
                                                applySavedPhotoAdjustment(imgId);
                                            }, 100);
                                        } else {
                                            $element.html(`<img id="${imgId}" src="${photoUrl}" class="${imageClass}">`);
                                            setTimeout(() => {
                                                applyFaceDetection(imgId, photoUrl);
                                                applySavedPhotoAdjustment(imgId);
                                            }, 100);
                                        }
                                    },
                                    error: function() {
                                        $element.html(`<img id="${imgId}" src="${photoUrl}" class="${imageClass}">`);
                                        setTimeout(() => {
                                            applyFaceDetection(imgId, photoUrl);
                                            applySavedPhotoAdjustment(imgId);
                                        }, 100);
                                    }
                                });
                            } else {
                                $element.html(`<img id="${imgId}" src="${photoUrl}" class="${imageClass}">`);
                                setTimeout(() => {
                                    const imgElement = document.getElementById(imgId);
                                    if (imgElement) {
                                        enhanceImageForProcessing(imgElement);
                                    }
                                    applyFaceDetection(imgId, photoUrl);
                                    applySavedPhotoAdjustment(imgId);
                                }, 100);
                            }
                        } else {
                            // For non-photo images, still apply quality enhancements
                            const imgSrc = element.properties.src || '';
                            $element.html(`<img src="${imgSrc}" class="element-content high-quality-photo">`);
                            setTimeout(() => {
                                const img = $element.find('img')[0];
                                if (img) {
                                    enhanceImageForProcessing(img);
                                }
                            }, 50);
                        }
                        break;
                }

                $container.append($element);
            });
        }

        function getFieldValue(fieldName, entityData) {
            const FIELD_MAPPINGS = [
                { field: '[[NAME]]', key: 'name' },
                { field: '[[ID]]', key: 'employee_code' },
                { field: '[[DEPARTMENT]]', key: 'department' },
                { field: '[[DESIGNATION]]', key: 'designation' },
                { field: '[[CONTACT]]', key: 'contact' },
                { field: '[[PARENT_NAME]]', key: 'parent_name' },
                { field: '[[FATHER_NAME]]', key: 'father_name' },
                { field: '[[MOTHER_NAME]]', key: 'mother_name' },
                { field: '[[FATHER_CONTACT]]', key: 'father_contact' },
                { field: '[[MOTHER_CONTACT]]', key: 'mother_contact' },
                { field: '[[BLOOD_GROUP]]', key: 'blood_group' },
                { field: '[[EMERGENCY_CONTACT]]', key: 'emergency_contact' },
                { field: '[[DATE_OF_BIRTH]]', key: 'date_of_birth' },
                { field: '[[DOB]]', key: 'dob' },
                { field: '[[QR_CODE]]', key: 'qr_code' },
                { field: '[[BAR_CODE]]', key: 'bar_code' },
                { field: '[[SIGNATURE]]', key: 'signature' },
                { field: '[[LOGO]]', key: 'logo' },
                { field: '[[ADDRESS]]', key: 'address' },
                { field: '[[EMAIL]]', key: 'email' },
                { field: '[[PHONE]]', key: 'phone' },
                { field: '[[WEBSITE]]', key: 'website' },
                { field: '[[SOCIAL_MEDIA]]', key: 'social_media' },
                { field: '[[QR_CODE_URL]]', key: 'qr_code_url' },
                { field: '[[BAR_CODE_URL]]', key: 'bar_code_url' },
                { field: '[[SIGNATURE_URL]]', key: 'signature_url' },
                { field: '[[LOGO_URL]]', key: 'logo_url' },
                { field: '[[ADDRESS_URL]]', key: 'address_url' },
                { field: '[[EMAIL_URL]]', key: 'email_url' },
                { field: '[[PHONE_URL]]', key: 'phone_url' },
                { field: '[[WEBSITE_URL]]', key: 'website_url' },
                { field: '[[SOCIAL_MEDIA_URL]]', key: 'social_media_url' },
                { field: '[[PHOTO]]', key: 'photo' },
                { field: '[[GRADE_SECTION]]', key: 'grade_section' },
                { field: '[[STUDENT_ADDRESS]]', key: 'address' },
                { field: '[[FATHER_ADDRESS]]', key: 'father_address' },
                { field: '[[MOTHER_ADDRESS]]', key: 'mother_address' },
                { field: '[[FATHER_PHOTO]]', key: 'father_photo' },
                { field: '[[MOTHER_PHOTO]]', key: 'mother_photo' },
                { field: '[[GRADE]]', key: 'grade' },
                { field: '[[COMBINATION]]', key: 'combination' },
                { field: '[[ENROLLMENT_NO]]', key: 'enrollment_no' },
                { field: '[[ALPHA_ROLL_NO]]', key: 'alpha_rollnum' },
                { field: '[[ADMISSION_NO]]', key: 'admission_no' },
                { field: '[[RELATION_TYPE]]', key: 'relation_type' },
                { field: '[[SIBLING_NAME]]', key: 'sibling_name' },
                { field: '[[NAME_CLASS]]', key: 'name_class' },
                { field: '[[STUDENT_PICKING_ROUTE]]', key: 'picking_route' },
                { field: '[[STUDENT_DROPPING_ROUTE]]', key: 'dropping_route' },
                { field: '[[SPOUSE_NAME]]', key: 'spouse_name' },
                { field: '[[STAFF_TYPE]]', key: 'staff_type' },
                { field: '[[QUALIFICATION]]', key: 'qualification' },
                { field: '[[CLASS_SECTION]]', key: 'class_section' },
                { field: '[[ADISSION_ACADEMIC_YEAR]]', key: 'admission_acad_year' },
                { field: '[[IDENTIFICATION_CODE]]', key: 'identification_code' },

                
            ];
            const mapping = FIELD_MAPPINGS.find(m => m.field === fieldName);
            return mapping ? entityData[mapping.key] || '' : '';
        }

        function renderSimplePreview(entityData) {
            const frontHtml = `
                <div style="width: 100%; height: 100%; position: relative; background-color: #f8f9fa; border: 2px solid #ddd;">
                    <!-- X marks -->
                    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg" style="position: absolute; top: 0; left: 0;">
                        <line x1="0" y1="0" x2="100%" y2="100%" stroke="#ccc" stroke-width="3"/>
                        <line x1="100%" y1="0" x2="0" y2="100%" stroke="#ccc" stroke-width="3"/>
                    </svg>

                    <!-- Content overlay -->
                    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; z-index: 2;">
                        <div style="background-color: rgba(255,255,255,0.9); padding: 15px; border-radius: 5px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                            <div style="font-size: 16px; color: #333; font-weight: bold; margin-bottom: 10px;">
                                ID CARD PREVIEW
                            </div>
                            <div style="color: #666;">
                                <strong>${entityData.name}</strong><br>
                                ${entityData.employee_code || entityData.admission_no}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            const backHtml = `
                <div style="width: 100%; height: 100%; position: relative; background-color: #f8f9fa; border: 2px solid #ddd;">
                    <!-- X marks -->
                    <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg" style="position: absolute; top: 0; left: 0;">
                        <line x1="0" y1="0" x2="100%" y2="100%" stroke="#ccc" stroke-width="3"/>
                        <line x1="100%" y1="0" x2="0" y2="100%" stroke="#ccc" stroke-width="3"/>
                    </svg>

                    <!-- Content overlay -->
                    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; z-index: 2;">
                        <div style="background-color: rgba(255,255,255,0.9); padding: 15px; border-radius: 5px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                            <div style="font-size: 16px; color: #333; font-weight: bold;">
                                BACK SIDE
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#frontCardPreview1').html(frontHtml);
            $('#backCardPreview1').html(backHtml);

            // Enable action buttons when using simple preview
            // Get current status from the entity row
            const entityId = window.visibleEntityIds[window.currentEntityIndex];
            const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
            const currentStatus = entityRow.attr('data-status') || 'in review';

            // Enable buttons with proper state
            enableActionButtons(currentStatus);
        }

        $('.id-card-close').click(function() {
            closeAndClearModal();
        });

        function closeAndClearModal() {
            $('#idCardModal').fadeOut(300, function() {
                clearModalContent();
            });
        }

        function clearModalContent() {
            $('#idCardModalTitle').text('ID Card Preview');

            $('#frontCardPreview').empty();
            $('#backCardPreview').empty();

            $('#entityPositionIndicator').hide();
            $('#entityStatusBadge').hide();
            resetApprovalUI();

            // Clear any pending timeout
            if (window.buttonEnableTimeout) {
                clearTimeout(window.buttonEnableTimeout);
                window.buttonEnableTimeout = null;
            }

            // Reset shape element counters
            window.shapeElementsLoading = 0;
            window.shapeElementsLoaded = 0;
        }





        function modifyIdCard(entityId, onSuccess) {
            const type=entityId.relation_type;
            const sm_id=entityId.id;
            let order_id='<?php echo $order_id ?>';
            let template_id='<?php echo $template ?>';
            Swal.fire({
                title: 'Modify ID Card?',
                text: "Are you sure you want to mark this ID card for modification?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, mark for modification',
                cancelButtonText: 'Cancel',
              
                reverseButtons: true,
                html: `
                    <textarea id="swal-textarea" class="swal2-textarea" placeholder="Enter reason" rows="3"></textarea>
                `,
                preConfirm: () => {
                    const remarks = document.getElementById('swal-textarea').value.trim();
                    if (!remarks) {
                        Swal.showValidationMessage('Remarks are required');
                    }
                    return { remarks };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    const remarks = result.value.remarks;
                    showLoading('Marking ID card for modification...');

                    $.ajax({
                        url: '<?php echo site_url("idcards/Idcards_controller/change_idcard_entity_status"); ?>',
                        type: 'post',
                        data: {
                            sm_id: sm_id,
                            order_id: order_id,
                            type: type,
                            status: 'modify',
                            remarks:remarks
                        },
                        dataType: 'json',
                        success: function(response) {
                            try {
                                if (response.success) {
                                    hideLoading();

                                    showNotification('success', 'ID card marked for modification');

                                    // Update the entity status in the table
                                    updateEntityStatus(sm_id, 'modify');

                                    // Update the button states in the modal
                                    setModalActionButtonStates('modify');

                                    // Update the status badge
                                    $('#entityStatusBadge')
                                        .text('Modify')
                                        .attr('class', 'status-badge status-modify')
                                        .show();

                                    // Update the info-btn data-entity attribute to ensure it has the latest status
                                    const $infoBtn = $(`.entity-table tbody tr[data-entity-id="${sm_id}"]`).find('.info-btn');
                                    if ($infoBtn.length) {
                                        try {
                                            let entityData = JSON.parse(decodeURIComponent($infoBtn.attr('data-entity')));
                                            entityData.status = 'modify';
                                            $infoBtn.attr('data-entity', encodeURIComponent(JSON.stringify(entityData)));
                                        } catch (e) {
                                            console.error('Error updating info-btn data-entity:', e);
                                        }
                                    }

                                    // Refresh the status counts from the server
                                    $.ajax({
                                        url: '<?php echo site_url("idcards/Idcards_controller/get_order_status_counts_ajax"); ?>',
                                        type: 'POST',
                                        data: { order_id: order_id },
                                        dataType: 'json',
                                        success: function(response) {
                                            if (response.success) {
                                                // Update the status counts in the UI
                                                $('.status-tabs .status-tab[data-status="all"] .status-count').text(response.counts.all);
                                                $('.status-tabs .status-tab[data-status="in review"] .status-count').text(response.counts.in_review);
                                                $('.status-tabs .status-tab[data-status="approved"] .status-count').text(response.counts.approved);
                                                $('.status-tabs .status-tab[data-status="removed"] .status-count').text(response.counts.removed);
                                                $('.status-tabs .status-tab[data-status="re_ordered"] .status-count').text(response.counts.re_ordered);
                                            }
                                        }
                                    });
                                    if (typeof onSuccess === 'function') onSuccess();

                                    // Auto-navigate to next entity after modification
                                    setTimeout(function() {
                                        if (
                                            typeof window.currentEntityIndex !== 'undefined' &&
                                            typeof window.visibleEntityIds !== 'undefined' &&
                                            window.currentEntityIndex < window.visibleEntityIds.length - 1
                                        ) {
                                            // Store the current entity ID before navigating
                                            const currentEntityId = window.visibleEntityIds[window.currentEntityIndex];

                                            window.currentEntityIndex++;
                                            const nextEntityId = window.visibleEntityIds[window.currentEntityIndex];
                                            if (typeof window.loadEntityData === 'function') {
                                                window.loadEntityData(nextEntityId);
                                            }
                                        }
                                    }, 800);
                                } else {
                                    hideLoading();

                                    showNotification('error', response.message || 'Failed to mark ID card for modification');
                                }
                            } catch (e) {
                                hideLoading();

                                showNotification('error', 'An error occurred while processing the response');
                                console.error('Error parsing response:', e);
                            }
                        },
                        error: function(err) {
                            hideLoading();

                            showNotification('error', 'Failed to mark ID card for modification');
                            console.error('Error modifying ID card:', err);
                        }
                    });
                }
            });
        }

        function removeIdCard(entityId, onSuccess) {
            const type=entityId.relation_type;
            const sm_id=entityId.id;
            let order_id='<?php echo $order_id ?>';
            let template_id='<?php echo $template ?>';
            Swal.fire({
                title: 'Remove ID Card?',
                text: "Are you sure you want to remove this ID card?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Yes, remove it',
                cancelButtonText: 'Cancel',
                confirmButtonColor: '#d33',
                reverseButtons: true,
                html: `
                    <textarea id="swal-textarea" class="swal2-textarea" placeholder="Enter reason" rows="3"></textarea>
                `,
                preConfirm: () => {
                    const remarks = document.getElementById('swal-textarea').value.trim();
                    if (!remarks) {
                        Swal.showValidationMessage('Remarks are required');
                    }
                    return { remarks };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    const remarks = result.value.remarks;
                    showLoading('Removing ID card...');

                    $.ajax({
                        url: '<?php echo site_url("idcards/Idcards_controller/change_idcard_entity_status"); ?>',
                        type: 'post',
                        data: {
                            sm_id: sm_id,
                            order_id: order_id,
                            type: type,
                            status: 'removed',
                            remarks: remarks

                        },
                        dataType: 'json',
                        success: function(response) {
                            try {
                                if (response.success) {
                                    hideLoading();

                                    showNotification('success', 'ID card removed successfully');

                                    // Update the entity status in the table
                                    updateEntityStatus(sm_id, 'removed');

                                    // Update the button states in the modal
                                    setModalActionButtonStates('removed');

                                    // Update the status badge
                                    $('#entityStatusBadge')
                                        .text('Removed')
                                        .attr('class', 'status-badge status-removed')
                                        .show();

                                    // Update the info-btn data-entity attribute to ensure it has the latest status
                                    const $infoBtn = $(`.entity-table tbody tr[data-entity-id="${sm_id}"]`).find('.info-btn');
                                    if ($infoBtn.length) {
                                        try {
                                            let entityData = JSON.parse(decodeURIComponent($infoBtn.attr('data-entity')));
                                            entityData.status = 'removed';
                                            $infoBtn.attr('data-entity', encodeURIComponent(JSON.stringify(entityData)));
                                        } catch (e) {
                                            console.error('Error updating info-btn data-entity:', e);
                                        }
                                    }

                                    // Refresh the status counts from the server
                                    $.ajax({
                                        url: '<?php echo site_url("idcards/Idcards_controller/get_order_status_counts_ajax"); ?>',
                                        type: 'POST',
                                        data: { order_id: order_id },
                                        dataType: 'json',
                                        success: function(response) {
                                            if (response.success) {
                                                // Update the status counts in the UI
                                                $('.status-tabs .status-tab[data-status="all"] .status-count').text(response.counts.all);
                                                $('.status-tabs .status-tab[data-status="in review"] .status-count').text(response.counts.in_review);
                                                $('.status-tabs .status-tab[data-status="approved"] .status-count').text(response.counts.approved);
                                                $('.status-tabs .status-tab[data-status="removed"] .status-count').text(response.counts.removed);
                                                $('.status-tabs .status-tab[data-status="re_ordered"] .status-count').text(response.counts.re_ordered);
                                            }
                                        }
                                    });
                                    if (typeof onSuccess === 'function') onSuccess();

                                    // Auto-navigate to next entity after removal
                                    setTimeout(function() {
                                        if (
                                            typeof window.currentEntityIndex !== 'undefined' &&
                                            typeof window.visibleEntityIds !== 'undefined' &&
                                            window.currentEntityIndex < window.visibleEntityIds.length - 1
                                        ) {
                                            // Store the current entity ID before navigating
                                            const currentEntityId = window.visibleEntityIds[window.currentEntityIndex];

                                            window.currentEntityIndex++;
                                            const nextEntityId = window.visibleEntityIds[window.currentEntityIndex];
                                            if (typeof window.loadEntityData === 'function') {
                                                window.loadEntityData(nextEntityId);
                                            }
                                        }
                                    }, 800);
                                } else {
                                    hideLoading();

                                    showNotification('error', response.message || 'Failed to remove ID card');
                                }
                            } catch (e) {
                                hideLoading();

                                showNotification('error', 'An error occurred while processing the response');
                                console.error('Error parsing response:', e);
                            }
                        },
                        error: function(err) {
                            hideLoading();

                            showNotification('error', 'Failed to remove ID card');
                            console.error('Error removing ID card:', err);
                        }
                    });
                }
            });
        }

        function formatDate(dateString) {
            if (!dateString) return '';

            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return dateString;

                const day = date.getDate().toString().padStart(2, '0');
                const month = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][date.getMonth()];
                const year = date.getFullYear();

                return day + '-' + month + '-' + year;
            } catch (e) {
                console.error('Error formatting date:', e);
                return dateString;
            }
        }

        function showLoading(message) {
            if ($('#loadingOverlay').length === 0) {
                $('body').append(`
                    <div id="loadingOverlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background-color:rgba(0,0,0,0.5); z-index:9999;">
                        <div style="position:absolute; top:50%; left:50%; transform:translate(-50%,-50%); background-color:white; padding:20px; border-radius:5px; text-align:center;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                            <div id="loadingMessage" class="mt-2">Loading...</div>
                        </div>
                    </div>
                `);
            }

            $('#loadingMessage').text(message || 'Loading...');

            $('#loadingOverlay').fadeIn(200);
        }

        function hideLoading() {
            $('#loadingOverlay').fadeOut(200);
        }

        function showNotification(type, message) {
            if ($('#notificationContainer').length === 0) {
                $('body').append('<div id="notificationContainer" style="position:fixed; top:20px; right:20px; z-index:9999;"></div>');
            }

            const notificationId = 'notification-' + Date.now();

            let bgColor, textColor;
            switch (type) {
                case 'success':
                    bgColor = '#28a745';
                    textColor = 'white';
                    break;
                case 'error':
                    bgColor = '#dc3545';
                    textColor = 'white';
                    break;
                case 'warning':
                    bgColor = '#ffc107';
                    textColor = '#212529';
                    break;
                default:
                    bgColor = '#17a2b8';
                    textColor = 'white';
            }

            const notification = $(`
                <div id="${notificationId}" style="margin-bottom:10px; padding:15px; border-radius:4px; background-color:${bgColor}; color:${textColor}; box-shadow:0 2px 5px rgba(0,0,0,0.2); opacity:0; transition:opacity 0.3s ease;">
                    <div style="display:flex; justify-content:space-between; align-items:center;">
                        <div>${message}</div>
                        <button type="button" style="background:none; border:none; color:${textColor}; font-size:20px; line-height:1; cursor:pointer;" onclick="$('#${notificationId}').fadeOut(300, function() { $(this).remove(); });">&times;</button>
                    </div>
                </div>
            `);

            $('#notificationContainer').append(notification);

            setTimeout(() => {
                $(`#${notificationId}`).css('opacity', '1');
            }, 10);

            setTimeout(() => {
                $(`#${notificationId}`).fadeOut(300, function() {
                    $(this).remove();
                });
            }, 5000);
        }

        // Update status badge after approve, modify, or remove actions
        function updateEntityStatus(entityId, status) {
            let dataTable = null;
            if ($.fn.DataTable.isDataTable('.entity-table')) {
                dataTable = $('.entity-table').DataTable();
            }

            const $row = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
            if ($row.length) {
                // Update status text in DOM
                $row.attr('data-status', status);
                
                const $statusCell = $row.find('.status-cell .status-badge');
                $statusCell.text(capitalizeFirstLetter(status));
                
                // Change class for styling
                $statusCell.removeClass().addClass(`status-badge status-${status}`);
                
                // Check for 'modify' and toggle button visibility
                const $editButton = $row.find('.edit-btn');
                if (status.toLowerCase().trim() === 'modify') {
                    $editButton.show();
                } else {
                    $editButton.hide();
                }

                // Sync with DataTables if present
                if (dataTable) {
                    const rowIndex = dataTable.row($row).index();
                    if (rowIndex !== undefined) {
                        dataTable.row(rowIndex).invalidate().draw(false);
                    }
                }
            }
        }


        // Helper to set modal action button states based on status
        function setModalActionButtonStates(status) {
            // Check if order is in a locked state (order_submitted, in_printing, or delivered)
            const isOrderLocked = ['order_submitted', 'in_printing', 'delivered'].includes(order_status);

            // If order is locked, disable all action buttons regardless of entity status
            if (isOrderLocked) {
                // Disable all buttons
                $('.id-card-approve')
                    .prop('disabled', true)
                    .attr('title', 'Actions are locked - Order is in ' + capitalizeFirstLetter(order_status.replace('_', ' ')) + ' status')
                    .html('<i class="fa fa-lock"></i> APPROVE');
                $('.id-card-modify')
                    .prop('disabled', true)
                    .attr('title', 'Actions are locked - Order is in ' + capitalizeFirstLetter(order_status.replace('_', ' ')) + ' status')
                    .html('<i class="fa fa-lock"></i> MODIFY');
                $('.id-card-remove')
                    .prop('disabled', true)
                    .attr('title', 'Actions are locked - Order is in ' + capitalizeFirstLetter(order_status.replace('_', ' ')) + ' status')
                    .html('<i class="fa fa-lock"></i> REMOVE');

                // Add a locked class to the buttons for additional styling
                $('.id-card-action-btn').addClass('locked-action');

                // Show a notification to the user
                if (!window.lockNotificationShown) {
                    showNotification('info', 'This order is in ' + capitalizeFirstLetter(order_status.replace('_', ' ')) + ' status. All actions are locked.');
                    window.lockNotificationShown = true;
                }
            } else {
                // Normal behavior when order is not locked
                // status: 'approved', 'modify', 'removed', 'in review'
                $('.id-card-approve')
                    .prop('disabled', status === 'approved')
                    .attr('title', status === 'approved' ? 'This ID card is already approved' : 'Approve this ID card')
                    .html('<i class="fa fa-check"></i> APPROVE')
                    .removeClass('locked-action');
                $('.id-card-modify')
                    .prop('disabled', status === 'modify')
                    .attr('title', status === 'modify' ? 'This ID card is already marked for modification' : 'Mark this ID card for modification')
                    .html('<i class="fa fa-pencil"></i> MODIFY')
                    .removeClass('locked-action');
                $('.id-card-remove')
                    .prop('disabled', status === 'removed')
                    .attr('title', status === 'removed' ? 'This ID card is already removed' : 'Remove this ID card')
                    .html('<i class="fa fa-trash"></i> REMOVE')
                    .removeClass('locked-action');
            }
        }

        // Move this function to global scope so it can be called from anywhere
        window.loadEntityData = async function(entityId) {
            // Show loading indicator
            $('#modalLoadingIndicator').show();

            // Find the entity row
            const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
            if (!entityRow.length) {
                console.error('Entity row not found for ID:', entityId);
                $('#modalLoadingIndicator').hide();
                return;
            }

            // Get entity data
            let rawData = entityRow.find('.info-btn').attr('data-entity');
            if (!rawData) {
                console.error('Entity data not found for ID:', entityId);
                $('#modalLoadingIndicator').hide();
                return;
            }

            let entity = JSON.parse(decodeURIComponent(rawData));
            let entityData;

            // Process entity data based on type
            if (id_card_for.toLowerCase() === 'staff') {
                let faceImage = entity.picture_url;
                if(faceImage != ''){
                    faceImage = await cropFaceFromPhoto(entity.picture_url);
                }
                entityData = {
                    id: entity.avatar_id, // This is sm_id for staff
                    name: entity.staff_name,
                    employee_code: entity.employee_code,
                    dob: entity.dob,
                    date_of_birth: entity.dob,
                    contact: entity.contact,
                    emergency_contact: entity.emergency_contact,
                    blood_group: entity.blood_group,
                    address: entity.address,
                    department: entity.department,
                    designation: entity.designation,
                    phone: entity.contact,
                    photo:  entity.picture_url,
                    type: entity.type,
                    relation_type : id_card_for,
                    spouse_name:entity.spouse_name,
                    staff_type:entity.staff_type,
                    qualification : entity.qualification
                };
            } else {
                let parent_name_info = entity.father_name || '';
                let parent_contact_info = (entity.mother_contact || '') + ' ' + (entity.father_contact || '');

                if (entity.point_of_contact != null && entity.point_of_contact !== '') {
                    // override like original logic
                    parent_name_info = entity.parent_name;
                    parent_contact_info = entity.contact;
                } else {
                    // fallback to avatar_type logic
                    switch (entity.avatar_type) {
                        case 'Father':
                            parent_name_info = entity.father_name;
                            parent_contact_info = entity.father_contact;
                            break;
                        case 'Mother':
                            parent_name_info = entity.mother_name;
                            parent_contact_info = entity.mother_contact;
                            break;
                        case 'Guardian':
                            parent_name_info = entity.guardian_name;
                            parent_contact_info = entity.guardian_contact;
                            break;
                        case 'Guardian_2':
                            parent_name_info = entity.guardian_2_name;
                            parent_contact_info = entity.guardian_2_contact;
                            break;
                        case 'Driver':
                            parent_name_info = entity.driver_name;
                            parent_contact_info = entity.driver_contact;
                            break;
                        case 'Driver_2':
                            parent_name_info = entity.driver_2_name;
                            parent_contact_info = entity.driver_2_contact;
                            break;
                        default:
                            // If all else fails
                            parent_name_info = entity.parent_name || entity.father_name;
                            parent_contact_info = entity.contact || ((entity.mother_contact || '') + ' ' + (entity.father_contact || ''));
                    }
                }             
                entityData = {
                    id: entity.sa_id,
                    name: entity.name,
                    admission_no: entity.admission_no,
                    combination: entity.combination,
                    enrollment_no: entity.enrollment_no,
                    alpha_rollnum: entity.alpha_rollnum,
                    grade_section: entity.grade_section,
                    grade: entity.grade,
                    dob: entity.dob,
                    date_of_birth: entity.dob,
                    contact: parent_contact_info,
                    blood_group: entity.blood_group,
                    address: entity.address,
                    parent_name: parent_name_info,
                    father_name: entity.father_name,
                    mother_name: entity.mother_name,
                    father_contact: entity.father_contact,
                    mother_contact: entity.mother_contact,
                    email: entity.email,
                    phone: entity.preferred_contact_no,
                    photo: (entity.avatar_type == 'Father') ? entity.father_photo :
                            (entity.avatar_type == 'Mother') ? entity.mother_photo :
                            (entity.avatar_type == 'Guardian') ? entity.guardian_photo :
                            (entity.avatar_type == 'Guardian_2') ? entity.guardian_2_photo :
                            (entity.avatar_type == 'Driver') ? entity.driver_photo :
                            (entity.avatar_type == 'Driver_2') ? entity.driver_2_photo :
                            entity.picture_url,
                    type: entity.type,
                    father_address : entity.father_address,
                    mother_address : entity.mother_address,
                    qr_code : entity.qr_code,
                    relation_type : id_card_for,
                    sibling_name : entity.sibling_name,
                    name_class : entity.name_class,
                    picking_route : entity.picking_route,
                    dropping_route : entity.dropping_route,
                    mother_photo : entity.mother_photo,
                    father_photo : entity.father_photo,
                    class_section : entity.class_section,
                    admission_acad_year : entity.admission_acad_year,
                    identification_code: entity.identification_code
                };
            }
            // Reset approval UI for new staff details
            resetApprovalUI();

            // Update modal title
            $('#idCardModalTitle').text('ID Card Preview - ' + entityData.name);

            // Render preview (this will show another loading indicator)
            renderPreview(entityData);

            // Get the current status from the entity row
            const currentStatus = entityRow.attr('data-status') || 'in review';
            // Set up action buttons
            $('.id-card-approve')
                .data('staff-id', entityData.id)
                .data('type', entityData.relation_type)
                .off('click')
                .on('click', function () {
                    const $btn = $(this);
                    $btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Please wait...');
                    // Only update status badge after approval is confirmed and successful
                    approveIdCard({
                        button: this,
                        onSuccess: function() {
                            $('#entityStatusBadge')
                                .text('Approved')
                                .attr('class', 'status-badge status-approved')
                                .show();
                        }
                    });
                });
            $('.id-card-modify')
                .off('click')
                .on('click', function() {
                    // Only update status badge after modification is confirmed and successful
                    modifyIdCard(entityData, function() {
                        $('#entityStatusBadge')
                            .text('Modify')
                            .attr('class', 'status-badge status-modify')
                            .show();
                    });
                });
            $('.id-card-remove')
                .off('click')
                .on('click', function() {
                    // Only update status badge after removal is confirmed and successful
                    removeIdCard(entityData, function() {
                        $('#entityStatusBadge')
                            .text('Removed')
                            .attr('class', 'status-badge status-removed')
                            .show();
                    });
                });

            // Set correct enabled/disabled state for all three buttons
            setModalActionButtonStates(currentStatus);

            // Update navigation buttons state
            if (typeof updateNavigationButtons === 'function') updateNavigationButtons();

            // Hide loading indicator after everything is set up
            $('#modalLoadingIndicator').hide();
        };

        // Helper to update the entity position indicator in the modal
        function updateEntityPositionIndicator() {
            // ...existing code...
            // Fix: Only count unique, visible entity IDs (avoid duplicates)
            if (
                typeof window.currentEntityIndex !== 'undefined' &&
                typeof window.visibleEntityIds !== 'undefined' &&
                Array.isArray(window.visibleEntityIds) &&
                window.visibleEntityIds.length > 0 &&
                window.currentEntityIndex >= 0
            ) {
                // Remove duplicates from visibleEntityIds
                const uniqueIds = [...new Set(window.visibleEntityIds)];
                $('#entityPositionIndicator')
                    .text((window.currentEntityIndex + 1) + ' of ' + uniqueIds.length)
                    .show();
            } else {
                $('#entityPositionIndicator').hide();
            }
        }

        // Add this function to update the status badge in the modal header
        function updateEntityStatusBadge() {
            if (
                typeof window.currentEntityIndex !== 'undefined' &&
                typeof window.visibleEntityIds !== 'undefined' &&
                Array.isArray(window.visibleEntityIds) &&
                window.visibleEntityIds.length > 0 &&
                window.currentEntityIndex >= 0
            ) {
                const entityId = window.visibleEntityIds[window.currentEntityIndex];
                // Always get the fresh row to ensure we have the latest status
                const $row = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                if ($row.length) {
                    const status = $row.attr('data-status') || 'in review';

                    // Set badge text and class with the correct styling
                    if (status === 'approved') {
                        $('#entityStatusBadge')
                            .text('Approved')
                            .attr('class', 'status-badge status-approved')
                            .show();
                    } else if (status === 'modify') {
                        $('#entityStatusBadge')
                            .text('Modify')
                            .attr('class', 'status-badge status-modify')
                            .show();
                    } else if (status === 'removed') {
                        $('#entityStatusBadge')
                            .text('Removed')
                            .attr('class', 'status-badge status-removed')
                            .show();
                    } else {
                        // Default to 'in review'
                        $('#entityStatusBadge')
                            .text('In Review')
                            .attr('class', 'status-badge status-in-review')
                            .show();
                    }

                    // Also update the button states to match the current status
                    setModalActionButtonStates(status);
                } else {
                    $('#entityStatusBadge').hide();
                }
            } else {
                $('#entityStatusBadge').hide();
            }
        }

        // Patch navigation and modal open to update the indicator and badge
        function updateNavigationButtons() {
            // ...existing code...
            $('#prevStaffBtn').prop('disabled', window.currentEntityIndex <= 0);
            $('#nextStaffBtn').prop('disabled', window.currentEntityIndex >= window.visibleEntityIds.length - 1);
            updateEntityPositionIndicator();
            updateEntityStatusBadge();
        }

        // Patch window.loadEntityData to update the indicator and badge after loading
        // Only patch if not already patched
        if (!window._loadEntityDataPatched) {
            const originalLoadEntityData = window.loadEntityData;
            window.loadEntityData = async function(entityId) {
                await originalLoadEntityData.call(this, entityId);
                updateEntityPositionIndicator();
                updateEntityStatusBadge();
            };
            window._loadEntityDataPatched = true;
        }

        // Hide indicator and badge when modal is cleared
        function clearModalContent() {
            $('#idCardModalTitle').text('ID Card Preview');
            $('#frontCardPreview').empty();
            $('#backCardPreview').empty();
            $('#entityPositionIndicator').hide();
            $('#entityStatusBadge').hide();
            resetApprovalUI();
        }

    function single_file_progress(percentage) {
        if (percentage == 100) {
            in_progress_promises--;
            if (in_progress_promises == 0) {
                current_percentage = percentage;
            }
        } else {
            if (current_percentage < percentage) {
                current_percentage = percentage;
            }
        }
        $("#percentage_doc_completed").html(`${current_percentage}%`);
        return false;
    }

function approveIdCard(opts) {
    // opts: {button, onSuccess}
    Swal.fire({
        title: 'Approve ID Card?',
        text: "Are you sure you want to approve this ID card?",
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Yes, approve it',
        cancelButtonText: 'Cancel',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            $('.id-card-actions').hide();
            // Hide old progress bar, show new overlay
            $('#approval-progress-container').hide();
            showCustomApprovalLoadingOverlay(0);
            const staff_id = $(opts.button).data('staff-id');
            const type = id_card_for;
            const template = <?= json_encode($template) ?>;
            const order_id='<?php echo $order_id ?>';
            in_progress_promises = 2;
            current_percentage = 0;
            setTimeout(() => {
                updateCustomApprovalProgress(10);
                processCardImages(staff_id, type, template, order_id);
                // Call onSuccess after approval process (simulate after processCardImages if needed)
                if (typeof opts.onSuccess === 'function') {
                    opts.onSuccess();
                }
            }, 500);
        } else {
            // Reset the button state if the user cancels
            $(opts.button).prop('disabled', false).html('<i class="fa fa-check"></i> APPROVE');

            // Make sure we update the status badge and buttons to reflect the current status
            if (typeof window.currentEntityIndex !== 'undefined' &&
                window.visibleEntityIds &&
                window.currentEntityIndex >= 0) {

                const entityId = window.visibleEntityIds[window.currentEntityIndex];
                const $row = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                if ($row.length) {
                    const currentStatus = $row.attr('data-status') || 'in review';
                    setModalActionButtonStates(currentStatus);
                    updateEntityStatusBadge();
                }
            }
        }
    });
}

function updateProgressBar(percentage, statusText) {
    updateCustomApprovalProgress(percentage);
    // Optionally update status text if you want to show it
    // $('#customApprovalProgressStatus').text(statusText || '');
}

// Function to ensure all images are loaded before processing
function waitForImagesToLoad(element) {
    return new Promise((resolve) => {
        const images = element.querySelectorAll('img');
        const imagePromises = Array.from(images).map(img => {
            return new Promise((imgResolve) => {
                if (img.complete && img.naturalWidth > 0) {
                    imgResolve();
                } else {
                    img.onload = () => imgResolve();
                    img.onerror = () => imgResolve(); // Continue even if image fails to load
                    // Fallback timeout
                    setTimeout(() => imgResolve(), 3000);
                }
            });
        });

        Promise.all(imagePromises).then(() => {
            console.log('All images loaded for processing');
            resolve();
        });
    });
}

function processCardImages(staff_id, type, template, order_id) {
    const frontEl = document.querySelector('#frontCardPreview');
    const backEl = document.querySelector('#backCardPreview');

    console.log('Processing card images:', {
        frontEl: frontEl,
        backEl: backEl,
        frontElId: frontEl ? frontEl.id : 'not found',
        backElId: backEl ? backEl.id : 'not found',
        frontImages: frontEl ? frontEl.querySelectorAll('img').length : 0,
        backImages: backEl ? backEl.querySelectorAll('img').length : 0
    });

    if (!frontEl || !backEl) {
        updateProgressBar(100, 'Error: Card preview not found');
        showNotification('error', 'Card preview not found.');
        resetApprovalUI();
        return;
    }

    // Update progress to show we're loading images
    updateProgressBar(10, 'Loading images for processing...');

    // Wait for all images to load before processing
    Promise.all([
        waitForImagesToLoad(frontEl),
        waitForImagesToLoad(backEl)
    ]).then(() => {
        // Update progress to show we're starting to process the front image
        updateProgressBar(15, 'Processing front side of ID card...');

        // Process front image first
        processImage(frontEl, 'front.png', staff_id, type, template, order_id)
            .then(frontUrl => {
                // Update progress to show we're starting to process the back image
                updateProgressBar(40, 'Processing back side of ID card...');

                // Then process back image
                return processImage(backEl, 'back.png', staff_id, type, template, order_id)
                    .then(backUrl => {
                        // Both images processed successfully
                        updateProgressBar(70, 'Uploading ID card to server...');
                        generateCard(staff_id, type, template, frontUrl, backUrl,order_id);
                    });
            })
            .catch(error => {
                updateProgressBar(100, 'Error: Failed to process images');
                showNotification('error', error.message || 'Failed to process images');
                resetApprovalUI();
            });
    }).catch(error => {
        updateProgressBar(100, 'Error: Failed to load images');
        showNotification('error', 'Failed to load images for processing');
        resetApprovalUI();
    });
}

// Function to prepare images for html2canvas (convert object-fit/position to actual positioning)
function prepareImagesForCanvas(element) {
    // Try multiple selectors to find all images
    const imageSelectors = [
        '.photo-in-shape',
        '.photo-in-circle',
        'img.element-content',
        '.shape-container img',
        '.circle-container img',
        'img[src]'
    ];

    let images = [];
    imageSelectors.forEach(selector => {
        const foundImages = element.querySelectorAll(selector);
        foundImages.forEach(img => {
            if (!images.includes(img)) {
                images.push(img);
            }
        });
    });

    console.log(`Found ${images.length} images to prepare for canvas processing:`,
        images.map(img => ({ src: img.src, class: img.className, id: img.id })));

    const originalStyles = [];

    images.forEach((img, index) => {
        // Skip if image is not loaded
        if (!img.complete || !img.naturalWidth || !img.naturalHeight) {
            console.warn('Image not fully loaded, skipping preparation:', img.src);
            // Try to force image reload
            const originalSrc = img.src;
            img.src = '';
            img.src = originalSrc;
            return;
        }

        // Apply enhanced quality settings for 300 DPI processing
        enhanceImageForProcessing(img);

        const container = img.parentElement;
        const containerRect = container.getBoundingClientRect();
        const imgRect = img.getBoundingClientRect();

        // Store original styles to restore later (expanded for high-quality processing)
        originalStyles[index] = {
            width: img.style.width,
            height: img.style.height,
            objectFit: img.style.objectFit,
            objectPosition: img.style.objectPosition,
            position: img.style.position,
            top: img.style.top,
            left: img.style.left,
            transform: img.style.transform,
            borderRadius: img.style.borderRadius,
            maxWidth: img.style.maxWidth,
            maxHeight: img.style.maxHeight,
            imageRendering: img.style.imageRendering,
            filter: img.style.filter,
            imageInterpolation: img.style.imageInterpolation,
            msInterpolationMode: img.style.msInterpolationMode,
            transformOrigin: img.style.transformOrigin,
            loading: img.getAttribute('loading'),
            decoding: img.getAttribute('decoding')
        };

        // Get computed styles
        const computedStyle = getComputedStyle(img);
        const objectFit = img.style.objectFit || computedStyle.objectFit;
        const objectPosition = img.style.objectPosition || computedStyle.objectPosition;

        console.log(`Processing high-res image ${index}:`, {
            objectFit: objectFit,
            objectPosition: objectPosition,
            naturalSize: `${img.naturalWidth}x${img.naturalHeight}`,
            containerSize: `${containerRect.width}x${containerRect.height}`,
            quality: 'Enhanced for 300 DPI'
        });

        // If object-fit is cover, we need to simulate it manually for html2canvas
        if (objectFit === 'cover' && objectPosition !== 'initial') {
            try {
                // Calculate the scaling and positioning manually
                const containerWidth = containerRect.width;
                const containerHeight = containerRect.height;
                const imgNaturalWidth = img.naturalWidth;
                const imgNaturalHeight = img.naturalHeight;

                // Calculate scale to cover the container
                const scaleX = containerWidth / imgNaturalWidth;
                const scaleY = containerHeight / imgNaturalHeight;
                const scale = Math.max(scaleX, scaleY);

                // Calculate scaled dimensions
                const scaledWidth = imgNaturalWidth * scale;
                const scaledHeight = imgNaturalHeight * scale;

                // Parse object-position
                let posX = 50, posY = 50; // Default center
                if (objectPosition && objectPosition !== 'initial') {
                    const positions = objectPosition.split(' ');
                    if (positions.length >= 2) {
                        // Convert percentage or keywords to numbers
                        const xPos = positions[0];
                        const yPos = positions[1];

                        if (xPos.includes('%')) {
                            posX = parseFloat(xPos);
                        } else if (xPos === 'left') {
                            posX = 0;
                        } else if (xPos === 'right') {
                            posX = 100;
                        } else if (xPos === 'center') {
                            posX = 50;
                        }

                        if (yPos.includes('%')) {
                            posY = parseFloat(yPos);
                        } else if (yPos === 'top') {
                            posY = 0;
                        } else if (yPos === 'bottom') {
                            posY = 100;
                        } else if (yPos === 'center') {
                            posY = 50;
                        }
                    }
                }

                // Calculate offset based on object-position
                const offsetX = (containerWidth - scaledWidth) * (posX / 100);
                const offsetY = (containerHeight - scaledHeight) * (posY / 100);

                // Apply the calculated positioning
                img.style.position = 'absolute';
                img.style.width = scaledWidth + 'px';
                img.style.height = scaledHeight + 'px';
                img.style.left = offsetX + 'px';
                img.style.top = offsetY + 'px';
                img.style.objectFit = 'none';
                img.style.objectPosition = 'initial';
                img.style.maxWidth = 'none';
                img.style.maxHeight = 'none';

                console.log(`Applied manual positioning for image ${index}:`, {
                    scale: scale,
                    scaledSize: `${scaledWidth}x${scaledHeight}`,
                    offset: `${offsetX}, ${offsetY}`,
                    position: `${posX}%, ${posY}%`
                });

            } catch (error) {
                console.error('Error in manual positioning:', error);
            }
        }
    });

    return originalStyles;
}

// Function to restore original image styles after html2canvas
function restoreImageStyles(element, originalStyles) {
    // Use the same comprehensive image selection as prepareImagesForCanvas
    const imageSelectors = [
        '.photo-in-shape',
        '.photo-in-circle',
        'img.element-content',
        '.shape-container img',
        '.circle-container img',
        'img[src]'
    ];

    let images = [];
    imageSelectors.forEach(selector => {
        const foundImages = element.querySelectorAll(selector);
        foundImages.forEach(img => {
            if (!images.includes(img)) {
                images.push(img);
            }
        });
    });

    images.forEach((img, index) => {
        if (originalStyles[index]) {
            const styles = originalStyles[index];
            // Restore all original styles
            Object.keys(styles).forEach(property => {
                if (styles[property] !== undefined && styles[property] !== null) {
                    img.style[property] = styles[property];
                } else {
                    img.style[property] = '';
                }
            });
        }
    });
}

// Function to ensure element is properly positioned and visible for canvas capture
function prepareElementForCapture(element) {
    // Store original styles
    const originalElementStyles = {
        position: element.style.position,
        visibility: element.style.visibility,
        opacity: element.style.opacity,
        transform: element.style.transform,
        overflow: element.style.overflow,
        width: element.style.width,
        height: element.style.height,
        minWidth: element.style.minWidth,
        minHeight: element.style.minHeight,
        maxWidth: element.style.maxWidth,
        maxHeight: element.style.maxHeight
    };

    // Ensure element is visible and properly positioned
    element.style.visibility = 'visible';
    element.style.opacity = '1';
    element.style.position = 'relative';
    element.style.overflow = 'visible';

    // Remove any transforms that might affect positioning
    element.style.transform = 'none';

    // Temporarily remove size constraints to ensure full content is captured
    element.style.maxWidth = 'none';
    element.style.maxHeight = 'none';
    element.style.minWidth = 'auto';
    element.style.minHeight = 'auto';

    // Ensure the element expands to contain all its children
    const computedStyle = getComputedStyle(element);
    if (computedStyle.width === '0px' || computedStyle.height === '0px') {
        element.style.width = 'auto';
        element.style.height = 'auto';
    }

    return originalElementStyles;
}

// Function to restore element styles after capture
function restoreElementStyles(element, originalStyles) {
    Object.keys(originalStyles).forEach(property => {
        if (originalStyles[property] !== undefined && originalStyles[property] !== null) {
            element.style[property] = originalStyles[property];
        } else {
            element.style[property] = '';
        }
    });
}

function processImage(element, filename, staff_id, type, template, order_id) {
    return new Promise((resolve, reject) => {
        console.log(`Starting processImage for ${filename}`, {
            element: element,
            elementId: element.id,
            elementClass: element.className,
            hasImages: element.querySelectorAll('img').length
        });

        // First, convert all external images to base64 to avoid CORS issues
        const externalImages = element.querySelectorAll('img[src*="wasabisys.com"], img[src*="s3."]');
        console.log(`Found ${externalImages.length} external images to convert for ${filename}`);

        const imageConversionPromises = Array.from(externalImages).map((img, index) => {
            return new Promise((imgResolve) => {
                console.log(`Converting external image ${index + 1} for ${filename}:`, img.src);

                $.ajax({
                    url: '<?php echo site_url("idcards/Idcards_controller/getImageAsBase64"); ?>',
                    type: 'POST',
                    data: {
                        image_url: img.src,
                        enhance_quality: true,
                        target_dpi: 300
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success && response.base64) {
                            const originalSrc = img.src;
                            img.src = response.base64;
                            img.setAttribute('data-original-src', originalSrc);
                            img.setAttribute('data-converted', 'true');
                            console.log(`Successfully converted external image ${index + 1} for ${filename}`);
                        } else {
                            console.warn(`Failed to convert external image ${index + 1} for ${filename}, using original URL`);
                        }
                        imgResolve();
                    },
                    error: function() {
                        console.warn(`Error converting external image ${index + 1} for ${filename}, using original URL`);
                        imgResolve();
                    }
                });
            });
        });

        // Wait for all image conversions to complete before proceeding
        Promise.all(imageConversionPromises).then(() => {
            console.log(`All external images converted for ${filename}, proceeding with canvas processing`);

            // Prepare element for capture
            const originalElementStyles = prepareElementForCapture(element);

            // Ensure containers have relative positioning for absolute image positioning
            const containers = element.querySelectorAll('.shape-container');
            containers.forEach(container => {
                if (getComputedStyle(container).position === 'static') {
                    container.style.position = 'relative';
                }
            });

        // Prepare images for html2canvas to preserve object-fit and object-position
        const originalStyles = prepareImagesForCanvas(element);

        console.log(`Prepared ${Object.keys(originalStyles).length} images for ${filename}`);

        // Force layout recalculation to ensure all elements are properly positioned
        element.offsetHeight; // Trigger reflow

        // Add a small delay to ensure all positioning is applied
        setTimeout(() => {
            // Get element dimensions and ensure they're valid
            const elementRect = element.getBoundingClientRect();
            const computedStyle = getComputedStyle(element);

            // Set base dimensions for ID cards (Portrait orientation: 215px × 335px)
            let baseWidth, baseHeight;
            if (filename === 'front.png') {
                baseWidth = 215;  // Standard ID card width
                baseHeight = 335; // Standard ID card height (portrait)
            } else {
                baseWidth = 215;  // Back card width
                baseHeight = 335; // Back card height (portrait)
            }

            // Use fixed base dimensions for consistent ID card size
            const elementWidth = baseWidth;  // Always use 215px
            const elementHeight = baseHeight; // Always use 335px

            console.log(`Element dimensions for ${filename}:`, {
                baseWidth: baseWidth,
                baseHeight: baseHeight,
                offsetWidth: element.offsetWidth,
                offsetHeight: element.offsetHeight,
                scrollWidth: element.scrollWidth,
                scrollHeight: element.scrollHeight,
                rectWidth: elementRect.width,
                rectHeight: elementRect.height,
                finalWidth: elementWidth,
                finalHeight: elementHeight,
                position: computedStyle.position,
                overflow: computedStyle.overflow,
                transform: computedStyle.transform,
                left: elementRect.left,
                top: elementRect.top,
                right: elementRect.right,
                bottom: elementRect.bottom
            });

            // Use fixed dimensions for consistent ID card output
            const finalWidth = elementWidth;   // Fixed 215px
            const finalHeight = elementHeight; // Fixed 335px

            console.log(`Adjusted dimensions for ${filename}:`, {
                originalWidth: elementWidth,
                originalHeight: elementHeight,
                finalWidth: finalWidth,
                finalHeight: finalHeight,
                fixedDimensions: `${finalWidth}px × ${finalHeight}px`
            });

            // Calculate 300 DPI scale factor
            // Standard ID card dimensions: 215px × 335px (portrait template size)
            // For 300 DPI quality: scale factor of 4.65 (1000px / 215px ≈ 4.65)
            const dpi300Scale = 4.65;

            // Enhanced html2canvas options for 300 DPI quality
            const options = {
                useCORS: true,              // Enable CORS for images
                allowTaint: true,           // Allow tainted canvas
                backgroundColor: null,       // Transparent background
                scale: dpi300Scale,         // 300 DPI scale factor
                logging: true,              // Enable logging for debugging
                width: finalWidth,          // Use adjusted width
                height: finalHeight,        // Use adjusted height
                scrollX: 0,                 // Prevent scroll offset issues
                scrollY: 0,                 // Prevent scroll offset issues
                windowWidth: Math.max(window.innerWidth, finalWidth),
                windowHeight: Math.max(window.innerHeight, finalHeight),
                imageTimeout: 15000,        // Longer timeout for high-res images
                removeContainer: true,      // Clean up after rendering
                foreignObjectRendering: false, // Better compatibility
                x: 0,                       // Start from left edge
                y: 0,                       // Start from top edge
                ignoreElements: function(element) {
                    // Skip elements that might cause rendering issues
                    return element.classList.contains('fa-spinner') ||
                           element.classList.contains('loading-indicator');
                }
            };

            console.log(`Processing ${filename} at 300 DPI (scale: ${dpi300Scale})`);

            html2canvas(element, options).then(canvas => {
                console.log(`html2canvas completed for ${filename}`, {
                    canvasWidth: canvas.width,
                    canvasHeight: canvas.height,
                    elementWidth: element.offsetWidth,
                    elementHeight: element.offsetHeight
                });

                // Restore original styles after rendering
                restoreImageStyles(element, originalStyles);
                restoreElementStyles(element, originalElementStyles);

                // Validate canvas
                if (!canvas || canvas.width === 0 || canvas.height === 0) {
                    restoreElementStyles(element, originalElementStyles);
                    reject(new Error(`Invalid canvas generated for ${filename}`));
                    return;
                }

                // Apply additional quality enhancements to the canvas
                let enhancedCanvas;
                try {
                    enhancedCanvas = enhanceCanvasQuality(canvas);
                    console.log(`Generated 300 DPI ${filename}: ${enhancedCanvas.width}x${enhancedCanvas.height}px`);
                } catch (enhanceError) {
                    console.warn(`Canvas enhancement failed for ${filename}, using original:`, enhanceError);
                    enhancedCanvas = canvas;
                }

                // Create high-quality PNG blob
                enhancedCanvas.toBlob(blob => {
                    if (!blob) {
                        reject(new Error(`Failed to create blob for ${filename}`));
                        return;
                    }

                    console.log(`High-quality ${filename} created: ${(blob.size/1024/1024).toFixed(2)}MB`);

                    uploadToS3(blob, filename, staff_id, type, template, order_id)
                        .then(resolve)
                        .catch(reject);
                }, 'image/png', 1.0); // Maximum quality PNG

            }).catch(err => {
                // Restore original styles even if html2canvas fails
                restoreImageStyles(element, originalStyles);
                restoreElementStyles(element, originalElementStyles);
                console.error(`Error in html2canvas for ${filename}:`, err);
                reject(new Error(`Failed to render ${filename}: ${err.message}`));
            });
        }, 150); // Longer delay for high-resolution processing

        }).catch(error => {
            console.error(`Error in image conversion for ${filename}:`, error);
            reject(new Error(`Failed to convert images for ${filename}: ${error.message}`));
        });
    });
}

function uploadToS3(blob, filename, staff_id, type, template, order_id) {
    return new Promise((resolve, reject) => {
        // Update progress to show we're getting a signed URL
        if (filename === 'front.png') {
            updateProgressBar(20, 'Getting upload URL for front side...');
        } else {
            updateProgressBar(45, 'Getting upload URL for back side...');
        }

        $.ajax({
            url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
            type: 'POST',
            data: {
                file_type: blob.type,
                filename: filename,
                folder: 'idcards'
            },
            success: function(response) {
                try {
                    const { path, signedUrl } = JSON.parse(response);

                    // Update progress to show we're uploading
                    if (filename === 'front.png') {
                        updateProgressBar(25, 'Uploading front side to server...');
                    } else {
                        updateProgressBar(50, 'Uploading back side to server...');
                    }

                    $.ajax({
                        url: signedUrl,
                        type: 'PUT',
                        headers: {
                            "Content-Type": blob.type,
                            "x-amz-acl": "public-read"
                        },
                        processData: false,
                        data: blob,
                        xhr: function() {
                            const xhr = $.ajaxSettings.xhr();
                            xhr.upload.onprogress = function(e) {
                                if (e.lengthComputable) {
                                    const progress = (e.loaded / e.total * 100) | 0;
                                    // Update progress based on which file we're uploading
                                    if (filename === 'front.png') {
                                        // Front image progress from 25% to 40%
                                        const adjustedProgress = 25 + (progress * 0.15);
                                        updateProgressBar(adjustedProgress, 'Uploading front side: ' + progress + '%');
                                    } else {
                                        // Back image progress from 50% to 65%
                                        const adjustedProgress = 50 + (progress * 0.15);
                                        updateProgressBar(adjustedProgress, 'Uploading back side: ' + progress + '%');
                                    }
                                }
                            };
                            return xhr;
                        },
                        success: () => {
                            if (filename === 'front.png') {
                                updateProgressBar(40, 'Front side uploaded successfully');
                            } else {
                                updateProgressBar(65, 'Back side uploaded successfully');
                            }
                            resolve(path);
                        },
                        error: (err) => {
                            updateProgressBar(100, 'Error: Failed to upload to server');
                            reject(new Error('Failed to upload to S3'));
                        }
                    });
                } catch (err) {
                    updateProgressBar(100, 'Error: Invalid server response');
                    reject(new Error('Invalid signed URL response'));
                }
            },
            error: () => {
                updateProgressBar(100, 'Error: Failed to get upload URL');
                reject(new Error('Failed to get signed URL'));
            }
        });
    });
}

function generateCard(staff_id, type, template, frontUrl, backUrl, order_id) {
    updateProgressBar(75, 'Finalizing ID card approval...');

    $.ajax({
        url: '<?php echo site_url("idcards/Idcards_controller/aprrove_idcards_templates") ?>',
        type: 'POST',
        data: {
            staffData: staff_id,
            type: type,
            template: template,
            frontUrl: frontUrl,
            backUrl: backUrl,
            order_id: order_id
        },
        success: function(response) {
            const result = JSON.parse(response);
            // console.log(result);
            if (result.success) {
                updateProgressBar(100, 'ID card approved successfully!');
                showNotification('success', 'ID card approved successfully');

                updateEntityStatus(staff_id, 'approved');

                // Update the info-btn data-entity attribute to ensure it has the latest status
                const $infoBtn = $(`.entity-table tbody tr[data-entity-id="${staff_id}"]`).find('.info-btn');
                if ($infoBtn.length) {
                    try {
                        let entityData = JSON.parse(decodeURIComponent($infoBtn.attr('data-entity')));
                        entityData.status = 'approved';
                        $infoBtn.attr('data-entity', encodeURIComponent(JSON.stringify(entityData)));
                    } catch (e) {
                        console.error('Error updating info-btn data-entity:', e);
                    }
                }

                // Update the button states in the modal
                setModalActionButtonStates('approved');

                // Update the status badge
                $('#entityStatusBadge')
                    .text('Approved')
                    .attr('class', 'status-badge status-approved')
                    .show();

                $.ajax({
                    url: '<?php echo site_url("idcards/Idcards_controller/get_order_status_counts_ajax"); ?>',
                    type: 'POST',
                    data: { order_id: order_id },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            $('.status-tabs .status-tab[data-status="all"] .status-count').text(response.counts.all);
                            $('.status-tabs .status-tab[data-status="in review"] .status-count').text(response.counts.in_review);
                            $('.status-tabs .status-tab[data-status="approved"] .status-count').text(response.counts.approved);
                            $('.status-tabs .status-tab[data-status="removed"] .status-count').text(response.counts.removed);
                            $('.status-tabs .status-tab[data-status="re_ordered"] .status-count').text(response.counts.re_ordered);
                        }
                    }
                });

                refreshEntityData(function(data) {
                    // console.log('Entity data refreshed after approval');
                });

                resetApprovalUI();

                // Auto-navigate to next entity after approval
                setTimeout(function() {
                    if (
                        typeof window.currentEntityIndex !== 'undefined' &&
                        typeof window.visibleEntityIds !== 'undefined' &&
                        window.currentEntityIndex < window.visibleEntityIds.length - 1
                    ) {
                        window.currentEntityIndex++;
                        const nextEntityId = window.visibleEntityIds[window.currentEntityIndex];
                        if (typeof window.loadEntityData === 'function') {
                            window.loadEntityData(nextEntityId);
                        }
                    }
                }, 800);
            } else {
                updateProgressBar(100, 'Error: ' + (result.message || 'Failed to approve ID card'));
                showNotification('error', result.message || 'Failed to approve ID card');
                resetApprovalUI();
            }
            Swal.close();
            updateVisibleCount();
        },
        error: function(xhr, status, error) {
            console.error('Card generation error:', error);
            updateProgressBar(100, 'Error: Failed to generate card');
            showNotification('error', 'Failed to generate card');
            resetApprovalUI();
        }
    });
}


function resetApprovalUI() {
    $('#approval-progress-bar').css('width', '0%');
    $('#progress-percentage').text('0%');
    $('#progress-status-text').text('Preparing...');

    $('#approval-progress-container').hide();
    $('.id-card-actions').show();

    // Get the current entity row and status if available
    let currentStatus = 'in review';

    if (window.currentEntityIndex >= 0) {
        const visibleRows = $('.entity-table tbody tr:visible');
        if (visibleRows.length > 0 && window.currentEntityIndex < visibleRows.length) {
            const entityId = window.visibleEntityIds[window.currentEntityIndex];
            if (entityId) {
                const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
                if (entityRow.length) {
                    currentStatus = entityRow.attr('data-status') || 'in review';
                }
            }
        }
    }

    // Set correct enabled/disabled state for all three buttons
    setModalActionButtonStates(currentStatus);

    hideLoading();
    hideCustomApprovalLoadingOverlay();
}

function showCustomApprovalLoadingOverlay(percent) {
    // Show overlay only inside modal content, no blur
    $('#customApprovalLoadingOverlay').css({
        display: 'flex',
        position: 'absolute', // Overlay only modal content
        top: 0,
        left: 0,
        width: '100%',
        height: '100%'
    });
    updateCustomApprovalProgress(percent || 0);

    // Auto-navigate to next staff when progress completes
    if (percent === 100) {
        setTimeout(function() {
            // Try to move to next staff if possible
            if (
                typeof window.currentEntityIndex !== 'undefined' &&
                typeof window.visibleEntityIds !== 'undefined' &&
                window.currentEntityIndex < window.visibleEntityIds.length - 1
            ) {
                // Hide overlay before navigating
                hideCustomApprovalLoadingOverlay();
                // Move to next staff
                window.currentEntityIndex++;
                // Use the same loader as info-btn click
                const nextEntityId = window.visibleEntityIds[window.currentEntityIndex];
                // Load next entity data
                $('.id-card-actions').show(); // Ensure actions are visible for next
                // Use the global loadEntityData function
                if (typeof window.loadEntityData === 'function') {
                    window.loadEntityData(nextEntityId);
                }
            } else {
                // If no more staff, just hide overlay
                hideCustomApprovalLoadingOverlay();
            }
        }, 800); // Small delay for user to see 100%
    }
}
function hideCustomApprovalLoadingOverlay() {
    $('#customApprovalLoadingOverlay').hide();
}

function updateCustomApprovalProgress(percent) {
    var roundedPercent = Math.round(percent);
    $('#customApprovalProgressText').text(roundedPercent + '%');

    // Update circular progress bar
    var circle = document.querySelector('#customApprovalProgressCircle .progress-bar');
    if (circle) {
        var radius = circle.r.baseVal.value;
        var circumference = 2 * Math.PI * radius;
        var offset = circumference - (roundedPercent / 100) * circumference;
        circle.style.strokeDasharray = circumference;
        circle.style.strokeDashoffset = offset;
    }
}

// Move this function to global scope so it can be called from anywhere
window.loadEntityData = async function(entityId) {
    // Show loading indicator
    $('#modalLoadingIndicator').show();

    // Find the entity row - always get the fresh row to ensure we have the latest status
    const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
    if (!entityRow.length) {
        console.error('Entity row not found for ID:', entityId);
        $('#modalLoadingIndicator').hide();
        return;
    }

    // Get the current status directly from the table row
    const currentStatus = entityRow.attr('data-status') || 'in review';

    // Get entity data
    let rawData = entityRow.find('.info-btn').attr('data-entity');
    if (!rawData) {
        console.error('Entity data not found for ID:', entityId);
        $('#modalLoadingIndicator').hide();
        return;
    }

    let entity;
    try {
        entity = JSON.parse(decodeURIComponent(rawData));
        // Update the entity status in the data-entity attribute if it doesn't match the row status
        if (entity.status !== currentStatus) {
            entity.status = currentStatus;
            entityRow.find('.info-btn').attr('data-entity', encodeURIComponent(JSON.stringify(entity)));
        }
    } catch (e) {
        console.error('Error parsing entity data:', e);
        $('#modalLoadingIndicator').hide();
        return;
    }

    let entityData;
    // Process entity data based on type
    if (id_card_for.toLowerCase() === 'staff') {
        let faceImage = entity.picture_url;
        if(faceImage != ''){
            faceImage = await cropFaceFromPhoto(entity.picture_url);
        }
        entityData = {
            id: entity.avatar_id, // This is sm_id for staff
            name: entity.staff_name,
            employee_code: entity.employee_code,
            dob: entity.dob,
            date_of_birth: entity.dob,
            contact: entity.contact,
            blood_group: entity.blood_group,
            emergency_contact: entity.emergency_contact,
            address: entity.address,
            department: entity.department,
            designation: entity.designation,
            phone: entity.contact,
            photo: entity.picture_url,
            type: entity.type,
            status: currentStatus, // Add status to entityData
            relation_type : id_card_for,
            spouse_name:entity.spouse_name,
            staff_type:entity.staff_type,
            qualification : entity.qualification
        };
    } else {
        let parent_name_info = entity.father_name || '';
        let parent_contact_info = (entity.mother_contact || '') + ' ' + (entity.father_contact || '');

        if (entity.point_of_contact != null && entity.point_of_contact !== '') {
            // override like original logic
            parent_name_info = entity.parent_name;
            parent_contact_info = entity.contact;
        } else {
            // fallback to avatar_type logic
            switch (entity.avatar_type) {
                case 'Father':
                    parent_name_info = entity.father_name;
                    parent_contact_info = entity.father_contact;
                    break;
                case 'Mother':
                    parent_name_info = entity.mother_name;
                    parent_contact_info = entity.mother_contact;
                    break;
                case 'Guardian':
                    parent_name_info = entity.guardian_name;
                    parent_contact_info = entity.guardian_contact;
                    break;
                case 'Guardian_2':
                    parent_name_info = entity.guardian_2_name;
                    parent_contact_info = entity.guardian_2_contact;
                    break;
                case 'Driver':
                    parent_name_info = entity.driver_name;
                    parent_contact_info = entity.driver_contact;
                    break;
                case 'Driver_2':
                    parent_name_info = entity.driver_2_name;
                    parent_contact_info = entity.driver_2_contact;
                    break;
                default:
                    // If all else fails
                    parent_name_info = entity.parent_name || entity.father_name;
                    parent_contact_info = entity.contact || ((entity.mother_contact || '') + ' ' + (entity.father_contact || ''));
            }
        }
        entityData = {
            id: entity.sa_id,
            name: entity.name,
            admission_no: entity.admission_no,
            combination: entity.combination,
            alpha_rollnum: entity.alpha_rollnum,
            enrollment_no: entity.enrollment_no,
            grade_section: entity.grade_section,
            grade: entity.grade,
            dob: entity.dob,
            date_of_birth: entity.dob,
            contact: parent_contact_info,
            blood_group: entity.blood_group,
            address: entity.address,
            parent_name: parent_name_info,
            father_name: entity.father_name,
            mother_name: entity.mother_name,
            father_contact: entity.father_contact,
            mother_contact: entity.mother_contact,
            email: entity.email,
            phone: entity.preferred_contact_no,
            photo:  (entity.avatar_type == 'Father') ? entity.father_photo :
                    (entity.avatar_type == 'Mother') ? entity.mother_photo :
                    (entity.avatar_type == 'Guardian') ? entity.guardian_photo :
                    (entity.avatar_type == 'Guardian_2') ? entity.guardian_2_photo :
                    (entity.avatar_type == 'Driver') ? entity.driver_photo :
                    (entity.avatar_type == 'Driver_2') ? entity.driver_2_photo :
                    entity.picture_url,
            type: entity.type,
            father_address : entity.father_address,
            mother_address : entity.mother_address,
            qr_code : entity.qr_code,
            relation_type : id_card_for,
            sibling_name : entity.sibling_name,
            name_class : entity.name_class,
            picking_route : entity.picking_route,
            dropping_route : entity.dropping_route,
            status: currentStatus, // Add status to entityData
            mother_photo : entity.mother_photo,
            father_photo : entity.father_photo,
            class_section : entity.class_section,
            admission_acad_year : entity.admission_acad_year,
            identification_code: entity.identification_code
        };
    }

    // Reset approval UI for new staff details
    resetApprovalUI();

    // Store current entity data globally for access by other functions
    window.currentEntityData = entityData;

    // Update modal title
    $('#idCardModalTitle').text('ID Card Preview - ' + entityData.name);

    // Render preview (this will show another loading indicator)
    renderPreview(entityData);

    // Load any saved photo and name adjustments after a short delay to ensure DOM is ready
    setTimeout(() => {
        loadInlinePhotoAdjustments();
        loadNameAdjustments();
    }, 500);

    // Set up action buttons
    $('.id-card-approve')
        .data('staff-id', entityData.id)
        .data('type', entityData.relation_type)
        .off('click')
        .on('click', function () {
            const $btn = $(this);
            $btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Please wait...');
            // Only update status badge after approval is confirmed and successful
            approveIdCard({
                button: this,
                onSuccess: function() {
                    $('#entityStatusBadge')
                        .text('Approved')
                        .attr('class', 'status-badge status-approved')
                        .show();
                }
            });
        });
    $('.id-card-modify')
        .off('click')
        .on('click', function() {
            // Only update status badge after modification is confirmed and successful
            modifyIdCard(entityData, function() {
                $('#entityStatusBadge')
                    .text('Modify')
                    .attr('class', 'status-badge status-modify')
                    .show();
            });
        });
    $('.id-card-remove')
        .off('click')
        .on('click', function() {
            // Only update status badge after removal is confirmed and successful
            removeIdCard(entityData, function() {
                $('#entityStatusBadge')
                    .text('Removed')
                    .attr('class', 'status-badge status-removed')
                    .show();
            });
        });

    // Set correct enabled/disabled state for all three buttons
    setModalActionButtonStates(currentStatus);

    // Update navigation buttons state
    if (typeof updateNavigationButtons === 'function') updateNavigationButtons();

    // Hide loading indicator after everything is set up
    $('#modalLoadingIndicator').hide();
};

// Helper to update the entity position indicator in the modal
function updateEntityPositionIndicator() {
    // ...existing code...
    // Fix: Only count unique, visible entity IDs (avoid duplicates)
    if (
        typeof window.currentEntityIndex !== 'undefined' &&
        typeof window.visibleEntityIds !== 'undefined' &&
        Array.isArray(window.visibleEntityIds) &&
        window.visibleEntityIds.length > 0 &&
        window.currentEntityIndex >= 0
    ) {
        // Remove duplicates from visibleEntityIds
        const uniqueIds = [...new Set(window.visibleEntityIds)];
        $('#entityPositionIndicator')
            .text((window.currentEntityIndex + 1) + ' of ' + uniqueIds.length)
            .show();
    } else {
        $('#entityPositionIndicator').hide();
    }
}

// Add this function to update the status badge in the modal header
function updateEntityStatusBadge() {
    if (
        typeof window.currentEntityIndex !== 'undefined' &&
        typeof window.visibleEntityIds !== 'undefined' &&
        Array.isArray(window.visibleEntityIds) &&
        window.visibleEntityIds.length > 0 &&
        window.currentEntityIndex >= 0
    ) {
        const entityId = window.visibleEntityIds[window.currentEntityIndex];
        // Always get the fresh row to ensure we have the latest status
        const $row = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
        if ($row.length) {
            const status = $row.attr('data-status') || 'in review';

            // Set badge text and class with the correct styling
            if (status === 'approved') {
                $('#entityStatusBadge')
                    .text('Approved')
                    .attr('class', 'status-badge status-approved')
                    .show();
            } else if (status === 'modify') {
                $('#entityStatusBadge')
                    .text('Modify')
                    .attr('class', 'status-badge status-modify')
                    .show();
            } else if (status === 'removed') {
                $('#entityStatusBadge')
                    .text('Removed')
                    .attr('class', 'status-badge status-removed')
                    .show();
            } else {
                // Default to 'in review'
                $('#entityStatusBadge')
                    .text('In Review')
                    .attr('class', 'status-badge status-in-review')
                    .show();
            }

            // Also update the button states to match the current status
            setModalActionButtonStates(status);

            // Update the info-btn data-entity attribute to ensure it has the latest status
            const $infoBtn = $row.find('.info-btn');
            if ($infoBtn.length) {
                try {
                    let entityData = JSON.parse(decodeURIComponent($infoBtn.attr('data-entity')));
                    if (entityData.status !== status) {
                        entityData.status = status;
                        $infoBtn.attr('data-entity', encodeURIComponent(JSON.stringify(entityData)));
                    }
                } catch (e) {
                    console.error('Error updating info-btn data-entity:', e);
                }
            }
        } else {
            $('#entityStatusBadge').hide();
        }
    } else {
        $('#entityStatusBadge').hide();
    }
}

// Patch navigation and modal open to update the indicator and badge
function updateNavigationButtons() {
    // ...existing code...
    $('#prevStaffBtn').prop('disabled', window.currentEntityIndex <= 0);
    $('#nextStaffBtn').prop('disabled', window.currentEntityIndex >= window.visibleEntityIds.length - 1);
    updateEntityPositionIndicator();
    updateEntityStatusBadge();
}

// Patch window.loadEntityData to update the indicator and badge after loading
// Only patch if not already patched
if (!window._loadEntityDataPatched) {
    const originalLoadEntityData = window.loadEntityData;
    window.loadEntityData = async function(entityId) {
        await originalLoadEntityData.call(this, entityId);

        // Get the current status directly from the table row
        const entityRow = $(`.entity-table tbody tr[data-entity-id="${entityId}"]`);
        if (entityRow.length) {
            const currentStatus = entityRow.attr('data-status') || 'in review';

            // Update the status badge
            updateEntityStatusBadge();

            // Update the button states
            setModalActionButtonStates(currentStatus);
        }

        updateEntityPositionIndicator();
    };
    window._loadEntityDataPatched = true;
}

// Hide indicator and badge when modal is cleared
function clearModalContent() {
    $('#idCardModalTitle').text('ID Card Preview');
    $('#frontCardPreview').empty();
    $('#backCardPreview').empty();
    $('#entityPositionIndicator').hide();
    $('#entityStatusBadge').hide();
    resetApprovalUI();
}

 function generate_qr_code(dataType, qrdata, qr_size, callback){
    $.ajax({
        url: "<?php echo site_url('idcards/Idcards_controller/generate_qr_code_for_idcards') ?>",
        data: {dataType:dataType, qrdata: qrdata, qr_size:qr_size},
        success: function (base64img) {
            callback(base64img);
        }
    });
}

// Photo Adjustment Functionality - Inline Controls
$(document).ready(function() {
    // Inline zoom controls
    $('#zoomInBtnInline').on('click', function() {
        const slider = $('#zoomSliderInline');
        const currentValue = parseFloat(slider.val());
        const newValue = Math.min(3, currentValue + 0.1);
        slider.val(newValue);
        updateInlinePhotoZoom(newValue);
    });

    $('#zoomOutBtnInline').on('click', function() {
        const slider = $('#zoomSliderInline');
        const currentValue = parseFloat(slider.val());
        const newValue = Math.max(0.5, currentValue - 0.1);
        slider.val(newValue);
        updateInlinePhotoZoom(newValue);
    });

    $('#zoomSliderInline').on('input', function() {
        const scale = parseFloat($(this).val());
        updateInlinePhotoZoom(scale);
    });

    // Inline alignment controls
    $('#alignLeftBtnInline').on('click', function() {
        const slider = $('#alignmentSliderInline');
        const currentValue = parseFloat(slider.val());
        const newValue = Math.max(0, currentValue - 5);
        slider.val(newValue);
        updateInlinePhotoAlignment(newValue);
    });

    $('#alignRightBtnInline').on('click', function() {
        const slider = $('#alignmentSliderInline');
        const currentValue = parseFloat(slider.val());
        const newValue = Math.min(100, currentValue + 5);
        slider.val(newValue);
        updateInlinePhotoAlignment(newValue);
    });

    $('#alignmentSliderInline').on('input', function() {
        const alignment = parseFloat($(this).val());
        updateInlinePhotoAlignment(alignment);
    });

    // Vertical alignment controls
    $('#alignUpBtnInline').on('click', function() {
        const slider = $('#verticalAlignmentSliderInline');
        const currentValue = parseFloat(slider.val());
        const newValue = Math.max(0, currentValue - 5);
        slider.val(newValue);
        updateVerticalPhotoAlignment(newValue);
    });

    $('#alignDownBtnInline').on('click', function() {
        const slider = $('#verticalAlignmentSliderInline');
        const currentValue = parseFloat(slider.val());
        const newValue = Math.min(100, currentValue + 5);
        slider.val(newValue);
        updateVerticalPhotoAlignment(newValue);
    });

    $('#verticalAlignmentSliderInline').on('input', function() {
        const alignment = parseFloat($(this).val());
        updateVerticalPhotoAlignment(alignment);
    });

    // Inline reset button
    $('#resetPhotoBtnInline').on('click', function() {
        resetInlinePhotoPosition();
    });

    // Name Size Adjustment Tool - Compatible with older browsers
    $('#decreaseNameSize').on('click', function() {
        var slider = $('#nameSizeSlider');
        var currentValue = parseInt(slider.val());
        var newValue = Math.max(8, currentValue - 1);
        slider.val(newValue);
        adjustNameSize(newValue);
    });

    $('#increaseNameSize').on('click', function() {
        var slider = $('#nameSizeSlider');
        var currentValue = parseInt(slider.val());
        var newValue = Math.min(36, currentValue + 1);
        slider.val(newValue);
        adjustNameSize(newValue);
    });

    $('#nameSizeSlider').on('input change', function() {
        var fontSize = parseInt($(this).val());
        adjustNameSize(fontSize);
    });

    $('#resetNameSize').on('click', function() {
        resetNameSizeToDefault();
    });

    // Field selector change event
    $('#fieldSelector').on('change', function() {
        var selectedField = $(this).val();

        // Debug: Show all available text in template
        debugTemplateText();

        // Debug: Compare name vs father_name detection
        debugFieldDetection();

        loadFieldAdjustment(selectedField);
        console.log('Field selector changed to:', selectedField);
    });

    // Prevent dropdown from closing when interacting with controls
    $('.adjustment-menu .dropdown-menu').on('click', function(e) {
        e.stopPropagation();
    });

    // Close dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.adjustment-menu').length) {
            $('.adjustment-menu .dropdown-menu').removeClass('show');
        }
    });
});

// Global variables for inline photo adjustment
let inlinePhotoAdjustment = {
    scale: 1,
    alignment: 50, // 0 = far left, 50 = center, 100 = far right
    verticalAlignment: 50 // 0 = top, 50 = center, 100 = bottom
};

// Inline photo adjustment functions
function updateInlinePhotoZoom(scale) {
    inlinePhotoAdjustment.scale = scale;
    applyInlinePhotoAdjustment();
}

function updateInlinePhotoAlignment(alignment) {
    inlinePhotoAdjustment.alignment = alignment;
    applyInlinePhotoAdjustment();
    // console.log('Photo alignment set to:', alignment + '%');
}

function updateVerticalPhotoAlignment(alignment) {
    inlinePhotoAdjustment.verticalAlignment = alignment;
    applyInlinePhotoAdjustment();
}

function resetInlinePhotoPosition() {
    inlinePhotoAdjustment.scale = 1;
    inlinePhotoAdjustment.alignment = 50; // Reset to center (50%)
    inlinePhotoAdjustment.verticalAlignment = 50; // Reset to center (50%)
    $('#zoomSliderInline').val(1);
    $('#alignmentSliderInline').val(50);
    $('#verticalAlignmentSliderInline').val(50);

    applyInlinePhotoAdjustment();
}

function applyInlinePhotoAdjustment() {
    // Find all photo elements in the current template and apply adjustments
    const photoElements = $('#frontCardPreview .element img[id*="photo-"], #backCardPreview .element img[id*="photo-"]');
    const shapePhotoElements = $('#frontCardPreview .element .shape-container img, #backCardPreview .element .shape-container img');

    // Combine both sets of photo elements
    const allPhotoElements = photoElements.add(shapePhotoElements);

    allPhotoElements.each(function() {
        const $img = $(this);

        // Calculate alignment-based object-position using percentage
        // Horizontal: 0 = far left (0%), 50 = center (50%), 100 = far right (100%)
        // Vertical: 0 = top (0%), 50 = center (50%), 100 = bottom (100%)
        const horizontalPosition = inlinePhotoAdjustment.alignment;
        const verticalPosition = inlinePhotoAdjustment.verticalAlignment || 50;
        const objectPosition = `${horizontalPosition}% ${verticalPosition}%`;

        // Calculate vertical transform for additional positioning (only for vertical)
        const translateY = (verticalPosition - 50) * 0.5; // Convert to translate offset

        // Apply the adjustments using CSS transform and object-position
        // Keep original horizontal behavior with object-position, add translateY for vertical
        $img.css({
            'transform': `scale(${inlinePhotoAdjustment.scale}) translateY(${translateY}%)`,
            'transform-origin': 'center center',
            'object-position': objectPosition,
            'object-fit': 'cover',
            'transition': 'transform 0.3s ease, object-position 0.3s ease'
        });

        // Ensure the container has proper dimensions and overflow
        $img.closest('.element, .shape-container').css({
            'overflow': 'hidden',
            'position': 'relative',
            'display': 'block'
        });

        // Ensure the container has overflow hidden
        $img.closest('.element, .shape-container').css({
            'overflow': 'hidden'
        });
    });

    // Store the adjustment data globally for persistence
    if (window.currentEntityData) {
        window.currentEntityData.inlinePhotoAdjustment = {
            scale: inlinePhotoAdjustment.scale,
            alignment: inlinePhotoAdjustment.alignment,
            timestamp: Date.now()
        };
    }
}

// Function to load saved inline photo adjustments when entity data is loaded
function loadInlinePhotoAdjustments() {
    if (window.currentEntityData && window.currentEntityData.inlinePhotoAdjustment) {
        const adjustment = window.currentEntityData.inlinePhotoAdjustment;

        // Update the controls to reflect saved settings
        inlinePhotoAdjustment.scale = adjustment.scale;
        inlinePhotoAdjustment.alignment = adjustment.alignment;

        $('#zoomSliderInline').val(adjustment.scale);
        $('#alignmentSliderInline').val(adjustment.alignment);

        // Apply the adjustments
        applyInlinePhotoAdjustment();

        console.log('Loaded saved inline photo adjustment:', adjustment);
    }
}

// Debug function to show all text in template
function debugTemplateText() {
    console.log('=== DEBUGGING TEMPLATE TEXT ===');
    $('#frontCardPreview .element, #backCardPreview .element').each(function(index) {
        var $this = $(this);
        var text = $this.text().replace(/\s+/g, ' ').replace(/^\s+|\s+$/g, '');
        if (text.length > 0) {
            console.log('Element ' + index + ':', text);
        }
    });
    console.log('=== END DEBUG ===');
}

// Debug function to compare name vs father_name detection
function debugFieldDetection() {
    console.log('=== COMPARING NAME vs FATHER_NAME DETECTION ===');

    // Test name detection
    console.log('--- Testing NAME detection ---');
    var nameElements = $('#frontCardPreview .element, #backCardPreview .element').filter(function() {
        var $this = $(this);
        var text = $this.text().replace(/\s+/g, ' ').replace(/^\s+|\s+$/g, '');

        var found = text.indexOf('[[NAME]]') !== -1 ||
                   text.indexOf('NAME') !== -1 ||
                   $this.find('[data-field="name"]').length > 0 ||
                   $this.hasClass('name-field') ||
                   $this.find('.name-field').length > 0;

        if (found) {
            console.log('✓ NAME FOUND in:', '"' + text + '"');
            console.log('  - Contains [[NAME]]:', text.indexOf('[[NAME]]') !== -1);
            console.log('  - Contains NAME:', text.indexOf('NAME') !== -1);
            console.log('  - Has data-field="name":', $this.find('[data-field="name"]').length > 0);
            console.log('  - Has name-field class:', $this.hasClass('name-field'));
        }
        return found;
    });
    console.log('NAME elements found:', nameElements.length);

    // Test father_name detection
    console.log('--- Testing FATHER_NAME detection ---');
    var fatherElements = $('#frontCardPreview .element, #backCardPreview .element').filter(function() {
        var $this = $(this);
        var text = $this.text().replace(/\s+/g, ' ').replace(/^\s+|\s+$/g, '');

        var found = text.indexOf('[[FATHER_NAME]]') !== -1 ||
                   text.indexOf('FATHER_NAME') !== -1 ||
                   $this.find('[data-field="father_name"]').length > 0 ||
                   $this.hasClass('father-name-field') ||
                   $this.find('.father-name-field').length > 0;

        if (found) {
            console.log('✓ FATHER_NAME FOUND in:', '"' + text + '"');
            console.log('  - Contains [[FATHER_NAME]]:', text.indexOf('[[FATHER_NAME]]') !== -1);
            console.log('  - Contains FATHER_NAME:', text.indexOf('FATHER_NAME') !== -1);
            console.log('  - Has data-field="father_name":', $this.find('[data-field="father_name"]').length > 0);
            console.log('  - Has father-name-field class:', $this.hasClass('father-name-field'));
        } else {
            console.log('✗ FATHER_NAME NOT FOUND in:', '"' + text + '"');
        }
        return found;
    });
    console.log('FATHER_NAME elements found:', fatherElements.length);

    console.log('=== END COMPARISON ===');
}

// Name Size Adjustment Functions - Compatible with older JavaScript
var currentNameSize = 14; // Default font size

function adjustNameSize(fontSize) {
    currentNameSize = fontSize;

    // Update the display badge
    $('#currentNameSize').text(fontSize + 'px');

    // Get selected field type
    var selectedField = $('#fieldSelector').val();

    // Find and adjust elements for the selected field
    var nameElements = findNameElements(selectedField);

    if (nameElements.length === 0) {
        console.log('No ' + selectedField + ' elements found to adjust');
        return;
    }

    nameElements.each(function() {
        var $element = $(this);

        // Apply font size to override template styles
        $element.css('font-size', fontSize + 'px');
        $element.css('line-height', '1.2');
        $element.attr('style', $element.attr('style') + '; font-size: ' + fontSize + 'px;');

        // Also apply to child text elements
        $element.find('*').each(function() {
            var $child = $(this);
            if ($child.text().trim().length > 0 && $child.find('img').length === 0) {
                // $child.css('font-size', fontSize + 'px');
                $child.css('line-height', '1.2');
                // $child.attr('style', $child.attr('style') + '; font-size: ' + fontSize + 'px;');
            }
        });
    });

    // Save the adjustment for this entity
    if (window.currentEntityData) {
        if (!window.currentEntityData.fieldAdjustments) {
            window.currentEntityData.fieldAdjustments = {};
        }
        window.currentEntityData.fieldAdjustments[selectedField] = {
            fontSize: fontSize,
            timestamp: new Date().getTime()
        };
    }

    console.log('Adjusted ' + nameElements.length + ' ' + selectedField + ' elements to ' + fontSize + 'px');
}

function findNameElements(fieldType) {
    // Default to 'name' if no field type specified
    if (!fieldType) {
        fieldType = 'name';
    }

    console.log('=== SEARCHING FOR FIELD TYPE:', fieldType, 'USING ORIGINAL PLACEHOLDERS ===');

    // Look for elements that originally contained the field placeholders
    // We need to check data attributes or classes that were set during template rendering
    var foundElements = $('#frontCardPreview .element, #backCardPreview .element').filter(function() {
        var $this = $(this);

        // Check for data attributes that indicate the original field type
        var dataField = $this.attr('data-field') || $this.find('[data-field]').attr('data-field');
        var dataFieldName = $this.attr('data-field-name') || $this.find('[data-field-name]').attr('data-field-name');

        console.log('Checking element with data-field:', dataField, 'data-field-name:', dataFieldName);

        // Check based on field type
        if (fieldType === 'name') {
            // Look for NAME field indicators
            var found = (dataField && (dataField === 'name' || dataField === 'student_name')) ||
                       (dataFieldName && (dataFieldName === '[[NAME]]' || dataFieldName === 'NAME')) ||
                       $this.hasClass('name-field') ||
                       $this.find('.name-field').length > 0;

            if (found) {
                console.log('✓ FOUND NAME field');
            }
            return found;
        }
        else if (fieldType === 'father_name') {
            // Look for FATHER_NAME field indicators
            var found = (dataField && (dataField === 'father_name' || dataField === 'fathername')) ||
                       (dataFieldName && (dataFieldName === '[[FATHER_NAME]]' || dataFieldName === 'FATHER_NAME')) ||
                       $this.hasClass('father-name-field') ||
                       $this.find('.father-name-field').length > 0;

            if (found) {
                console.log('✓ FOUND FATHER_NAME field');
            }
            return found;
        }
        else if (fieldType === 'mother_name') {
            // Look for MOTHER_NAME field indicators
            var found = (dataField && (dataField === 'mother_name' || dataField === 'mothername')) ||
                       (dataFieldName && (dataFieldName === '[[MOTHER_NAME]]' || dataFieldName === 'MOTHER_NAME')) ||
                       $this.hasClass('mother-name-field') ||
                       $this.find('.mother-name-field').length > 0;

            if (found) {
                console.log('✓ FOUND MOTHER_NAME field');
            }
            return found;
        }

        return false;
    });

    // If no elements found using data attributes, try to find by field mapping
    if (foundElements.length === 0) {
        console.log('No elements found by data attributes, trying field mapping approach...');
        foundElements = findElementsByFieldMapping(fieldType);
    }

    console.log('=== RESULT: Found', foundElements.length, 'elements for', fieldType, '===');
    return foundElements;
}

// Helper function to find elements by field mapping (when data attributes are not available)
function findElementsByFieldMapping(fieldType) {
    console.log('Using field mapping approach for:', fieldType);

    // Get all text elements and their positions
    var allTextElements = [];
    $('#frontCardPreview .element, #backCardPreview .element').each(function(index) {
        var text = $(this).text().replace(/\s+/g, ' ').replace(/^\s+|\s+$/g, '');
        if (text.length > 0 && !$(this).find('img').length) {
            allTextElements.push({
                element: $(this),
                text: text,
                index: index
            });
        }
    });

    console.log('Found', allTextElements.length, 'text elements for mapping');
    allTextElements.forEach(function(item, i) {
        console.log('Text element', i + ':', '"' + item.text + '" at DOM index', item.index);
    });

    // Based on your debug output pattern:
    // Element 0: "Aadhya Chandra" (student name)
    // Element 1: "Montessori-1 A" (class)
    // Element 2: "CHANDRASHEKAR" (father name)
    // Element 3: "9739000601" (father phone)
    // Element 4: "DIVYA R" (mother name)
    // Element 5: "9341850633" (mother phone)
    // Element 6: "O +ve" (blood group)

    if (fieldType === 'name') {
        // Find the student name - first element that looks like a name
        for (var i = 0; i < allTextElements.length; i++) {
            var item = allTextElements[i];
            console.log('Checking name candidate:', '"' + item.text + '"');

            // Student name pattern: Mixed case with space, not all caps
            if (item.text.match(/^[A-Z][a-z]+ [A-Z][a-z]+$/) ||
                (item.text.match(/^[A-Za-z\s]+$/) && item.text !== item.text.toUpperCase() && item.text.indexOf(' ') !== -1)) {
                console.log('✓ FOUND student name:', '"' + item.text + '"');
                return item.element;
            }
        }
    }
    else if (fieldType === 'father_name') {
        // Find father name - specifically the first all-caps name (not phone number)
        for (var i = 0; i < allTextElements.length; i++) {
            var item = allTextElements[i];
            console.log('Checking father name candidate:', '"' + item.text + '"');

            // Father name: All caps, contains letters, not a phone number, not blood group
            if (item.text === item.text.toUpperCase() &&
                item.text.match(/^[A-Z\s]+$/) &&
                !item.text.match(/^\d/) &&
                !item.text.match(/\+ve$/) &&
                item.text.length > 3) {

                console.log('✓ FOUND father name:', '"' + item.text + '"');
                return item.element;
            }
        }
    }
    else if (fieldType === 'mother_name') {
        // Find mother name - the second all-caps name (after father name)
        var foundFatherName = false;
        for (var i = 0; i < allTextElements.length; i++) {
            var item = allTextElements[i];
            console.log('Checking mother name candidate:', '"' + item.text + '"');

            // Check if this looks like a name (all caps, letters only)
            if (item.text === item.text.toUpperCase() &&
                item.text.match(/^[A-Z\s]+$/) &&
                !item.text.match(/^\d/) &&
                !item.text.match(/\+ve$/) &&
                item.text.length > 3) {

                if (!foundFatherName) {
                    // This is likely the father name, skip it
                    foundFatherName = true;
                    console.log('Skipping father name:', '"' + item.text + '"');
                    continue;
                } else {
                    // This is likely the mother name
                    console.log('✓ FOUND mother name:', '"' + item.text + '"');
                    return item.element;
                }
            }
        }
    }

    console.log('No element found for field type:', fieldType);
    return $();
}

function resetNameSizeToDefault() {
    var defaultSize = 14;
    var selectedField = $('#fieldSelector').val();
    currentNameSize = defaultSize;

    $('#nameSizeSlider').val(defaultSize);
    $('#currentNameSize').text(defaultSize + 'px');

    adjustNameSize(defaultSize);

    console.log('Reset ' + selectedField + ' size to default: ' + defaultSize + 'px');
}

function loadFieldAdjustment(fieldType) {
    var defaultSize = 14;

    if (window.currentEntityData && window.currentEntityData.fieldAdjustments && window.currentEntityData.fieldAdjustments[fieldType]) {
        var adjustment = window.currentEntityData.fieldAdjustments[fieldType];
        var fontSize = adjustment.fontSize;

        currentNameSize = fontSize;
        $('#nameSizeSlider').val(fontSize);
        $('#currentNameSize').text(fontSize + 'px');

        // Apply the saved adjustment after a short delay to ensure DOM is ready
        setTimeout(function() {
            adjustNameSize(fontSize);
        }, 100);

        console.log('Loaded saved ' + fieldType + ' adjustment: ' + fontSize + 'px');
    } else {
        // Set to default if no saved adjustment for this field
        currentNameSize = defaultSize;
        $('#nameSizeSlider').val(defaultSize);
        $('#currentNameSize').text(defaultSize + 'px');

        setTimeout(function() {
            adjustNameSize(defaultSize);
        }, 100);

        console.log('No saved adjustment for ' + fieldType + ', using default: ' + defaultSize + 'px');
    }
}

function loadNameAdjustments() {
    // Reset field selector to default (name)
    $('#fieldSelector').val('name');

    // Load adjustment for the default field (name)
    loadFieldAdjustment('name');

    console.log('Loaded field adjustments for entity');
}

function findPhotoElementInTemplate() {
    // Look for photo element in the current template design
    try {
        // First priority: Check if we have template data available
        if (window.currentTemplateData && window.currentTemplateData.front_design) {
            const frontDesign = JSON.parse(window.currentTemplateData.front_design);
            if (frontDesign.elements) {
                for (let element of frontDesign.elements) {
                    if ((element.type === 'image' && element.properties && element.properties.fieldName === '[[PHOTO]]') ||
                        (element.type === 'shape' && element.properties && element.properties.fieldName === '[[PHOTO]]')) {
                        console.log('Found photo element from template data:', element);
                        return element;
                    }
                }
            }
        }

        // Second priority: try to find photo element in the DOM with more accurate detection
        const photoElements = $('#frontCardPreview .element img[id*="photo-"], #frontCardPreview .element .shape-container img');
        if (photoElements.length > 0) {
            const photoImg = photoElements.first();
            const photoContainer = photoImg.closest('.element');
            if (photoContainer.length > 0) {
                // Get the actual rendered dimensions and position
                const containerRect = photoContainer[0].getBoundingClientRect();
                const previewRect = $('#frontCardPreview')[0].getBoundingClientRect();

                // Calculate relative position within the preview
                const relativeX = containerRect.left - previewRect.left;
                const relativeY = containerRect.top - previewRect.top;

                const element = {
                    x: relativeX,
                    y: relativeY,
                    width: photoContainer.outerWidth(),
                    height: photoContainer.outerHeight(),
                    properties: {
                        fieldName: '[[PHOTO]]',
                        shapeType: photoImg.hasClass('photo-in-circle') || photoImg.closest('.shape-container').length > 0 ? 'circle' : 'rectangle'
                    }
                };

                console.log('Found photo element from DOM:', element);
                return element;
            }
        }

        // Third priority: Check back card if front card doesn't have photo
        const backPhotoElements = $('#backCardPreview .element img[id*="photo-"], #backCardPreview .element .shape-container img');
        if (backPhotoElements.length > 0) {
            const photoImg = backPhotoElements.first();
            const photoContainer = photoImg.closest('.element');
            if (photoContainer.length > 0) {
                const containerRect = photoContainer[0].getBoundingClientRect();
                const previewRect = $('#backCardPreview')[0].getBoundingClientRect();

                const relativeX = containerRect.left - previewRect.left;
                const relativeY = containerRect.top - previewRect.top;

                const element = {
                    x: relativeX,
                    y: relativeY,
                    width: photoContainer.outerWidth(),
                    height: photoContainer.outerHeight(),
                    properties: {
                        fieldName: '[[PHOTO]]',
                        shapeType: photoImg.hasClass('photo-in-circle') || photoImg.closest('.shape-container').length > 0 ? 'circle' : 'rectangle'
                    }
                };

                console.log('Found photo element from back card DOM:', element);
                return element;
            }
        }

        // Default fallback
        console.warn('Using default photo element dimensions');
        return {
            x: 50,
            y: 50,
            width: 100,
            height: 120,
            properties: {
                fieldName: '[[PHOTO]]',
                shapeType: 'rectangle'
            }
        };
    } catch (e) {
        console.error('Error finding photo element:', e);
        return null;
    }
}

function setupPhotoContainer(photoElement) {
    const container = $('#photoContainer');
    const isCircle = photoElement.properties && photoElement.properties.shapeType === 'circle';

    // Set container dimensions to exactly match the photo element from ID card
    const exactWidth = Math.round(photoElement.width);
    const exactHeight = Math.round(photoElement.height);

    container.css({
        width: exactWidth + 'px',
        height: exactHeight + 'px',
        borderRadius: isCircle ? '50%' : '8px',
        minWidth: exactWidth + 'px',
        minHeight: exactHeight + 'px',
        maxWidth: exactWidth + 'px',
        maxHeight: exactHeight + 'px'
    });

    // Store the exact dimensions for reference
    photoAdjustment.containerWidth = exactWidth;
    photoAdjustment.containerHeight = exactHeight;
    photoAdjustment.containerElement = container[0];

    console.log(`Photo container set to exact dimensions: ${exactWidth}x${exactHeight}px, Circle: ${isCircle}`);
}

function loadPhotoForAdjustment(photoUrl) {
    const img = $('#photoPreviewImage');

    img.off(); // Remove any existing event handlers

    img.on('load', function() {
        // Set initial photo size to fit the container properly
        const imgElement = this;
        const containerWidth = photoAdjustment.containerWidth;
        const containerHeight = photoAdjustment.containerHeight;

        // Calculate the scale to fit the photo in the container (similar to object-fit: cover)
        const imgAspectRatio = imgElement.naturalWidth / imgElement.naturalHeight;
        const containerAspectRatio = containerWidth / containerHeight;

        let initialWidth, initialHeight;

        if (imgAspectRatio > containerAspectRatio) {
            // Image is wider than container - fit by height
            initialHeight = containerHeight;
            initialWidth = initialHeight * imgAspectRatio;
        } else {
            // Image is taller than container - fit by width
            initialWidth = containerWidth;
            initialHeight = initialWidth / imgAspectRatio;
        }

        // Set the photo dimensions
        img.css({
            width: initialWidth + 'px',
            height: initialHeight + 'px',
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            'transform-origin': 'center center'
        });

        // Store initial dimensions
        photoAdjustment.initialWidth = initialWidth;
        photoAdjustment.initialHeight = initialHeight;

        setupPhotoInteraction();
        resetPhotoPosition();

        console.log(`Photo loaded: ${imgElement.naturalWidth}x${imgElement.naturalHeight}, Container: ${containerWidth}x${containerHeight}, Initial: ${initialWidth}x${initialHeight}`);
    });

    img.attr('src', photoUrl);
    photoAdjustment.photoElement = img[0];
}

function setupPhotoInteraction() {
    const img = $('#photoPreviewImage');

    // Mouse events for dragging
    img.on('mousedown', function(e) {
        e.preventDefault();
        photoAdjustment.isDragging = true;
        photoAdjustment.startX = e.clientX - photoAdjustment.x;
        photoAdjustment.startY = e.clientY - photoAdjustment.y;
        $(document).css('cursor', 'grabbing');
    });

    $(document).on('mousemove', function(e) {
        if (photoAdjustment.isDragging) {
            const deltaX = e.clientX - photoAdjustment.startX;
            const deltaY = e.clientY - photoAdjustment.startY;

            // Constrain movement within reasonable bounds
            const maxMove = Math.max(photoAdjustment.containerWidth, photoAdjustment.containerHeight);
            photoAdjustment.x = Math.max(-maxMove, Math.min(maxMove, deltaX));
            photoAdjustment.y = Math.max(-maxMove, Math.min(maxMove, deltaY));

            updatePhotoTransform();
        }
    });

    $(document).on('mouseup', function() {
        if (photoAdjustment.isDragging) {
            photoAdjustment.isDragging = false;
            $(document).css('cursor', 'default');
        }
    });

    // Touch events for mobile
    img.on('touchstart', function(e) {
        e.preventDefault();
        const touch = e.originalEvent.touches[0];
        photoAdjustment.isDragging = true;
        photoAdjustment.startX = touch.clientX - photoAdjustment.x;
        photoAdjustment.startY = touch.clientY - photoAdjustment.y;
    });

    $(document).on('touchmove', function(e) {
        if (photoAdjustment.isDragging) {
            e.preventDefault();
            const touch = e.originalEvent.touches[0];
            const deltaX = touch.clientX - photoAdjustment.startX;
            const deltaY = touch.clientY - photoAdjustment.startY;

            // Constrain movement within reasonable bounds
            const maxMove = Math.max(photoAdjustment.containerWidth, photoAdjustment.containerHeight);
            photoAdjustment.x = Math.max(-maxMove, Math.min(maxMove, deltaX));
            photoAdjustment.y = Math.max(-maxMove, Math.min(maxMove, deltaY));

            updatePhotoTransform();
        }
    });

    $(document).on('touchend', function() {
        photoAdjustment.isDragging = false;
    });
}

function updatePhotoZoom(scale) {
    photoAdjustment.scale = scale;
    updatePhotoTransform();
}

function updatePhotoTransform() {
    const img = $('#photoPreviewImage');

    // Combine the centering transform with the user adjustments
    const centerX = -50; // -50% for centering
    const centerY = -50; // -50% for centering

    // Convert pixel adjustments to percentage of container
    const containerWidth = photoAdjustment.containerWidth || 100;
    const containerHeight = photoAdjustment.containerHeight || 120;

    const adjustX = (photoAdjustment.x / containerWidth) * 100;
    const adjustY = (photoAdjustment.y / containerHeight) * 100;

    img.css({
        transform: `translate(${centerX + adjustX}%, ${centerY + adjustY}%) scale(${photoAdjustment.scale})`,
        top: '50%',
        left: '50%'
    });
}

function resetPhotoPosition() {
    photoAdjustment.scale = 1;
    photoAdjustment.x = 0;
    photoAdjustment.y = 0;
    $('#zoomSlider').val(1);

    // Reset to center position with initial sizing
    const img = $('#photoPreviewImage');
    img.css({
        transform: 'translate(-50%, -50%) scale(1)',
        top: '50%',
        left: '50%'
    });
}

function savePhotoAdjustment() {
    // Store the adjustment data globally
    if (!window.currentEntityData) {
        Swal.fire({
            title: 'Error',
            text: 'No entity data available.',
            icon: 'error',
            confirmButtonText: 'OK'
        });
        return;
    }

    // Store photo adjustment data in the current entity
    window.currentEntityData.photoAdjustment = {
        scale: photoAdjustment.scale,
        x: photoAdjustment.x,
        y: photoAdjustment.y,
        timestamp: Date.now()
    };

    // Apply the adjustment to the current template
    applyPhotoAdjustmentToTemplate();

    Swal.fire({
        title: 'Photo Adjusted',
        text: 'Photo adjustment has been saved and applied to the ID card.',
        icon: 'success',
        confirmButtonText: 'OK'
    }).then(() => {
        $('#photoAdjustmentModal').modal('hide');
    });
}

function applyPhotoAdjustmentToTemplate() {
    // Find all photo elements in the current template and apply adjustments
    const photoElements = $('#frontCardPreview .element img[id*="photo-"], #backCardPreview .element img[id*="photo-"]');

    photoElements.each(function() {
        const $img = $(this);
        const adjustment = window.currentEntityData.photoAdjustment;

        if (adjustment) {
            // Apply the same transform that was used in the adjustment modal
            $img.css({
                'transform': `translate(${adjustment.x}px, ${adjustment.y}px) scale(${adjustment.scale})`,
                'transform-origin': 'center center',
                'transition': 'transform 0.3s ease'
            });

            // Ensure the image container has overflow hidden to clip the adjusted photo
            $img.closest('.element').css({
                'overflow': 'hidden'
            });
        }
    });

    // Also apply to shape elements that contain photos
    const shapePhotoElements = $('#frontCardPreview .element .shape-container img, #backCardPreview .element .shape-container img');

    shapePhotoElements.each(function() {
        const $img = $(this);
        const adjustment = window.currentEntityData.photoAdjustment;

        if (adjustment) {
            $img.css({
                'transform': `translate(${adjustment.x}px, ${adjustment.y}px) scale(${adjustment.scale})`,
                'transform-origin': 'center center',
                'transition': 'transform 0.3s ease'
            });

            // Ensure the shape container has overflow hidden
            $img.closest('.shape-container').css({
                'overflow': 'hidden'
            });
        }
    });
}

function applySavedPhotoAdjustment(imgId) {
    // Check if there are saved photo adjustments for the current entity
    if (!window.currentEntityData || !window.currentEntityData.photoAdjustment) {
        return;
    }

    const adjustment = window.currentEntityData.photoAdjustment;
    const $img = $('#' + imgId);

    if ($img.length > 0) {
        // Apply the saved adjustment
        $img.css({
            'transform': `translate(${adjustment.x}px, ${adjustment.y}px) scale(${adjustment.scale})`,
            'transform-origin': 'center center',
            'transition': 'none' // No transition for initial load
        });

        // Ensure the container has overflow hidden
        $img.closest('.element, .shape-container').css({
            'overflow': 'hidden'
        });

        console.log('Applied saved photo adjustment:', adjustment);
    }
}

// Function to load photo adjustments when entity data is loaded
function loadPhotoAdjustments() {
    if (window.currentEntityData && window.currentEntityData.photoAdjustment) {
        // Apply adjustments to all existing photo elements
        const photoElements = $('#frontCardPreview img[id*="photo-"], #backCardPreview img[id*="photo-"]');
        photoElements.each(function() {
            const imgId = $(this).attr('id');
            if (imgId) {
                applySavedPhotoAdjustment(imgId);
            }
        });
    }
}

</script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


<style>
    /* Entity Table Styles */
    .entity-container {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .filter-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .status-tabs {
        display: flex;
        gap: 5px;
    }

    .status-tab {
        background-color: #6c757d;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 6px 12px;
        font-size: 14px;
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .status-tab.active {
        background-color: #343a40;
    }

    .status-count {
        background-color: white;
        color: #343a40;
        border-radius: 50%;
        padding: 2px 6px;
        font-size: 12px;
        margin-left: 5px;
    }

    .search-filters {
        display: flex;
        gap: 10px;
    }

    .search-input {
        border-radius: 4px;
        border: 1px solid #ced4da;
        padding: 6px 12px;
        width: 250px;
    }

    .filter-select {
        border-radius: 4px;
        border: 1px solid #ced4da;
        padding: 6px 12px;
        width: 120px;
    }

    .view-toggle {
        background-color: #6c757d;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 6px 12px;
        cursor: pointer;
    }

    .entity-table-container {
        background-color: white;
        border-radius: 4px;
        overflow: hidden;
        height: 350px;
        overflow: scroll;
    }

    .entity-table {
        width: 100%;
        border-collapse: collapse;
    }

    .entity-table th {
        background-color: #f8f9fa;
        padding: 12px 15px;
        text-align: left;
        font-weight: 500;
        color: #495057;
        border-bottom: 1px solid #dee2e6;
    }

    .entity-table td {
        padding: 12px 15px;
        border-bottom: 1px solid #dee2e6;
        color: #212529;
    }

    .entity-table tr:hover {
        background-color: #f8f9fa;
    }

    .actions-cell {
        width: 100px;
        text-align: center;
    }

    .action-btn {
        background: none;
        border: none;
        color: #6c757d;
        cursor: pointer;
        padding: 4px;
        margin: 0 2px;
    }

    .action-btn:hover {
        color: #343a40;
    }

    .entity-count {
        color: #6c757d;
        font-size: 14px;
        margin-top: 10px;
    }

    /* Add styles for the status badge */
    .status-cell {
        text-align: center;
        width: 120px;
    }
    .status-badge {
        display: inline-block;
        padding: 4px 10px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        text-transform: capitalize;
        text-align: center;
        min-width: 100px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    /* In Review */

    .status-in-review {
        border-radius: 4px;
        border: 1px solid #B0DEFF;
        background: #E6F4FF;
        color:#0088E8;
    }

    /* Approved (using Delivered style) */
    .status-approved {
        border-radius: 4px;
        border: 1px solid #C5E9CD;
        background: #ECF8EF;
        color: #3DA755;
    }

    /* Modify (using Order Placed style) */
    .status-modify {
        border-radius: 4px;
        border: 1px solid #FFE5B0;
        background: #FFF7E6;
        color: #E89B00;
    }

    /* Removed (using Cancelled style) */
    .status-removed {
        border-radius: 4px;
        border: 1px solid #FAC5C3;
        background: #FDECEC;
        color:#D93E39;
    }


    /* ID Card Preview Modal Styles */
    .id-card-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 1000;
        overflow: auto;
    }

    .id-card-modal-content {
        background-color: #fff;
        margin: 2% auto;
        padding: 20px;
        border-radius: 8px;
        width: 90%;
        max-width: 900px;
        position: relative;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        overflow-y: auto;
        max-height: 90vh;
        display: flex;
        flex-direction: column;
    }

    .id-card-close {
        position: absolute;
        top: 10px;
        right: 15px;
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
        z-index: 20; /* Ensure it's above other elements */
        width: 30px;
        height: 30px;
        text-align: center;
        line-height: 30px;
        background-color: #f8f9fa;
        border-radius: 50%;
        border: 1px solid #dee2e6;
        transition: all 0.2s ease;
    }

    .id-card-close:hover {
        background-color: #e9ecef;
        border-color: #ced4da;
        color: #dc3545; /* Red color on hover */
    }

    .id-card-preview-container {
        display: flex;
        justify-content: center;
        gap: 30px;
        margin: 20px 0;
        flex-wrap: wrap;
    }

    .id-card-preview-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 20px;
    }
 .status-button {
      display: inline-flex;
      align-items: center;
      background-color: #f3f3f3;
      border: 1px solid #d0d0d0;
      border-radius: 16px;
      padding: 4px 12px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      color: #666;
    }

    .status-button .dot {
      width: 8px;
      height: 8px;
      background-color: #666;
      border-radius: 50%;
      margin-right: 8px;
    }
    .card-preview {
        border: 1px solid #ccc;
        position: relative;
        overflow: hidden;
        margin: 0 auto;
        background: #fff;
        width: 215px;
        height: 335px;
        display: block;
    }

    .card-size-portrait {
        width: 215px;
        height: 335px;
    }

    .card-size-landscape {
        width: 335px;
        height: 215px;
    }

    .element {
        position: absolute;
    }

    .element-content {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        white-space: nowrap;
        overflow: visible;
    }

    .shape-circle {
        border-radius: 50%;
    }

    img.element-content {
        object-fit: cover;
        max-width: 100%;
        max-height: 100%;
    }

    .shape-container {
        position: relative;
        overflow: hidden;
    }

    .photo-in-shape,
    .photo-in-circle {
        object-fit: cover;
        width: 100%;
        height: 100%;
        /* Default object-position will be set by face detection */
        transition: object-position 0.3s ease;
        /* Enhanced styles for high-resolution 300 DPI images */
        image-rendering: -webkit-optimize-contrast;
        image-rendering: -moz-crisp-edges;
        image-rendering: crisp-edges;
        image-rendering: high-quality;
        -ms-interpolation-mode: bicubic;
        backface-visibility: hidden;
        transform: translateZ(0);
    }

    .photo-in-circle {
        border-radius: 50%;
    }

    /* High-quality photo enhancements for 300 DPI */
    .high-quality-photo {
        image-rendering: -webkit-optimize-contrast !important;
        image-rendering: -moz-crisp-edges !important;
        image-rendering: crisp-edges !important;
        image-rendering: high-quality !important;
        -ms-interpolation-mode: bicubic !important;
        backface-visibility: hidden;
        transform: translateZ(0);
        will-change: transform;
        /* Enhanced filters for 300 DPI quality */
        filter: contrast(1.02) brightness(1.01) saturate(1.05);
    }

    /* Ensure all ID card images use high quality rendering */
    .element-content img,
    .card-preview img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: -moz-crisp-edges;
        image-rendering: crisp-edges;
        image-rendering: high-quality;
        -ms-interpolation-mode: bicubic;
    }

    /* Additional 300 DPI optimizations */
    .card-preview {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
        image-rendering: high-quality;
    }

    .card-size-portrait {
        width: 215px;
        height: 335px;
    }

    /* .card-size-landscape {
        width: 86mm;
        height: 54mm;
    } */

    .element {
        position: absolute;
    }

    /* This is a duplicate rule that has been updated above */

    .shape-circle {
        border-radius: 50%;
    }

    .shape-rectangle {
        border-radius: 0;
    }

    img.element-content {
        object-fit: cover;
    }

    .shape-container {
        position: relative;
        overflow: hidden;
    }

    .photo-in-shape,
    .photo-in-circle {
        object-fit: cover;
        width: 100%;
        height: 100%;
        /* Default object-position will be set by face detection */
        transition: object-position 0.3s ease;
        /* Enhanced styles for high-resolution 300 DPI images */
        image-rendering: -webkit-optimize-contrast;
        image-rendering: -moz-crisp-edges;
        image-rendering: crisp-edges;
        image-rendering: high-quality;
        -ms-interpolation-mode: bicubic;
        backface-visibility: hidden;
        transform: translateZ(0);
    }

    .photo-in-circle {
        border-radius: 50%;
    }

    .id-card-actions {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        padding: 15px 0;
        border-top: 1px solid #eee;
        width: 100%;
    }

    .id-card-action-btn {
        flex: 1;
        padding: 10px 0;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        font-weight: 500;
        font-size: 14px;
        letter-spacing: 0.5px;
        transition: all 0.2s ease;
        box-shadow: none;
        margin: 0;
        border-radius: 0;
    }

    .id-card-approve {
        background-color: transparent;
        border-top: 1px solid #495057;
        border-bottom: 1px solid #495057;
        border-left: 1px solid #495057;
        border-right: none;
        color: #495057;
    }

    .id-card-approve:hover {
        background-color: #495057;
        color: white;
    }

    .id-card-modify {
        background-color: transparent;
        border-top: 1px solid #495057;
        border-bottom: 1px solid #495057;
        border-left: none;
        border-right: none;
        color: #495057;
    }

    .id-card-modify:hover {
        background-color: #495057;
        color: white;
    }

    .id-card-remove {
        background-color: transparent;
        border-top: 1px solid #495057;
        border-bottom: 1px solid #495057;
        border-right: 1px solid #495057;
        border-left: none;
        color: #495057;
    }

    .id-card-remove:hover {
        background-color: #495057;
        color: white;
    }

    /* Active state for all buttons */
    .id-card-action-btn:active {
        background-color: #343a40 !important;
        color: white !important;
    }

    /* Disabled state for all buttons */
    .id-card-action-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background-color: #f8f9fa !important;
        color: #6c757d !important;
        position: relative;
    }

    /* Add a diagonal line through disabled buttons */
    .id-card-action-btn:disabled::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: repeating-linear-gradient(
            45deg,
            rgba(0, 0, 0, 0.05),
            rgba(0, 0, 0, 0.05) 5px,
            transparent 5px,
            transparent 10px
        );
        border-radius: 4px;
    }

    .id-card-photo {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* Status Badge Styles - Duplicate styles removed to avoid conflicts */
    /* Progress Bar Styles */
    #approval-progress-container {
        margin: 20px 0;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }

    /* Improved Action Button Styles */
    .action-btn {
        width: 40px;          /* Increased button width */
        height: 40px;         /* Increased button height */
        border-radius: 4px;   /* Rounded corners */
        margin: 0 5px;        /* Add spacing between buttons */
        border: 1px solid #ddd;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
        position: relative;   /* For tooltip positioning */
        cursor: pointer;
        display: inline-block; /* Ensure buttons are side by side */
        vertical-align: middle; /* Align buttons vertically */
    }

    /* Button container styling */
    .button-container {
        display: inline-flex;
        gap: 5px;
    }

    .action-btn i {
        font-size: 18px;      /* Larger icons */
        color: #495057;       /* Darker icon color */
    }

    .action-btn:hover {
        background-color: #e9ecef;
        border-color: #ced4da;
    }

    .action-btn:active {
        background-color: #dee2e6;
    }

    /* Edit button specific styling */
    .edit-btn {
        background-color: #e8f4f8;
        border-color: #bee5eb;
    }

    .edit-btn i {
        color: #495057;
    }

    .edit-btn:hover {
        background-color: #d1ecf1;
    }

    /* Info button specific styling */
    .info-btn {
        background-color: transparent;
        border-color: rgba(184, 218, 255, 0.5);
    }

    .info-btn i {
        color: #495057;
    }

    .info-btn:hover {
        background-color: rgba(204, 229, 255, 0.3);
    }

    .info-btn:active {
        background-color: rgba(204, 229, 255, 0.5);
    }

    /* Disabled button styling */
    .action-btn:disabled,
    .action-btn[disabled] {
        background-color: #f2f2f2 !important;
        border-color: #e0e0e0 !important;
        cursor: not-allowed;
        opacity: 0.8;
        position: relative;
    }

    /* Special styling for approved buttons */
    .approved-btn {
        background-color: transparent !important; /* Transparent background to match uploaded image */
        border-color: rgba(200, 230, 201, 0.5) !important;
    }

    .approved-btn i {
        color: #4caf50 !important; /* Green check icon */
    }

    /* Regular disabled button icon styling */
    .action-btn:disabled:not(.approved-btn) i,
    .action-btn[disabled]:not(.approved-btn) i {
        color: #adb5bd !important;
    }

    /* Add a subtle border for disabled buttons */
    .action-btn:disabled::after,
    .action-btn[disabled]::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 4px;
        box-shadow: inset 0 0 0 2px rgba(0, 0, 0, 0.1);
    }

    /* Actions cell styling */
    .actions-cell {
        min-width: 120px;
        position: relative;
        padding: 10px 5px !important;
        white-space: nowrap;  /* Prevent buttons from wrapping */
        text-align: center;   /* Center the buttons */
    }

    .progress-status {
        margin-bottom: 8px;
        font-weight: 500;
        color: #495057;
    }

    .progress-bar-container {
        height: 20px;
        background-color: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 8px;
    }

    .progress-bar {
        height: 100%;
        background-color: #28a745;
        width: 0%;
        transition: width 0.3s ease;
    }

    .progress-percentage {
        text-align: right;
        font-size: 14px;
        color: #6c757d;
    }

    /* Navigation buttons styles */
    .id-card-navigation {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none; /* Prevents blocking modal content, see below */
        z-index: 10;
    }
    .id-card-navigation h3 {
        display: none; /* Hide in navigation bar, but keep for modal header */
    }
    .id-card-nav-btn {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: auto;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 50%;
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: #495057;
        transition: background 0.2s, border 0.2s;
        box-shadow: 0 2px 6px rgba(0,0,0,0.08);
    }
    .id-card-nav-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
    #prevStaffBtn {
        left: 0;
    }
    #nextStaffBtn {
        right: 0;
    }
    .id-card-nav-btn:hover:not(:disabled) {
        background: #e9ecef;
        border-color: #adb5bd;
        color: #007bff;
    }

    /* Modal loading indicator styles */
    #modalLoadingIndicator {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        z-index: 10;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .modal-loading-spinner {
        text-align: center;
        padding: 30px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .modal-loading-spinner i {
        color: #007bff;
        margin-bottom: 15px;
    }

    .modal-loading-spinner p {
        margin: 0;
        color: #495057;
        font-weight: 500;
    }

    /* Preview loading indicator styles */
    .preview-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        width: 100%;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 4px;
        border: 1px dashed #dee2e6;
    }

    .preview-loading i {
        color: #007bff;
        margin-bottom: 10px;
    }

    .preview-loading p {
        margin: 0;
        color: #6c757d;
        font-size: 14px;
    }

    /* Custom Approval Loading Overlay Styles */
    #customApprovalLoadingOverlay {
        position: fixed;
        z-index: 2000;
        top: 0; left: 0; right: 0; bottom: 0;
        background: rgba(180,180,180,0.7);
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .custom-approval-loading-bg {
        position: absolute;
        top:0; left:0; right:0; bottom:0;
        background: rgba(180,180,180,0.7);
        z-index: 1;
    }
    .custom-approval-loading-content {
        position: relative;
        z-index: 2;
        background: transparent;
        text-align: center;
        padding: 40px 30px 30px 30px;
        border-radius: 12px;
        min-width: 320px;
    }
    .custom-approval-title {
        font-size: 1.5rem;
        font-weight: bold;
        color: #444;
        margin-bottom: 8px;
    }
    .custom-approval-desc {
        color: #555;
        margin-bottom: 24px;
    }
    .custom-approval-progress-img {
        position: relative;
        display: inline-block;
    }
    .custom-approval-progress-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 2rem;
        font-weight: bold;
        color: #444;
        pointer-events: none;
    }
    .blurred-bg {
        filter: blur(3px);
        pointer-events: none;
        user-select: none;
    }
    .circular-progress {
        position: relative;
        width: 120px;
        height: 120px;
        display: block;
        margin: auto;
    }
    .circular-progress svg {
        transform: rotate(-90deg);
    }
    .circular-progress .progress-bg {
        fill: none;
        stroke: #e6e6e6;
        stroke-width: 10;
    }
    .circular-progress .progress-bar {
        fill: none;
        stroke: #007bff;
        stroke-width: 10;
        stroke-linecap: round;
        stroke-dasharray: 283;
        stroke-dashoffset: 283;
        transition: stroke-dashoffset 0.4s ease;
    }
    .circular-progress .custom-approval-progress-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 2rem;
        font-weight: bold;
        color: #444;
        pointer-events: none;
    }

    /* Modal header title styling (keep visible at top) */
    .card-header h6,
    #idCardModalTitle {
        display: block !important;
        text-align: center;
        font-size: 1.2rem;
        font-weight: 600;
        margin: 0;
        padding: 0;
    }
    /* Special styling for locked action buttons */
    .id-card-action-btn.locked-action:disabled {
        background-color: #f1f1f1 !important;
        color: #888 !important;
        border-color: #ddd !important;
    }

    .id-card-action-btn.locked-action:disabled::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: repeating-linear-gradient(
            45deg,
            rgba(0, 0, 0, 0.03),
            rgba(0, 0, 0, 0.03) 5px,
            transparent 5px,
            transparent 10px
        );
        border-radius: 4px;
    }

    /* Lock icon styling */
    .id-card-action-btn.locked-action:disabled i.fa-lock {
        color: #999 !important;
        font-size: 14px;
    }
    /* Locked Order Banner Styling */
    .locked-order-banner {
        background-color: #fff8e1;
        border: 1px solid #ffe082;
        border-left: 4px solid #ffa000;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .locked-order-content {
        display: flex;
        align-items: center;
    }

    .lock-icon {
        background-color: #ffa000;
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        flex-shrink: 0;
    }

    .lock-icon i {
        font-size: 18px;
    }

    .lock-message {
        flex-grow: 1;
    }

    .lock-message h5 {
        margin: 0 0 5px 0;
        color: #f57c00;
        font-weight: 600;
    }

    .lock-message p {
        margin: 0;
        color: #5d4037;
        font-size: 14px;
    }

    /* Locked action button styling in table */
    .action-btn.locked-action:disabled {
        background-color: #f5f5f5 !important;
        border-color: #e0e0e0 !important;
    }

    .action-btn.locked-action:disabled i.fa-lock {
        color: #999 !important;
    }

    /* Add a tooltip to explain why buttons are locked */
    .action-btn.locked-action:disabled:hover::before {
        content: attr(title);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 1000;
        pointer-events: none;
        margin-bottom: 5px;
    }
    /* Base styling for name and parent name fields */
    .name-field .element-content,
    .parent-name-field .element-content {
        line-height: 1.2 !important; /* Adjust line height for better readability */
        white-space: nowrap !important; /* Allow text to wrap if needed */
        overflow: visible !important;
        text-overflow: ellipsis !important;
       /* Enable hyphenation for very long words */
        text-align: center /* Center text by default (will be overridden for long names) */
    }

    /* Additional styling for name field */
    .name-field .element-content {
        font-weight: 600; /* Make name slightly bolder */
    }

    /* Additional styling for parent name field */
    .parent-name-field .element-content {
        font-style: normal !important; /* Ensure parent name is not italic by default */
    }

    /* Inline Photo Adjustment Controls Styles */
    .photo-adjustment-controls {
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 8px 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .zoom-control-inline {
        border-right: 1px solid #eee;
        padding-right: 10px;
    }

    .zoom-slider-inline {
        height: 4px;
        border-radius: 2px;
        background: #ddd;
        outline: none;
        -webkit-appearance: none;
    }

    .zoom-slider-inline::-webkit-slider-thumb {
        appearance: none;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #007bff;
        cursor: pointer;
        border: 2px solid #fff;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .zoom-slider-inline::-moz-range-thumb {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #007bff;
        cursor: pointer;
        border: 2px solid #fff;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .alignment-control-inline {
        border-right: 1px solid #eee;
        padding-right: 10px;
    }

    .alignment-slider-inline {
        height: 4px;
        border-radius: 2px;
        background: #ddd;
        outline: none;
        -webkit-appearance: none;
    }

    .alignment-slider-inline::-webkit-slider-thumb {
        appearance: none;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #28a745;
        cursor: pointer;
        border: 2px solid #fff;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .alignment-slider-inline::-moz-range-thumb {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #28a745;
        cursor: pointer;
        border: 2px solid #fff;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    /* Adjustment Menu Dropdown Styling */
    .adjustment-menu .dropdown-menu {
        border: 1px solid #dee2e6;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        border-radius: 6px;
    }

    .adjustment-menu .dropdown-header {
        color: #495057;
        font-weight: 600;
        font-size: 13px;
        padding: 8px 0 4px 0;
    }

    .adjustment-menu .form-group {
        margin-bottom: 12px;
    }

    .adjustment-menu .form-group label {
        color: #6c757d;
        font-weight: 500;
        margin-bottom: 4px;
    }

    .adjustment-menu .form-control-range {
        padding: 0;
        height: 1.2rem;
    }

    .adjustment-menu .btn-sm {
        padding: 4px 8px;
        font-size: 12px;
    }

    .adjustment-menu .dropdown-divider {
        margin: 12px 0;
        border-color: #dee2e6;
    }

    .adjustment-menu .badge {
        font-size: 10px;
        padding: 3px 6px;
    }

    /* Prevent dropdown from closing when interacting with controls */
    .adjustment-menu .dropdown-menu {
        padding: 15px;
    }

    .adjustment-menu .dropdown-menu:hover {
        display: block;
    }
</style>


