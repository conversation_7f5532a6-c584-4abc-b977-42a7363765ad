<?php
/**
 * Name:    Oxygen
 * Author:  <PERSON>
 *          <EMAIL>
 *
 * Created:  20 June 2025
 *
 * Description: Only Report section Latest Boostrap.
 *
 * Requirements: PHP5 or above
 *
 */

defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Reports Fees_structure
*/
class Reports_v3 extends CI_Controller {

     private $yearId;
	public function __construct() {
    parent::__construct();
    if (!$this->ion_auth->logged_in()) {
      redirect('auth/login', 'refresh');
    }
    if (!$this->authorization->isModuleEnabled('FEESV2')) {
      redirect('dashboard', 'refresh');
    }
    $this->load->model('feesv2/reports_model');
    $this->load->model('student/Student_Model');
    $this->load->model('feesv2/fees_collection_model');
    $this->load->model('feesv2/fees_student_model');
    $this->load->model('feesv2/fees_cohorts_model');
    $this->load->model('payment_model');
    $this->load->helper('texting_helper');
    $this->load->helper('fees_helper');
    $this->load->model('avatar');
    $this->load->library('filemanager');
    $this->load->model('communication/emails_model', 'emails');
    $this->yearId = $this->acad_year->getAcadYearId();
  }

  public function day_books1(){
    $data['report_title'] = 'Daily Transaction Report';
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints_daily_transactions();
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['payment_mode'] = [];
    if(!empty($data['fee_blueprints'])){
      $data['payment_mode'] = json_decode($data['fee_blueprints'][0]->allowed_payment_modes);
    }
    $data['additionalAmount'] = $this->reports_model->get_payment_options_additional_amount();
    $data['sales'] = $this->authorization->isModuleEnabled('SALES');
    $data['admission'] = $this->authorization->isModuleEnabled('ADMISSION');
    $data['main_content'] = 'feesv2/reports/daily_transaction_report_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

  public function student_wise_fees_details($fee_type ='', $clsId =''){
    $data['report_title'] = 'Fee Detail Report';
    $data['fee_type'] = $fee_type;
    $data['clsId'] = $clsId;
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['combination'] = $this->Student_Model->getCombList();
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['rteType'] = $this->settings->getSetting('rte');
    $data['category'] = $this->settings->getSetting('category');
    $data['donors'] = $this->Student_Model->getDonorList();
    $data['admission_status'] = $this->settings->getSetting('admission_status');
    $data['loan_column'] = $this->settings->getSetting('loan_provider_charges');
    $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fee_fine_amount'] = $this->settings->getSetting('fee_fine_amount');
    $data['refundAmount'] = $this->settings->getSetting('fee_refund_amount_display');
    $data['main_content'] = 'feesv2/reports/student_fees_summary_v2';
    $this->load->view('inc/template_reportV3', $data);
    // $this->load->view('feesv2/reports/inc/reports_header', $data);
  }

    public function balance_report($fee_type=''){
    $data['report_title'] = 'Fees Balance / SMS Report';
    $data['fee_type'] = $fee_type;
    $data['rteType'] = $this->settings->getSetting('rte');
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['email_template'] = $this->reports_model->get_email_template_for_fee();
    $data['sms_template'] = $this->reports_model->get_sms_template_for_fee();
    $data['email_option'] = $this->settings->getSetting('fee_balance_from_email');
    $data['admissionStatusArr'] = $this->settings->getSetting('admission_status');
    $data['donors'] = $this->Student_Model->getDonorList();
    $data['combinationList'] = $this->Student_Model->getCombinations();
    $data['student_names'] = $this->reports_model->get_fees_balance_student_names();
    $data['adSelected'] = ['1','2']; // 2 is default select admission status approved
    if ($this->mobile_detect->isMobile()) {
      $data['main_content'] = 'feesv2/reports/balance_sms_mobile_v2';
    }else{
      $data['main_content'] = 'feesv2/reports/balance_sms_v2';     
    }
    $this->load->view('inc/template_reportV3', $data);
  }

  
  public function reconciled_report_v2(){
    $data['report_title'] = 'Reconciled Report';
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $fee_type_id = $this->input->post('fee_type');
    $reconcilation = $this->input->post('reconcilation');
    $from_date = $this->input->post('from_date');
    $to_date = $this->input->post('to_date');
    $data['selected_blueprint'] = $fee_type_id;
    $data['reconcilation'] = $reconcilation;
    $data['from_date'] = $from_date;
    $data['to_date'] = $to_date;
    $data['reconciled'] = $this->reports_model->get_reconciled_details($fee_type_id, $reconcilation, $from_date, $to_date);
    // echo "<pre>"; print_r($data['reconciled']); die();
    // $data['main_content'] = 'feesv2/reports/non_reconciled';
    $data['main_content'] = 'feesv2/student/non_reconciled_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

    public function daily_transcation_prarthana_new_v2(){
    $data['report_title'] = 'Daily Transaction Fee Type';
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints_all_branches();
    $data['main_content'] = 'feesv2/reports/daily_transcation_prarthana_new_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

  public function fine_waiver_report(){
    $data['report_title'] = 'Fine Waiver Report';
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['main_content'] = 'feesv2/reports/fine_waiver_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

    public function concessions_day_report_v2($fee_type ='') {
    $data['report_title'] = 'Concession Day Report';
    $data['fee_type'] = $fee_type;
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['main_content'] = 'feesv2/reports/view_concession_day_report_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

    public function management_summary_date_wise_v2(){
    $data['report_title'] = 'Fees Collection Summary Date Range';
    $data['fee_blueprints'] = $this->fees_student_model->get_all_blueprints_collection_summary();
    $data['additionalAmount'] = $this->reports_model->get_payment_options_additional_amount();
    $data['sales'] = $this->authorization->isModuleEnabled('SALES');
    $data['admission'] = $this->authorization->isModuleEnabled('ADMISSION');
    $data['main_content'] = 'feesv2/reports/management_day_wise_summary_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

  public function fees_edit_history_report_v2(){
    $data['report_title'] = 'Edit History Report';
    $data['main_content'] = 'feesv2/reports/fees_edit_history_report_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

  public function concessions_new_v2($fee_type ='') {
    $data['report_title'] = 'Fees Concessions Report';
    $data['fee_type'] = $fee_type;
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['main_content'] = 'feesv2/reports/concessions_new_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

  public function daily_transcation_summary_v2(){
    $data['report_title'] = 'Daily Challan Details';
    $date = $this->input->post('date');
    if (empty($date)) {
      $data['cDate']= date('d-m-Y');
    }else{
      $data['cDate']=  $date;
    }
    $data['allDate'] = $this->reports_model->get_all_dates_transcation();

    $data['total_challanAmount'] = $this->reports_model->getChallanAmountforDaywise($data['cDate']);
    $data['total_bankAmount'] = $this->reports_model->getBankAmountforDaywise($data['cDate']);
    $data['main_content'] = 'feesv2/reports/daily_transcation_summary_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

  public function fine_report_v2(){
    $data['report_title'] = 'Fine Report';
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['main_content'] = 'feesv2/reports/fine_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

  public function tally_report_v2(){
    $data['report_title'] = 'Tally Report';
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints_daily_transactions();
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['payment_mode'] = [];
    if(!empty($data['fee_blueprints'])){
      $data['payment_mode'] = json_decode($data['fee_blueprints'][0]->allowed_payment_modes);
    }
    $data['additionalAmount'] = $this->reports_model->get_payment_options_additional_amount();
    $data['sales'] = $this->authorization->isModuleEnabled('SALES');
    $data['admission'] = $this->authorization->isModuleEnabled('ADMISSION');
    $data['main_content'] = 'feesv2/reports/tally_report_view_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

  public function excess_report_v2(){
    $data['report_title'] = 'Excess Fee Report';
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['main_content'] = 'feesv2/reports/excess_fee_report_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

  public function refund_reports_v2(){
    $data['report_title'] = 'Refund Report';
    // $from_date = $this->input->post('from_date');
    // $to_date = $this->input->post('to_date');
    // $data['from_date'] = $from_date;
    // $data['to_date'] = $to_date;
    // $data['refunds'] = $this->refund_model->get_refund_details($from_date, $to_date);
    // echo "<pre>"; print_r($data['refunds']); die();
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['main_content'] = 'feesv2/reports/refund_reports_new_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

  public function cheques_report_v2(){
    $data['report_title'] = 'Cheque Report';
    $data['fee_blueprint']= $this->reports_model->get_fee_blueprint_cheques();
    $data['main_content'] = 'feesv2/student/cheques_report_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

  public function daily_transaction_v2(){
    $data['report_title'] = 'Daily Transaction Online Report';
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints_daily_transactions();
    $data['payment_mode'] = json_decode($data['fee_blueprints'][0]->allowed_payment_modes);
    $data['admission'] = $this->authorization->isModuleEnabled('ADMISSION');
    $data['main_content'] = 'feesv2/reports/daily_report_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

  public function class_summary_new_v2(){
    $data['report_title'] = 'Class Wise Summary';
    $data['loan_column'] = $this->settings->getSetting('loan_provider_charges');
    $data['adjustment'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fineAmount'] = $this->settings->getSetting('fee_fine_amount');
    $data['feeRefund'] = $this->settings->getSetting('fee_refund_amount_display');
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['main_content'] = 'feesv2/reports/class_summary_new_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

  public function class_daily_summary_new_v2(){
    $data['report_title'] = 'Class Level Date-Wise Summary';
    $data['loan_column'] = $this->settings->getSetting('loan_provider_charges');
    $data['adjustment'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fineAmount'] = $this->settings->getSetting('fee_fine_amount');
    $data['feeRefund'] = $this->settings->getSetting('fee_refund_amount_display');
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['main_content'] = 'feesv2/reports/class_daily_summary_new_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

  public function management_summary_v2($admission_status = null){
    $data['report_title'] = 'Fee Management Report';
    $admStatus = $this->input->post('admission_status');
  
    if(empty($admStatus)){
      $admStatus = ['2'];
    }
    $data['adSelected'] = $admStatus;
    $data['fee_management'] = $this->reports_model->get_fee_management_summary($admStatus);
    $data['loan_column'] = $this->settings->getSetting('loan_provider_charges');
    $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fee_fine_amount'] = $this->settings->getSetting('fee_fine_amount');
    $data['fee_refund_amount'] = $this->settings->getSetting('fee_refund_amount_display');
    $data['admissionStatus'] = $this->settings->getSetting('admission_status');
    //echo "<pre>"; print_r($data['admissionStatus']);die();

    //if ($this->mobile_detect->isTablet()) {
    //  $data['main_content'] = 'feesv2/reports/management_summary_mobile';
    //} else if ($this->mobile_detect->isMobile()) {
    //  $data['main_content'] = 'feesv2/reports/management_summary_mobile';
    //} else {
      $data['main_content'] = 'feesv2/reports/management_summary_v2';
    //}
    $this->load->view('inc/template_reportV3', $data);
  }

  public function view_fee_assign_view_count_v2(){
    $data['report_title'] = 'Fees assigned summary';
    $data['fee_types'] = $this->fees_student_model->get_fee_types_all();

    if(!empty($data['fee_types'])){
      $blueprint_id = $this->input->post('blueprint_id');
      if (empty($blueprint_id))
        $data['selectedBP'] = $data['fee_types'][0]->id;
      else
        $data['selectedBP'] = $blueprint_id;

      // We'll load the data via AJAX, so we don't need to fetch it here
    }

    $data['rte_type'] = $this->settings->getSetting('rte');
    $data['main_content'] = 'feesv2/reports/view_assign_fee_summary_count_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

  //payment controller functions
  public function online_transaction_report_v2(){
    $data['report_title'] = 'Online Transaction Report';
			if (!$this->ion_auth->logged_in()) {
				redirect('auth/login', 'refresh');
			}
			if (!$this->authorization->isModuleEnabled('FEESV2') || !$this->authorization->isAuthorized('FEESV2.VIEW_ONLINE_TX_REPORT')) {
				redirect('dashboard', 'redirect');
			}

			// $data['transaction_list'] = $this->payment_model->get_online_transaction_data();	
			$data['main_content'] = 'feesv2/online_transaction_report/index_v2';
			$this->load->view('inc/template_reportV3', $data);
		}

    public function online_challan_payment_report_v2(){
      $data['report_title'] = 'Online Challan Payment report';
			if (!$this->ion_auth->logged_in()) {
				redirect('auth/login', 'refresh');
			}
			if (!$this->authorization->isModuleEnabled('FEESV2') || !$this->authorization->isAuthorized('FEESV2.VIEW_ONLINE_CHALLAN_REPORT')) {
				redirect('dashboard', 'redirect');
			}

			// $data['transaction_list'] = $this->payment_model->get_online_transaction_data();	
			$data['main_content'] = 'feesv2/online_transaction_report/challan_v2';
			$this->load->view('inc/template_reportV3', $data);
		}

    public function online_settlement_report_v2() {
      $data['report_title'] = 'Online Payment Settlement report';
			if (!$this->ion_auth->logged_in()) {
				redirect('auth/login', 'refresh');
			}
			// if (!$this->authorization->isModuleEnabled('FEESV2') || !$this->authorization->isAuthorized('FEESV2.VIEW_ONLINE_TX_REPORT')) {
			// 	redirect('dashboard', 'redirect');
			// }
			$data['transaction_dates'] = $this->payment_model->get_trans_dates();
			$data['main_content'] = 'feesv2/settlement/online_payment_settlement_report_v2';
			$this->load->view('inc/template_reportV3', $data);
		}

    public function online_refund_report_v2(){
      $data['report_title'] = 'Online Refund Report';
			$data['main_content'] = 'feesv2/online_transaction_report/online_refund_v2';
			$this->load->view('inc/template_reportV3', $data);
		}

    public function online_settlement_report_consolidated_v2(){
      $data['report_title'] = 'Overview Online Payment Settlement Report';
      if (!$this->ion_auth->logged_in()) {
        redirect('auth/login', 'refresh');
      }

      if (!$this->authorization->isModuleEnabled('FEESV2') || !$this->authorization->isAuthorized('FEESV2.OVERVIEW_ONLINE_SETTLEMENT_REPORT')) {
        redirect('dashboard', 'redirect');
      }

      $data['main_content'] = 'feesv2/settlement/overview_online_settlement_v2';
      $this->load->view('inc/template_reportV3', $data);
    }

    ///////////////////////////////////////////////////////////////

    public function student_wise_fees_summary_details_v2_new(){
    $data['report_title'] = 'Fee Summary Detail Report';
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    // $data['combination'] = $this->Student_Model->getCombList();
    $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['rteType'] = $this->settings->getSetting('rte');
    $data['admission_status'] = $this->settings->getSetting('admission_status');
    // $data['category'] = $this->settings->getSetting('category');
    // $data['donors'] = $this->Student_Model->getDonorList();

    $data['loan_column'] = $this->settings->getSetting('loan_provider_charges');
    $data['fee_adjustment_amount'] = $this->settings->getSetting('fee_adjustment_amount');
    $data['fee_fine_amount'] = $this->settings->getSetting('fee_fine_amount');
    $data['refund_amount'] = $this->settings->getSetting('fee_refund_amount_display');
    // $data['fee_data'] = $this->reports_model->get_fees_summary_data();
    $data['main_content'] = 'feesv2/reports/student_fees_summary_overall_v2_new';
    $this->load->view('inc/template_reportV3', $data);
  }

  public function fees_collection_status_v2(){
    $data['report_title'] = 'Fee Collection Status';
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $fee_type_id = $this->input->post('fee_type');
    if (empty($fee_type_id)) {
      $fee_blueprints = $this->reports_model->get_blueprints_selection($data['fee_blueprints'][0]->id);
    }else{
      $fee_blueprints = $this->reports_model->get_blueprints_selection($fee_type_id);
    }
    $data['selected_blueprint'] = $fee_blueprints->id;
    $data['installment_types'] = $this->reports_model->get_installments_types($data['selected_blueprint']);
    $data['classes'] = $this->Student_Model->getClassNames();
    $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    $data['main_content'] = 'feesv2/reports/fee_collect_status_v2';
    $this->load->view('inc/template_reportV3', $data);
  }

  public function fees_component_detail_v2($fee_type ='', $clsId =''){
    $data['report_title'] = 'Fee Component Detail Report';
    // $data['components'] = $this->fees_student_model->get_blueprint_components();
    $data['classes'] = $this->Student_Model->getClassNames();
    // $data['classSectionList'] = $this->Student_Model->getClassSectionNames();
    // $data['admission_status'] = $this->settings->getSetting('admission_status');
    // $data['admission_type'] = $this->settings->getSetting('admission_type');
    $data['fee_blueprints'] = $this->fees_student_model->get_blueprints();
    $data['main_content'] = 'feesv2/reports/fees_component_detail_v2';
    $this->load->view('inc/template_reportV3', $data);
  }
}