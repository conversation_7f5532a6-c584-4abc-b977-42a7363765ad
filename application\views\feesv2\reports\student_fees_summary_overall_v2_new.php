<?php include(APPPATH . 'views/feesv2/reports/components/report_header.php'); ?>

<div class="card-body">
      <div class="col-md-12">
        <div id="all-filters" class="row" style="margin: 0px">
          <div class="col-md-3 form-group" id="multiBlueprintSelect">
            <p>Select Fee Type</p>
            <select class="form-control multiselect select" multiple title='All' id="fee_type" name="fee_type">
              <?php foreach ($fee_blueprints as $key => $val) { ?>
                <option value="<?= $val->id ?>"><?php echo $val->name ?></option>
              <?php } ?>
            </select>
          </div>

          <div class="col-md-3 form-group">
            <p>Class</p>
            <?php
                $array = array();
                foreach ($classes as $key => $class) {
                  $array[$class->classId] = $class->className;
                }
            echo form_dropdown("class_name[]", $array, set_value("class_name"), "id='classId' multiple title='Select Classes' class='form-control classId select '");
            ?>
          </div>

          <div class="col-md-3 form-group">
                <p>Class/Section</p>
                <?php
                  $array = array();
                  foreach ($classSectionList as $key => $cl) {
                      $array[$cl->id] = $cl->class_name . $cl->section_name;
                  }
                  echo form_dropdown("classSectionId", $array, '', "id='classSectionId' multiple title='All' class='form-control select'");
                ?>
              </div>

          <div class="col-md-3 form-group">
            <p>Admission Type</p>
            <?php
              $array = array();
              $array[0] = 'Select Admission Type';
              foreach ($admission_type as $key => $admission) {
                  $array[$key] = ucfirst($admission);
              }
              echo form_dropdown("admission_type", $array, set_value("admission_type"), "id='admission_type' class='form-control select'");
            ?>
          </div>

          <div class="col-md-3 form-group">
            <p>Payment Options</p>
            <select name="payment_status" id='payment_status' class='form-control select'>
              <option value="">Payment Option</option>
              <option value="FULL">Full Payment</option>
              <option value="PARTIAL">Partial Payment</option>
              <option value="NOT_STARTED">Balance</option>
            </select>
          </div>

              <div class="col-md-3 form-group">
                <p>Joining Academic Year</p>
                <?php
                  $AcadArray = array();
                  $AcadArray[0] = 'Joining Academic Year';
                  foreach ($this->acad_year->getAllYearData() as $yearId => $year) {
                      $AcadArray[$year->id] = $year->acad_year;
                  }
                  echo form_dropdown("acad_year_id", $AcadArray, set_value("acad_year_id"), "id='acad_year_id' class='form-control select'");
                ?>
              </div>

              <div class="col-md-3 form-group">
                <p>Admission Status</p>
                <select id="admission_status" name="admission_status" class="form-control select">
                  <option value=""><?php echo "All" ?></option>
                  <?php foreach ($admission_status as $value => $type) { ?>
                    <option value="<?php echo $value ?>"><?php echo $type ?></option>
                  <?php } ?>
                </select>
              </div>

              <div class="col-md-3 form-group">
                <p>Select Staff Kids</p>
                <select class="form-control select" name="staff_kids" id="staff_kid">
                  <option value="all">All</option>
                  <option value="0">Exclude Staff Kids</option>
                  <option value="1">Staff Kids</option>
                </select>
              </div>

              <?php if(!empty($rteType)){ ?>
                <div class="col-md-3 form-group">
                  <p>IS RTE</p>
                  <?php
                    $rte_nrte = array();
                    $rte_nrte[-1] = 'All';
                    foreach ($rteType as $key => $rn) {
                        $rte_nrte[$key] = $rn;
                    }
                    echo form_dropdown("rte_nrte", $rte_nrte, set_value("rte_nrte"), "id='rte_nrteId' class='form-control select'");
                  ?>
                </div>
              <?php } ?>

              <?php if ($this->authorization->isAuthorized('FEESV2.STUDENT_WISE_PREDEFINED_FILTERS')) { ?>
                <div class="col-md-3 report-controls-wrapper">
                  <div class="form-group">
                    <label class="control-label" style="margin-top: 6px;">Saved Reports</label>
                    <div class="report-row">
                      <select name="" id="filter_types" class="form-control grow-select" onchange="selectFilters()">
                        <option value="">Select Report</option>
                      </select>

                      <?php if ($this->authorization->isAuthorized('FEESV2.STUDENT_WISE_PREDEFINED_FILTERS')) { ?>
                        <div class="dt-buttons">
                          <input type="button" name="reload" id="reload_filter" class="btn btn-info" value="Reload" onclick="selectFilters()">
                          <input type="button" name="save" id="save_filter" class="btn btn-info" value="Save">
                          <input type="button" name="update" id="update_filter" class="btn btn-info" value="Update">
                        </div>
                    </div>
                  </div>
                  <style>
                    .report-row {
                      display: flex;
                      align-items: center;
                      gap: 15px;
                    }

                    .grow-select {
                      flex: 1.3;
                      min-width: 200px;
                    }

                    .dt-buttons {
                      display: flex;
                      gap: 10px;
                      flex-shrink: 0;
                    }

                    /* Data table vertical scroll */
                    .table-responsive {
                      max-height: 600px;
                      overflow-y: auto;
                      overflow-x: auto;
                    }

                    .table-responsive table {
                      margin-bottom: 0;
                    }

                    /* Custom scrollbar styling */
                    .table-responsive::-webkit-scrollbar {
                      width: 8px;
                      height: 8px;
                    }

                    .table-responsive::-webkit-scrollbar-track {
                      background: #f1f1f1;
                      border-radius: 4px;
                    }

                    .table-responsive::-webkit-scrollbar-thumb {
                      background: #c1c1c1;
                      border-radius: 4px;
                    }

                    .table-responsive::-webkit-scrollbar-thumb:hover {
                      background: #a8a8a8;
                    }
                  </style>

                  <!-- Fix dropdown z-index layering issue -->
                  <style>
                    /* Fix dropdown z-index layering issue */
                    .form-group {
                        position: relative !important;
                        z-index: 1 !important;
                    }
                    
                    /* Dropdown form groups - higher z-index */
                    .form-group:has(.form-control.select) {
                        z-index: 10 !important;
                    }
                    
                    /* Specific dropdown z-index ordering */
                    .form-group:has(#fee_type) {
                        z-index: 15 !important;
                    }
                    
                    .form-group:has(#classId) {
                        z-index: 14 !important;
                    }
                    
                    .form-group:has(#classSectionId) {
                        z-index: 13 !important;
                    }
                    
                    .form-group:has(#admission_type) {
                        z-index: 12 !important;
                    }
                    
                    .form-group:has(#payment_status) {
                        z-index: 11 !important;
                    }
                    
                    .form-group:has(#acad_year_id) {
                        z-index: 10 !important;
                    }
                    
                    .form-group:has(#admission_status) {
                        z-index: 9 !important;
                    }
                    
                    .form-group:has(#staff_kid) {
                        z-index: 8 !important;
                    }
                    
                    .form-group:has(#rte_nrteId) {
                        z-index: 7 !important;
                    }
                    
                    /* Ensure select dropdowns have proper z-index */
                    .form-control.select {
                        position: relative !important;
                        z-index: inherit !important;
                    }
                    
                    /* Fix for Bootstrap select dropdowns */
                    .form-control.select:focus {
                        z-index: 999 !important;
                    }
                    
                    /* Ensure dropdown options appear above other elements */
                    .form-control.select option {
                        z-index: 999 !important;
                    }
                    
                    /* Additional fix for multiple select dropdowns */
                    #classSectionId {
                        z-index: inherit !important;
                    }
                    
                    #classSectionId:focus {
                        z-index: 999 !important;
                    }
                    
                    #classId {
                        z-index: inherit !important;
                    }
                    
                    #classId:focus {
                        z-index: 999 !important;
                    }
                    
                    #fee_type {
                        z-index: inherit !important;
                    }
                    
                    #fee_type:focus {
                        z-index: 999 !important;
                    }
                  </style>

                  <!-- Data Table Controls Styling -->
                  <style>
                    /* Panel controls layout */
                    .panel-controls {
                      display: flex;
                      justify-content: flex-end;
                      align-items: center;
                      gap: 15px;
                      margin-top: 10px;
                      margin-bottom: 15px;
                      padding: 10px 0;
                    }

                    .search-box {
                      margin-bottom: 7px !important;
                      
                    }

                    /* Export buttons container */
                    .export-buttons {
                      display: flex;
                      gap: 10px;
                      align-items: center;
                    }

                    /* Ensure proper spacing between controls and table */
                    .table-responsive {
                      margin-top: 10px;
                    }

                    /* Responsive adjustments */
                    @media (max-width: 768px) {
                      .panel-controls {
                        flex-direction: column;
                        align-items: stretch;
                        gap: 10px;
                      }
                      
                      .input-search {
                        width: 100%;
                      }
                      
                      .export-buttons {
                        justify-content: center;
                        flex-wrap: wrap;
                      }
                    }
                  </style>

                  <script>
                    $(document).ready(function() {
                      get_predefined_filters();

                      $('#save_filter').on('click', function() {
                        saveFilter();
                      });

                      $('#update_filter').on('click', function() {
                        updateFilter();
                      });

                      $('.select').on('keydown', function(e) {
                        if (e.key === 'Escape') {
                          $(this).closest('.dropdown').find('.dropdown-toggle').dropdown('hide');
                        }

                        if (e.key === 'Enter') {
                          $(this).closest('.dropdown').find('.dropdown-toggle').dropdown('hide');
                        }

                        if (e.key === ' ' && !$(this).is(':focus')) {
                          e.preventDefault();
                        }
                      });
                    });

                    $(document).on('click', function(event) {
                      var $target = $(event.target);
                      if (!$target.closest('.bootstrap-select').length && $('.bootstrap-select').hasClass('open')) {
                        $('.bootstrap-select').removeClass('open show');
                        $('.dropdown-menu').removeClass('show');
                      }
                    });


                    function get_predefined_filters() {
                      return $.ajax({
                        url: '<?php echo site_url('feesv2/reports/get_predefined_filters4'); ?>',
                        type: 'POST',
                        success: function(data) {
                          try {
                            var res_data = JSON.parse(data);

                            if (Array.isArray(res_data) && res_data.length > 0) {
                              var html = '<option value="">Select</option>';
                              res_data.forEach(filter => {
                                html += `<option value="${filter.id}">${filter.title}</option>`;
                              });

                              $('#filter_types').html(html);
                            } else {
                              console.warn("No predefined filters found.");
                              $('#filter_types').html('<option value="">No Filters Available</option>');
                            }
                          } catch (error) {
                            console.error("Error parsing response:", error);
                            $('#filter_types').html('<option value="">Error Loading Filters</option>');
                          }
                        },
                        error: function(xhr, status, error) {
                          console.error("AJAX Error:", error);
                          $('#filter_types').html('<option value="">Error Fetching Filters</option>');
                        }
                      });
                    }

                    function collectFilterData() {
                      // Get the current report type
                      const report_type = $('input[name="report_type"]:checked').val();

                      // Use the appropriate column visibility tracking based on report type
                      let columnVisibility;
                      if (report_type == 2) {
                        columnVisibility = window.columnVisibilitySummary || {};
                      } else {
                        columnVisibility = window.columnVisibilityDetailed || {};
                      }

                      // Convert the column visibility object to an array of visible column indices
                      let visibleCols = [];
                      for (let index in columnVisibility) {
                        if (columnVisibility[index] === true) {
                          visibleCols.push(parseInt(index));
                        }
                      }

                      // Always collect all filter values regardless of report type
                      return {
                        report_type: report_type,
                        fee_type: $('#fee_type').val(),
                        classId: $('#classId').val(),
                        admission_type: $('#admission_type').val(),
                        payment_status: $('#payment_status').val(),
                        classSectionId: $('#classSectionId').val(),
                        acad_year_id: $('#acad_year_id').val(),
                        admission_status: $('#admission_status').val(),
                        staff_kid: $('#staff_kid').val(),
                        rte_nrteId: $('#rte_nrteId').length > 0 ? $('#rte_nrteId').val() : '',
                        column_visibility: JSON.stringify(visibleCols)
                      };
                    }

                    function saveFilter() {
                      bootbox.prompt({
                        inputType: 'text',
                        placeholder: 'Enter the Title name',
                        title: "Save filters",
                        className: 'half-width-box',
                        buttons: {
                          confirm: {
                            label: 'Yes',
                            className: 'btn-success'
                          },
                          cancel: {
                            label: 'No',
                            className: 'btn-danger'
                          }
                        },
                        callback: function(remarks) {
                          if (remarks === null) return;

                          $('.bootbox .error-message').remove();
                          remarks = remarks.trim();

                          if (!remarks) {
                            new PNotify({
                              title: 'Missing Title',
                              text: 'Please enter a name to save the filter.',
                              type: 'error',
                              addclass: 'custom-pnotify half-width-notify',
                              cornerclass: '',
                              animate: {
                                animate: true,
                                in_class: 'fadeInRight',
                                out_class: 'fadeOutRight'
                              },
                              styling: 'bootstrap3',
                              delay: 3000
                            });
                            return false;
                          }

                          if (remarks.length < 5 || remarks.length > 50) {
                            setTimeout(() => {
                              $('.bootbox-input').after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">Title must be between 5 and 50 characters.</div>`);
                            }, 10);
                            return false;
                          }

                          let duplicate = false;
                          $('#filter_types option').each(function() {
                            if ($(this).text().trim().toLowerCase() === remarks.toLowerCase()) {
                              duplicate = true;
                              return false;
                            }
                          });

                          if (duplicate) {
                            setTimeout(() => {
                              $('.bootbox-input').after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">A filter with this name already exists.</div>`);
                            }, 10);
                            return false;
                          }

                          // Get all filter data using our collectFilterData function
                          let filterData = collectFilterData();

                          // Add the title to the filter data
                          filterData.title = remarks;

                          // Check if we have visible columns
                          if (!filterData.column_visibility || JSON.parse(filterData.column_visibility).length === 0) {
                            new PNotify({
                              title: 'Action Required',
                              text: 'Please click the "Get Report" button first before saving a filter.',
                              type: 'warning',
                              addclass: 'custom-pnotify half-width-notify',
                              delay: 3000
                            });
                            return false;
                          }

                          $.ajax({
                            url: '<?php echo site_url('feesv2/reports/save_filters4'); ?>',
                            type: 'POST',
                            data: filterData,
                            success: function(data) {
                              if (data) {
                                let lastId = 0;
                                $('#filter_types option').each(function() {
                                  const val = parseInt($(this).val());
                                  if (!isNaN(val) && val > lastId) lastId = val;
                                });

                                const newId = lastId + 1;

                                $('#filter_types').append(
                                  $('<option>', {
                                    value: newId,
                                    text: remarks
                                  })
                                );

                                $('#filter_types').val(newId).trigger('change');

                                new PNotify({
                                  title: 'Success',
                                  text: 'Filter Saved Successfully',
                                  type: 'success',
                                  addclass: 'custom-pnotify half-width-notify'
                                });
                              } else {
                                new PNotify({
                                  title: 'Error',
                                  text: 'Something went wrong',
                                  type: 'error',
                                  addclass: 'custom-pnotify half-width-notify'
                                });
                              }
                            }
                          });
                        }
                      });

                      setTimeout(() => {
                        $('.bootbox .modal-dialog').css({
                          'max-width': '400px',
                          'margin': '1.75rem auto'
                        });

                        $('.bootbox-input').on('input', function() {
                          const inputVal = $(this).val().trim();
                          if (inputVal.length > 50) {
                            $(this).val(inputVal.slice(0, 50)); // Truncate input to 50 characters
                            $('.bootbox .error-message').remove(); // Remove previous error message
                            $(this).after(`<div class="error-message" style="color: red; font-size: 0.9em; margin-top: 0.25rem;">Title must be between 5 and 50 characters.</div>`);
                          } else {
                            $('.bootbox .error-message').remove(); // Remove error message when valid length
                          }
                        });
                      }, 10);
                    }

                    function updateFilter() {
                      const selectedFilterId = $('#filter_types').val();

                      if (!selectedFilterId) {
                        bootbox.alert({
                          title: "No Filter Selected",
                          message: "Please select a filter to update.",
                          className: "half-width-box",
                          buttons: {
                            ok: {
                              label: 'OK',
                              className: 'btn-primary'
                            }
                          }
                        });

                        // Center the alert modal
                        setTimeout(() => {
                          $('.bootbox .modal-dialog').css({
                            'max-width': '400px',
                            'margin': '1.75rem auto'
                          });
                        }, 10);

                        return; // stop the update function
                      }

                      let filterData = collectFilterData();
                      filterData.filter_types_id = selectedFilterId;
                      filterData.title = $('#filter_types option:selected').text().trim();
                      filterData.stakeholder_id = $('#stakeholder_id').val();

                      bootbox.confirm({
                        title: "Update Filters",
                        message: 'Are you sure you want to update the filter?',
                        className: "half-width-box",
                        buttons: {
                          confirm: {
                            label: 'Yes',
                            className: 'btn-success'
                          },
                          cancel: {
                            label: 'No',
                            className: 'btn-danger'
                          }
                        },
                        callback: function(result) {
                          if (result) {
                            $.ajax({
                              url: '<?php echo site_url('feesv2/reports/update_filters4'); ?>',
                              type: 'POST',
                              data: filterData,
                              complete: function() {
                                $.when(get_predefined_filters()).done(function() {
                                  if (
                                    $('#filter_types option[value="' + filterData.filter_types_id + '"]').length === 0
                                  ) {
                                    $('#filter_types').append(
                                      $('<option>', {
                                        value: filterData.filter_types_id,
                                        text: filterData.title
                                      })
                                    );
                                  }

                                  $('#filter_types').val(filterData.filter_types_id);
                                  selectFilters();

                                  new PNotify({
                                    title: 'Success',
                                    text: 'Filter updated successfully.',
                                    type: 'success',
                                    addclass: 'custom-pnotify half-width-notify'
                                  });
                                });
                              }
                            });
                          }
                        }
                      });

                      setTimeout(() => {
                        $('.bootbox .modal-dialog').css({
                          'max-width': '400px',
                          'margin': '1.75rem auto'
                        });
                      }, 10);
                    }

                    function selectFilters() {
                      const filterId = $('#filter_types').val();

                      if (!filterId || filterId.trim() === '') {
                        $('#reload_filter').prop('disabled', true);
                        return;
                      } else {
                        $('#reload_filter').prop('disabled', false);
                      }

                      // Set a flag to indicate we're loading a filter
                      // This prevents double-calling get_report_summary()
                      window.isLoadingFilter = true;

                      $('#reload_filter').show();

                      // Show loading indicator in the button
                      const originalReloadText = $('#reload_filter').val();
                      $('#reload_filter').val('Loading...').prop('disabled', true);

                      // Cache the current report type before changing anything
                      const initialReportType = $('input[name="report_type"]:checked').val();

                      $.ajax({
                        url: '<?php echo site_url('feesv2/reports/get_predefined_filters_by_id4'); ?>',
                        type: 'POST',
                        data: {
                          filter_id: filterId
                        },
                        dataType: 'json',
                        success: function(response) {
                          if (response && response.success && response.filters_selected) {
                            const filters = response.filters_selected;

                            // Reset column visibility tracking to ensure clean state
                            window.columnVisibilityDetailed = null;
                            window.columnVisibilitySummary = null;

                            const updateSelect = (selector, values) => {
                              if (values !== undefined && values !== null) {
                                if (typeof values === 'string') values = values.split(',');
                                $(selector).val(values);
                                if ($(selector).hasClass('selectpicker') || $(selector).hasClass('select')) {
                                  try {
                                    $(selector).selectpicker('refresh');
                                  } catch (e) {
                                    console.warn('Error refreshing selectpicker:', e);
                                  }
                                }
                              }
                            };

                            // Set report type without triggering the onchange event
                            if (filters.report_type) {
                              // First update the radio button state without triggering change event
                              $(`input[name="report_type"][value="${filters.report_type}"]`).prop('checked', true);
                              // Then manually call toggleFilters() to update the UI
                              toggleFilters();
                              $('.additional-filters').show();
                            }

                            updateSelect('#fee_type', filters.fee_type);
                            updateSelect('#classId', filters.classId);
                            updateSelect('#admission_type', filters.admission_type);
                            updateSelect('#payment_status', filters.payment_status);
                            updateSelect('#admission_status', filters.admission_status);
                            updateSelect('#staff_kid', filters.staff_kid);

                            if ($('#rte_nrteId').length > 0) {
                              updateSelect('#rte_nrteId', filters.rte_nrteId);
                            }

                            const classSectionFlow = new Promise((resolve) => {
                              $.ajax({
                                url: '<?php echo site_url('feesv2/reports_v2/get_class_section_by_fees_selection_class') ?>',
                                type: 'POST',
                                data: {
                                  feeclass: filters.classId
                                },
                                success: function(data) {
                                  try {
                                    const resdata = JSON.parse(data);
                                    let option = '';
                                    resdata.forEach(item => {
                                      option += `<option value="${item.section_id}">${item.class_section}</option>`;
                                    });
                                    $('#classSectionId').html(option);

                                    try {
                                      $('#classSectionId').selectpicker('refresh');
                                      $('#classSectionId').val(filters.classSectionId).selectpicker('refresh');
                                    } catch (e) {
                                      console.warn('Error refreshing classSectionId selectpicker:', e);
                                      $('#classSectionId').val(filters.classSectionId);
                                    }

                                    resolve();
                                  } catch (e) {
                                    console.error('Error processing class section data:', e);
                                    resolve();
                                  }
                                },
                                error: function() {
                                  console.error('Failed to fetch class sections');
                                  resolve();
                                }
                              });
                            });

                            classSectionFlow.then(() => {
                              $('#search').prop('disabled', true).val('Please wait...');
                              $('#reload_filter').val(originalReloadText).prop('disabled', false);

                              const originalButtonText = 'Get Report';

                              get_report_summary();

                              const checkReportLoaded = setInterval(() => {
                                if (!$(".loading-icon").is(":visible") && $("#exportButtons").is(":visible")) {
                                  $('#search').prop('disabled', false).val(originalButtonText);
                                  clearInterval(checkReportLoaded);

                                  if (filters.column_visibility) {
                                    if (filters.report_type == 1) {
                                      const table = $('#fee_student_detailed_data');
                                      if (table.length && window.showTableSkeletonLoading) {
                                        window.showTableSkeletonLoading(table);

                                        setTimeout(() => {
                                          applySavedColumnVisibility(filters.column_visibility);
                                        }, 100);
                                      } else {
                                        applySavedColumnVisibility(filters.column_visibility);
                                      }
                                    } else {
                                      applySavedColumnVisibility(filters.column_visibility);
                                    }

                                    setTimeout(function() {
                                      $('#column-toggle-btn').off('click');
                                      $(document).off('click', '#column-toggle-btn');

                                      $(document).on('click', '#column-toggle-btn', function(e) {
                                        e.preventDefault();
                                        e.stopPropagation();

                                        const existingMenu = $('.column-menu');
                                        if (existingMenu.is(':visible')) {
                                          existingMenu.hide();
                                        } else {
                                          initializeColumnToggle();

                                          $('.column-menu').show();

                                          const buttonPos = $(this).offset();
                                          $('.column-menu').css({
                                            position: 'absolute',
                                            top: buttonPos.top + $(this).outerHeight(),
                                            left: buttonPos.left,
                                            maxHeight: '300px',
                                            overflowY: 'auto',
                                            zIndex: 1000
                                          });
                                        }
                                      });
                                    }, 500);
                                  }

                                  window.isLoadingFilter = false;
                                }
                              }, 500);
                            });
                          } else {
                            $('#reload_filter').val(originalReloadText).prop('disabled', false);
                            window.isLoadingFilter = false;

                            new PNotify({
                              title: 'Error',
                              text: 'Failed to fetch filter details. Please try again.',
                              type: 'error',
                              addclass: 'custom-pnotify half-width-notify'
                            });
                          }
                        },
                        error: function(xhr, status, error) {
                          $('#reload_filter').val(originalReloadText).prop('disabled', false);
                          window.isLoadingFilter = false;
                        }
                      });
                    }

                    function applySavedColumnVisibility(column_visibility) {
                      let visibleCols;

                      try {
                        visibleCols = JSON.parse(column_visibility || '[]');
                      } catch (err) {
                        console.error('Failed to parse column_visibility JSON:', err);
                        return;
                      }

                      const report_type = $('input[name="report_type"]:checked').val();

                      if (report_type == 1) {
                        const table = $('#fee_student_detailed_data');
                        if (table.length) {
                          if (window.showTableSkeletonLoading) {
                            window.showTableSkeletonLoading(table);
                          }
                        }
                      }

                      $('.column-menu').remove();

                      if (report_type == 2) {
                        window.columnVisibilitySummary = {};
                        const summaryTable = $('#fee_student_summary_data');
                        if (summaryTable.length) {
                          summaryTable.find('thead th').each(function(index) {
                            window.columnVisibilitySummary[index] = true;
                          });

                          summaryTable.find('thead th').each(function(index) {
                            window.columnVisibilitySummary[index] = visibleCols.includes(parseInt(index));
                          });
                        }
                      } else {
                        window.columnVisibilityDetailed = {};
                        const detailedTable = $('#fee_student_detailed_data');
                        if (detailedTable.length) {
                          detailedTable.find('thead th').each(function(index) {
                            window.columnVisibilityDetailed[index] = true;
                          });

                          detailedTable.find('thead th').each(function(index) {
                            window.columnVisibilityDetailed[index] = visibleCols.includes(parseInt(index));
                          });
                        }
                      }

                      const applyVisibilityToTable = () => {
                        const tableSelector = report_type == 2 ? '.summary-view table' : '.detailed-view table';
                        const columnVisibility = report_type == 2 ? window.columnVisibilitySummary : window.columnVisibilityDetailed;
                        const table = $(tableSelector);

                        if (!table.length) {
                          console.warn('Table not found:', tableSelector);
                          return;
                        }

                        const batchSize = 5;
                        const totalColumns = Object.keys(columnVisibility).length;
                        let processedColumns = 0;

                        function processColumnBatch() {
                          const batch = Object.keys(columnVisibility)
                            .slice(processedColumns, processedColumns + batchSize)
                            .map(Number);

                          if (batch.length === 0) {
                            return;
                          }

                          batch.forEach(colIndex => {
                            const isVisible = columnVisibility[colIndex];

                            if (report_type == 2) {
                              // Summary view
                              const cells = table.find(`tr > *:nth-child(${colIndex + 1})`);
                              cells.css('display', isVisible ? '' : 'none');

                              $('.column-menu input[data-column="' + colIndex + '"]').prop('checked', isVisible);
                            } else {
                              // Detailed view
                              if (window.toggleColumnVisibility) {
                                window.toggleColumnVisibility(colIndex, isVisible);
                              } else {
                                const headerCell = table.find('thead tr th').eq(colIndex);
                                headerCell.css('display', isVisible ? '' : 'none');

                                if (colIndex <= 3) {
                                  table.find(`tbody tr td[rowspan]:nth-child(${colIndex + 1})`).css('display', isVisible ? '' : 'none');
                                } else {
                                  table.find(`tbody tr`).each(function() {
                                    const row = $(this);
                                    if (row.find('td[rowspan]').length > 0) {
                                      row.find(`td:eq(${colIndex})`).css('display', isVisible ? '' : 'none');
                                    } else if (row.find('th:contains("Total")').length === 0) {
                                      const adjustedIndex = colIndex - 4;
                                      row.find(`td:eq(${adjustedIndex})`).css('display', isVisible ? '' : 'none');
                                    }
                                  });
                                }
                              }

                              $('.column-menu input[data-column="' + colIndex + '"]').prop('checked', isVisible);
                            }
                          });

                          processedColumns += batch.length;

                          setTimeout(processColumnBatch, 0);
                        }

                        processColumnBatch();
                      };
                      setTimeout(() => {
                        applyVisibilityToTable();

                        if (report_type == 1) {
                          setTimeout(() => {
                            $('#column-toggle-skeleton').fadeOut(200, function() {
                              $(this).remove();
                              $('#fee_student_detailed_data').css('visibility', 'visible');
                            });
                          }, 500);
                        }
                      }, 100);
                    }


                  </script>

                <?php } ?>
                </div>
              <?php } ?>
        </div>

        <div class="row" style="margin: 0px; margin-top: 20px;">
          <div class="form-group col-md-6">
            <label class="control-label mr-3" style="font-size: 18px; font-weight: 600;">Report </label>
            <label class="radio-inline" for="type-1" style="font-size: 18px; margin-right: 20px; display: inline-flex; align-items: center;">
              <input type="radio" name="report_type" id="type-1" value="1" checked="" onchange="handleReportTypeChange()" style="transform: scale(1.5); margin-right: 8px; vertical-align: middle;">Detailed
            </label>
            <label class="radio-inline" for="type-2" style="font-size: 18px; display: inline-flex; align-items: center;">
              <input type="radio" name="report_type" id="type-2" value="2" onchange="handleReportTypeChange()" style="transform: scale(1.5); margin-right: 8px; vertical-align: middle;">Summary
            </label>
          </div>
          <div class="col-md-6 text-right">
            <input type="button" onclick="get_report_summary()" name="search" id="search" class="btn btn-primary" value="Get Report" style="margin-top: 5px;">
          </div>
        </div>
      </div>
    </div>
      <div class="card-body">
      <div class="col-md-12" style="margin-top: 2rem;">
         <div class="text-center"><div style="display: none;" class="progress" id="progress"><div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%"></div></div></div>

          <div id="printArea">
            <div id="print_visible" style="display: none;" class="text-center">
              <h3><?php echo $this->settings->getSetting('school_name') ?></h3>
              <h4>Fees Summary Detail Report</h4>
            </div>

            <div class="col-12 text-center loading-icon" style="display: none;">
              <i class="fa fa-spinner fa-spin" style="font-size: 40px;"></i>
            </div>
            <div class="fee_summary">

            </div>

            <div class="panel-controls" id="exportButtons" style="display: none;">
              <div class="search-box">
                <i class="bi bi-search"></i>
                <input type="text" class="input-search" id="table-search" placeholder="Enter Search...">
              </div>
              <div class="export-buttons">
                <button class="btn btn-outline-primary export_btn" id="column-toggle-btn" >
                    <?= $this->load->view('svg_icons/column.svg', [], true); ?> Columns
                </button>

                <button class="btn btn-outline-primary" id="expbtns"  onclick="printProfile()">
                    <?= $this->load->view('svg_icons/print.svg', [], true); ?> Print
                </button>

                <button class="btn btn-outline-primary" id="expbtns" onclick="exportToExcel_daily()">
                    <?= $this->load->view('svg_icons/excel_2.svg', [], true); ?> Excel
                </button>
              </div>
            </div>

            <div class="table-responsive detailed-view">
            </div>
            <div class="table-responsive summary-view" style="display: none;">
            </div>
          </div>

      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
  function toggleFilters() {
    var report_type = $('input[name="report_type"]:checked').val();

    $('.detailed-view').html('');
    $('.summary-view').html('');

    if (report_type == 1) {
      $('.detailed-view').show();
      $('.summary-view').hide();
    } else {
      $('.detailed-view').hide();
      $('.summary-view').show();
    }

    $('.additional-filters').show();
  }

  // function syncFilterValues() {
  //   console.log("Filter changed: " + $(this).attr('id'));
  // }

  function handleReportTypeChange() {
    toggleFilters();

    if (!window.isLoadingFilter) {
      get_report_summary();
    }
  }

  $(document).ready(function() {
    window.isLoadingFilter = false;

    var initial_report_type = $('input[name="report_type"]:checked').val();
    if (initial_report_type == 2) {
      $('.detailed-view').hide();
      $('.summary-view').show();
    } else {
      $('.detailed-view').show();
      $('.summary-view').hide();
    }

    $('.additional-filters').show();

    toggleFilters();

    // $('#fee_type').on('change', syncFilterValues);
    // $('#classId').on('change', syncFilterValues);
    // $('#admission_type').on('change', syncFilterValues);
    // $('#payment_status').on('change', syncFilterValues);
    // if ($('#rte_nrteId').length > 0) {
    //   $('#rte_nrteId').on('change', syncFilterValues);
    // }

    $(document).on('click', function(e) {
      if (!$(e.target).closest('.column-menu, #column-toggle-btn').length) {
        $('.column-menu').hide();
      }
    });

    // Search functionality
    $('#table-search').on('keyup', function() {
      const searchText = $(this).val().toLowerCase();

      // Search in the details table
      $('#fee_student_summary_data tbody tr').each(function() {
        let rowVisible = false;

        // Search in all cells of the row
        $(this).find('td').each(function() {
          if ($(this).text().toLowerCase().indexOf(searchText) > -1) {
            rowVisible = true;
            return false; // Break the loop if found
          }
        });

        $(this).toggle(rowVisible);
      });
    });
  });

  var loan_provider_charges = '<?php echo $loan_column ?>';
  var fee_adjustment_amount = '<?php echo $fee_adjustment_amount ?>';
  var fee_fine_amount = '<?php echo $this->settings->getSetting('fee_fine_amount') ?>';
  var is_semester_scheme = '<?php echo $this->settings->getSetting('is_semester_scheme') ?>';
  var refund_amount = '<?php echo $this->settings->getSetting('fee_refund_amount_display') ?>';
  var cohortStudentIds = [];
  var completed = 0;
  var total_students = 0;
  var totalFeeAssingedStudentCount = 0;
  var aluminiCount = 0;
  var summaryData = [];

  function get_report_summary() {
    $('#table-search').val('');

    $('#search').prop('disabled', true).val('Please wait...');

    var report_type = $('input[name="report_type"]:checked').val();

    toggleFilters();

    if (report_type == 2) {
      var filters = [];

      var fee_type_values = $('#fee_type').val();
      if (fee_type_values && fee_type_values.length > 0) {
        var fee_type_text = [];
        $('#fee_type option:selected').each(function() {
          fee_type_text.push($(this).text());
        });
        filters.push("Fee Type: " + fee_type_text.join(", "));
      }

      var class_values = $('#classId').val();
      if (class_values && class_values.length > 0) {
        var class_text = [];
        $('#classId option:selected').each(function() {
          class_text.push($(this).text());
        });
        filters.push("Class: " + class_text.join(", "));
      }

      var admission_type = $('#admission_type').val();
      if (admission_type && admission_type != "0") {
        filters.push("Admission Type: " + $('#admission_type option:selected').text());
      }

      var payment_status = $('#payment_status').val();
      if (payment_status) {
        filters.push("Payment Option: " + $('#payment_status option:selected').text());
      }

      // Remove the summary filter info box
      // var filter_html = '';
      // if (filters.length > 0) {
      //   filter_html = '<div class="alert alert-info"><strong>Current Filters:</strong> ' + filters.join(" | ") + '</div>';
      // }
      // $('.fee_summary').html(filter_html);

      $(".loading-icon").show();
      $("#exportButtons").hide();

    }

    $('.detailed-view').html('');
    $('.summary-view').html('');
    $('.fee_summary').html('');
    $(".loading-icon").show();
    $("#exportButtons").hide();

    if (report_type == 1) {
      constructFeeHeader();
    }

    window.columnVisibilityDetailed = null;
    window.columnVisibilitySummary = null;

    var fee_type, clsId, admission_type, payment_status, rte_nrte;
    var all_filters = [];

    fee_type = $('#fee_type').val();
    clsId = $('#classId').val();
    admission_type = $('#admission_type').val();
    payment_status = $('#payment_status').val();
    var classSectionId = $('#classSectionId').val();
    var acad_year_id = $('#acad_year_id').val();
    var admission_status = $('#admission_status').val();
    var staff_kid = $('#staff_kid').val();
    if ($('#rte_nrteId').length > 0) {
      var rte_nrte = $('#rte_nrteId').val();
    }

    if (fee_type && fee_type.length > 0) {
      var fee_type_text = [];
      $('#fee_type option:selected').each(function() {
        fee_type_text.push($(this).text());
      });
      all_filters.push("Fee Type: " + fee_type_text.join(", "));
    }

    if (clsId && clsId.length > 0) {
      var class_text = [];
      $('#classId option:selected').each(function() {
        class_text.push($(this).text());
      });
      all_filters.push("Class: " + class_text.join(", "));
    }

    if (classSectionId && classSectionId.length > 0) {
      var classSection_text = [];
      $('#classSectionId option:selected').each(function() {
        classSection_text.push($(this).text());
      });
      all_filters.push("Class/Section: " + classSection_text.join(", "));
    }

    if (admission_type && admission_type != "0") {
      all_filters.push("Admission Type: " + $('#admission_type option:selected').text());
    }

    if (payment_status) {
      all_filters.push("Payment Option: " + $('#payment_status option:selected').text());
    }

    if ($('#rte_nrteId').length > 0) {
      if (rte_nrte && rte_nrte != "0") {
        all_filters.push("RTE: " + $('#rte_nrteId option:selected').text());
      }
    }

    if (acad_year_id && acad_year_id != "0") {
      all_filters.push("Academic Year: " + $('#acad_year_id option:selected').text());
    }

    if (admission_status) {
      all_filters.push("Admission Status: " + $('#admission_status option:selected').text());
    }

    if (staff_kid && staff_kid != "all") {
      all_filters.push("Staff Kids: " + $('#staff_kid option:selected').text());
    }

    // Remove the detailed filter info box
    // if (all_filters.length > 0) {
    //   var filter_html = '<div class="alert alert-info"><strong>Current Filters:</strong> ' + all_filters.join(" | ") + '</div>';
    //   $('.fee_summary').append(filter_html);
    // }
    completed = 0;
    total_students = 0;
    totalFeeAssingedStudentCount = 0;
    aluminiCount = 0;
    summaryData = [];
    displayedRowCount = 0; // Reset the global counter for displayed rows

    var requestData = {
      'clsId': clsId,
      'fee_type': fee_type,
      'admission_type': admission_type,
      'payment_status': payment_status,
      'classSectionId': classSectionId,
      'acad_year_id': acad_year_id,
      'admission_status': admission_status,
      'staff_kid': staff_kid,
    };


    if ($('#rte_nrteId').length > 0) {
      var rte_nrte = $('#rte_nrteId').val();
      if (rte_nrte && rte_nrte != "0") {
        requestData.rte_nrteId = rte_nrte;
      }
    }

    if (classSectionId && classSectionId.length > 0) {
      requestData.classSectionId = classSectionId;
    }

    if (acad_year_id && acad_year_id != "0") {
      requestData.acad_year_id = acad_year_id;
    }

    if (admission_status) {
      requestData.admission_status = admission_status;
    }

    if (staff_kid && staff_kid != "all") {
      requestData.staff_kid = staff_kid;
    }

    if ($('#rte_nrteId').length > 0) {
      var rte_nrte = $('#rte_nrteId').val();
      if (rte_nrte && rte_nrte != "0") {
        requestData.rte_nrteId = rte_nrte;
      }
    }


    $.ajax({
      url: '<?php echo site_url('feesv2/reports_v2/get_student_summary_details_v2'); ?>',
      data: requestData,
      type: "post",
      success: function (data) {
        // console.log(data);
         var cohort_student_ids = JSON.parse(data);
          if (cohort_student_ids.length == 0) {
            $('#skeleton-loading-container').remove();

            $('.table-responsive').html('<h3>No data available for the current filters. Please adjust your filter criteria and try again.</h3>');
            $(".loading-icon").hide();
            $('#search').prop('disabled', false).val('Get Report');
            $("#progress").hide();
            $("#exportButtons").hide();
            return;
          }

          cohortStudentIds = cohort_student_ids;
          total_students = parseInt(150* (cohortStudentIds.length - 2)) + parseInt(cohortStudentIds[cohortStudentIds.length - 1].length);
          var progress = document.getElementById('progress-ind');
          progress.style.width = (completed/total_students)*100+'%';
          $("#progress").show();
          callReportGetter(0);

        // $('#feeGenerationReport').html(data);
      },
      error: function (err) {
        $('#skeleton-loading-container').remove();

        $('#search').prop('disabled', false).val('Get Report');
        $("#progress").hide();
        $(".loading-icon").hide();

        alert('Network may be slow');
        console.log(err);
      }
    });
  }

  function callReportGetter(index){
    if(index < cohortStudentIds.length) {
      getReport(index);
    } else {
      $("#progress").hide();
      $(".loading-icon").hide();
      $("#exportButtons").show();

      $('#search').prop('disabled', false).val('Get Report');

      initializeSearch();

      initializeColumnVisibility();
      initializeColumnToggle();

      var gtotal_fee_amount = 0;
      var gtotal_fee_collected = 0;
      var gtotal_fee_concession = 0;
      var gtotal_fee_adjustment = 0;
      var gtotal_fee_discount = 0;
      var gtotal_fee_balance = 0;
      var gtotal_fee_fine = 0;
      var gloan_fee_loan = 0;
      var gloan_fee_refund = 0;

      for(var bpName in summaryData) {
        gtotal_fee_amount += summaryData[bpName]['summary_total_fee_amount'];
        gtotal_fee_collected += summaryData[bpName]['summary_total_fee_collected'];
        gtotal_fee_concession += summaryData[bpName]['summary_total_fee_concession'];
        gtotal_fee_adjustment += summaryData[bpName]['summary_total_adjustment'];
        gtotal_fee_discount += summaryData[bpName]['summary_total_discount'];
        gtotal_fee_balance += summaryData[bpName]['summary_total_balance'];
        gtotal_fee_fine += summaryData[bpName]['summary_total_fine'];
        gloan_fee_loan += summaryData[bpName]['summary_total_loan'];
        gloan_fee_refund += summaryData[bpName]['summary_total_refund'];
      }

      var report_type = $('input[name="report_type"]:checked').val();
      var summary_output = '';

      if (report_type == 2) {
        // SUMMARY MODE
        summary_output = '<table class="table table-bordered">';
        summary_output += '<thead>';
        summary_output += '<tr>';
        summary_output += '<th>#</th>';
        summary_output += '<th>Fee Type</th>';
        summary_output += '<th>Total Fee Amount</th>';
        summary_output += '<th>Total Paid Amount</th>';
        summary_output += '<th>Total Concession</th>';
        if (fee_adjustment_amount) {
          summary_output += '<th>Total Adjustment</th>';
        }
        summary_output += '<th>Total Discount</th>';
        summary_output += '<th>Total Balance</th>';
        if (fee_fine_amount) {
          summary_output += '<th>Total Fine</th>';
        }
        summary_output += '<th>Percentage (%)</th>';
        summary_output += '<th>Overdue Balance</th>';
        if (loan_provider_charges) {
          summary_output += '<th>Total Loan Charges</th>';
        }
        if (refund_amount) {
          summary_output += '<th>Total Refund</th>';
        }
        summary_output += '</tr>';
        summary_output += '</thead>';
        summary_output +='<tbody>';

        var sl = 0;
        for(var bpName in summaryData) {
          summary_output += '<tr>';
          summary_output +='<th>'+(sl+1)+'</th>';
          summary_output +='<th style="font-size:14px; color:#7ea3d2"><b>'+bpName+'</b></th>';
          summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_fee_amount'])+'</td>';
          summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_fee_collected'])+'</td>';
          summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_fee_concession'])+'</td>';
          if (fee_adjustment_amount) {
            summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_adjustment'])+'</td>';
          }
          summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_discount'])+'</td>';
          summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_balance'])+'</td>';

          if (fee_fine_amount) {
            summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_fine'])+'</td>';
          }

          // percentage
          var feeAmount = parseFloat(summaryData[bpName]['summary_total_fee_amount']);
          var balance = parseFloat(summaryData[bpName]['summary_total_balance']);
          var percentage = 0;
          if (feeAmount > 0) {
            percentage = (balance / feeAmount) * 100;
          }
          summary_output +='<td>'+percentage.toFixed(2) + "%"+'</td>';

          // overdue balance
          summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_due_amount'])+'</td>';

          if (loan_provider_charges) {
            summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_loan'])+'</td>';
          }
          if (refund_amount) {
            summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_refund'])+'</td>';
          }
          summary_output += '</tr>';
          sl++;
        }
        summary_output +='</tbody>';
        summary_output +='<tfoot>';
        summary_output +='<tr>';
        summary_output +='<th colspan="2" style="font-size:14px;">Grand Total</th>';
        summary_output +='<th>'+in_currency(gtotal_fee_amount)+'</th>';
        summary_output +='<th>'+in_currency(gtotal_fee_collected)+'</th>';
        summary_output +='<th> ( '+in_currency(gtotal_fee_concession)+' ) </th>';
        if (fee_adjustment_amount) {
          summary_output +='<th> ( '+in_currency(gtotal_fee_adjustment)+' ) </th>';
        }
        summary_output +='<th> ( '+in_currency(gtotal_fee_discount)+' ) </th>';
        summary_output +='<th> ( '+in_currency(gtotal_fee_balance)+' ) </th>';

        if (fee_fine_amount) {
          summary_output +='<td> '+in_currency(gtotal_fee_fine)+' </td>';
        }

        // Calculate grand total percentage
        var gtotal_percentage = 0;
        if (gtotal_fee_amount > 0) {
          gtotal_percentage = (gtotal_fee_balance / gtotal_fee_amount) * 100;
        }
        summary_output +='<th>'+gtotal_percentage.toFixed(2) + "%"+'</th>';

        // Add grand total overdue balance
        var gtotal_due_amount = 0;
        for(var bpName in summaryData) {
          gtotal_due_amount += summaryData[bpName]['summary_total_due_amount'];
        }
        summary_output +='<th> ( '+in_currency(gtotal_due_amount)+' ) </th>';

        if (loan_provider_charges) {
          summary_output +='<th>'+in_currency(gloan_fee_loan)+'</th>';
        }
        if (refund_amount) {
          summary_output +='<th>'+in_currency(gloan_fee_refund)+'</th>';
        }
        summary_output +='</tr>';
        summary_output +='</tfoot>';
        summary_output +='</table>';
      } else {
        // DETAILED MODE - Show the complete summary table
        summary_output = '<table class="table table-bordered">';
        summary_output += '<thead>';
        summary_output += '<tr>';
        summary_output += '<th>#</th>';
        summary_output += '<th>Fee Type</th>';
        summary_output += '<th>Total Fee Amount</th>';
        summary_output += '<th>Total Paid Amount</th>';
        summary_output += '<th>Total Concession</th>';
        if (fee_adjustment_amount) {
          summary_output += '<th>Total Adjustment</th>';
        }
        summary_output += '<th>Total Discount</th>';
        summary_output += '<th>Total Balance</th>';
        if (fee_fine_amount) {
          summary_output += '<th>Total Fine</th>';
        }
        summary_output += '<th>Percentage (%)</th>';
        summary_output += '<th>Overdue Balance</th>';
        if (loan_provider_charges) {
          summary_output += '<th>Total Loan Charges</th>';
        }
        if (refund_amount) {
          summary_output += '<th>Total Refund</th>';
        }
        summary_output += '</tr>';
        summary_output += '</thead>';
        summary_output +='<tbody>';

        var sl = 0;
        for(var bpName in summaryData) {
          summary_output += '<tr>';
          summary_output +='<th>'+(sl+1)+'</th>';
          summary_output +='<th style="font-size:14px; color:#7ea3d2"><b>'+bpName+'</b></th>';
          summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_fee_amount'])+'</td>';
          summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_fee_collected'])+'</td>';
          summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_fee_concession'])+'</td>';
          if (fee_adjustment_amount) {
            summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_adjustment'])+'</td>';
          }
          summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_discount'])+'</td>';
          summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_balance'])+'</td>';

          if (fee_fine_amount) {
            summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_fine'])+'</td>';
          }

          // Calculate percentage
          var feeAmount = parseFloat(summaryData[bpName]['summary_total_fee_amount']);
          var balance = parseFloat(summaryData[bpName]['summary_total_balance']);
          var percentage = 0;
          if (feeAmount > 0) {
            percentage = (balance / feeAmount) * 100;
          }
          summary_output +='<td>'+percentage.toFixed(2) + "%"+'</td>';

          // Add overdue balance
          summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_due_amount'])+'</td>';

          if (loan_provider_charges) {
            summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_loan'])+'</td>';
          }
          if (refund_amount) {
            summary_output +='<td>'+in_currency(summaryData[bpName]['summary_total_refund'])+'</td>';
          }
          summary_output += '</tr>';
          sl++;
        }
        summary_output +='</tbody>';
        summary_output +='<tfoot>';
        summary_output +='<tr>';
        summary_output +='<th colspan="2" style="font-size:14px;">Grand Total</th>';
        summary_output +='<th>'+in_currency(gtotal_fee_amount)+'</th>';
        summary_output +='<th>'+in_currency(gtotal_fee_collected)+'</th>';
        summary_output +='<th> ( '+in_currency(gtotal_fee_concession)+' ) </th>';
        if (fee_adjustment_amount) {
          summary_output +='<th> ( '+in_currency(gtotal_fee_adjustment)+' ) </th>';
        }
        summary_output +='<th> ( '+in_currency(gtotal_fee_discount)+' ) </th>';
        summary_output +='<th> ( '+in_currency(gtotal_fee_balance)+' ) </th>';

        if (fee_fine_amount) {
          summary_output +='<td> '+in_currency(gtotal_fee_fine)+' </td>';
        }

        // Calculate grand total percentage
        var gtotal_percentage = 0;
        if (gtotal_fee_amount > 0) {
          gtotal_percentage = (gtotal_fee_balance / gtotal_fee_amount) * 100;
        }
        summary_output +='<th>'+gtotal_percentage.toFixed(2) + "%"+'</th>';

        // Add grand total overdue balance
        var gtotal_due_amount = 0;
        for(var bpName in summaryData) {
          gtotal_due_amount += summaryData[bpName]['summary_total_due_amount'];
        }
        summary_output +='<th> ( '+in_currency(gtotal_due_amount)+' ) </th>';

        if (loan_provider_charges) {
          summary_output +='<th>'+in_currency(gloan_fee_loan)+'</th>';
        }
        if (refund_amount) {
          summary_output +='<th>'+in_currency(gloan_fee_refund)+'</th>';
        }
        summary_output +='</tr>';
        summary_output +='</tfoot>';
        summary_output +='</table>';
      }

      // Store the grand total balance in a hidden field for export/print
      summary_output += '<input type="hidden" id="grand_total_balance" value="' + gtotal_fee_balance + '">';

      $(".fee_summary").html(summary_output);
    }
  }

  function getReport(index){
    var cohortstudentids = cohortStudentIds[index];
    var report_type = $('input[name="report_type"]:checked').val();

    var fee_type = $('#fee_type').val();
    var payment_status = $('#payment_status').val();

    var requestData = {
      cohortstudentids: cohortstudentids,
      'fee_type': fee_type,
      'payment_status': payment_status,
      'report_type': report_type
    };

    var classSectionId = $('#classSectionId').val();
    if (classSectionId && classSectionId.length > 0) {
      requestData.classSectionId = classSectionId;
    }

    var acad_year_id = $('#acad_year_id').val();
    if (acad_year_id && acad_year_id != "0") {
      requestData.acad_year_id = acad_year_id;
    }

    var admission_status = $('#admission_status').val();
    if (admission_status) {
      requestData.admission_status = admission_status;
    }

    var staff_kid = $('#staff_kid').val();
    if (staff_kid && staff_kid != "all") {
      requestData.staff_kid = staff_kid;
    }

    if ($('#rte_nrteId').length > 0) {
      var rte_nrte = $('#rte_nrteId').val();
      if (rte_nrte && rte_nrte != "0") {
        requestData.rte_nrteId = rte_nrte;
      }
    }

    url = '<?php echo site_url('feesv2/reports_v2/get_student_summary_fee_data_v2'); ?>';
    $.ajax({
      url:url,
      type: 'post',
      data: requestData,
      success:function(data){
        var rData = $.parseJSON(data);
        var fee_data = rData;
        constructFeeSummary(fee_data);
         if(index == 0) {
          constructFeeHeader();
        }
        completed += Object.keys(fee_data).length;
        var progress = document.getElementById('progress-ind');
        progress.style.width = (completed/total_students)*100+'%';
        constructFeeReport(fee_data, index);
      },
      error: function(xhr, status, error) {
        // If this is the first batch, remove skeleton loading
        if (index === 0 && report_type == 1) {
          $('#skeleton-loading-container').remove();
        }

        // If this is the last batch and there's an error, make sure to re-enable the button
        if (index >= cohortStudentIds.length - 1) {
          $('#search').prop('disabled', false).val('Get Report');
          $("#progress").hide();
          $(".loading-icon").hide();
        }

        console.error("Error fetching report data:", error);
        alert('Error fetching report data. Please try again.');

        // Continue to the next batch despite the error
        index++;
        callReportGetter(index);
      }
    });
  }

  function constructFeeSummary(fee_data) {
     for(var k in fee_data){
      var feeBlueprint = fee_data[k].blueprint;
      for(var b in feeBlueprint){
        var feeDetail = fee_data[k].feeDetails[feeBlueprint[b].id];
        if (feeDetail !=undefined) {
          if (!(summaryData).hasOwnProperty(feeBlueprint[b].name)) {
            summaryData[feeBlueprint[b].name] = [];
            summaryData[feeBlueprint[b].name]['summary_total_fee_amount'] = 0;
            summaryData[feeBlueprint[b].name]['summary_total_fee_collected'] = 0;
            summaryData[feeBlueprint[b].name]['summary_total_fee_concession'] = 0;
            summaryData[feeBlueprint[b].name]['summary_total_adjustment'] = 0;
            summaryData[feeBlueprint[b].name]['summary_total_discount'] = 0;
            summaryData[feeBlueprint[b].name]['summary_total_balance'] = 0;
            summaryData[feeBlueprint[b].name]['summary_total_due_amount'] = 0;
            summaryData[feeBlueprint[b].name]['summary_total_fine'] = 0;
            summaryData[feeBlueprint[b].name]['summary_total_loan'] = 0;
            summaryData[feeBlueprint[b].name]['summary_total_refund'] = 0;
          }
          summaryData[feeBlueprint[b].name]['summary_total_fee_amount'] += parseFloat(feeDetail.total_fee);
          summaryData[feeBlueprint[b].name]['summary_total_fee_collected'] += parseFloat(feeDetail.total_fee_paid);
          summaryData[feeBlueprint[b].name]['summary_total_fee_concession'] += parseFloat(feeDetail.total_concession);
          summaryData[feeBlueprint[b].name]['summary_total_adjustment'] += parseFloat(feeDetail.total_adjustment);
          summaryData[feeBlueprint[b].name]['summary_total_discount'] += parseFloat(feeDetail.discount);
          summaryData[feeBlueprint[b].name]['summary_total_balance'] += parseFloat(feeDetail.balance);
          summaryData[feeBlueprint[b].name]['summary_total_due_amount'] += parseFloat(feeDetail.total_due_amount || 0);
          summaryData[feeBlueprint[b].name]['summary_total_fine'] += parseFloat(feeDetail.total_fine);
          summaryData[feeBlueprint[b].name]['summary_total_loan'] += parseFloat(feeDetail.loan_charges);
          summaryData[feeBlueprint[b].name]['summary_total_refund'] += parseFloat(feeDetail.refund_amount);
        }

      }
    }
  }
  // Function to create skeleton loading for detailed table
  function createSkeletonLoading() {
    var report_type = $('input[name="report_type"]:checked').val();

    // Only create skeleton for detailed view
    if (report_type != 1) return;

    // Get the number of columns based on settings
    var columnCount = 13; // Base columns
    if (fee_adjustment_amount) columnCount++;
    if (fee_fine_amount) columnCount++;
    if (loan_provider_charges) columnCount++;
    if (refund_amount) columnCount++;

    // Create skeleton rows
    var skeletonRows = '';
    for (var i = 0; i < 5; i++) {
      skeletonRows += '<tr class="skeleton-row">';

      // First row of each student group has rowspan cells
      if (i % 2 === 0) {
        // Student info columns with rowspan
        for (var j = 0; j < 9; j++) {
          skeletonRows += '<td rowspan="2" class="skeleton-cell"><div class="skeleton-loading"></div></td>';
        }

        // Regular data cells
        for (var j = 9; j < columnCount; j++) {
          skeletonRows += '<td class="skeleton-cell"><div class="skeleton-loading"></div></td>';
        }
      } else {
        // Fee type rows (no rowspan)
        for (var j = 0; j < columnCount - 9; j++) {
          skeletonRows += '<td class="skeleton-cell"><div class="skeleton-loading"></div></td>';
        }
      }

      skeletonRows += '</tr>';
    }

    return skeletonRows;
  }

  function constructFeeHeader() {
    // Create detailed table header
    var detailed_output = '<table id="fee_student_detailed_data" class="table table-bordered">';
    detailed_output += '<thead>';
    detailed_output += '<tr>';
    detailed_output += '<th>#</th>';
    detailed_output += '<th>Student Name</th>';
    detailed_output += '<th>Class/Section</th>';
    detailed_output += '<th>Admission No</th>';
    detailed_output += '<th>Enrollment No</th>';
    detailed_output += '<th>Boarding Type</th>';
    detailed_output += '<th>Application No</th>';
    detailed_output += '<th>Parent Name</th>';
    detailed_output += '<th>Parent Number</th>';
    detailed_output += '<th>Fee Type</th>'; 
    detailed_output += '<th>Total Amount</th>';
    detailed_output += '<th>Amount Paid</th>';
    detailed_output += '<th>Concession</th>';
    if (fee_adjustment_amount) {
      detailed_output += '<th>Adjustment</th>';
    }
    detailed_output += '<th>Discount</th>';
    detailed_output += '<th>Balance</th>';
    if (fee_fine_amount) {
      detailed_output += '<th>Fine</th>';
    }
    detailed_output += '<th>Percentage (%)</th>';
    detailed_output += '<th>Overdue Balance</th>';
    if (loan_provider_charges) {
      detailed_output += '<th>Loan Charges</th>';
    }
    if (refund_amount) {
      detailed_output += '<th>Refund Amount</th>';
    }
    detailed_output += '</tr>';
    detailed_output += '</thead>';

    // Add skeleton loading for detailed view
    var report_type = $('input[name="report_type"]:checked').val();
    if (report_type == 1) {
      detailed_output += '<tbody id="skeleton-loading-container">';
      detailed_output += createSkeletonLoading();
      detailed_output += '</tbody>';
    }

    detailed_output += '</table>';
    $('.detailed-view').html(detailed_output);

    // Create summary table header (without Fee Type column)
    var summary_output = '<table id="fee_student_summary_data" class="table table-bordered">';
    summary_output += '<thead>';
    summary_output += '<tr>';
    summary_output += '<th>#</th>';
    summary_output += '<th>Student Name</th>';
    summary_output += '<th>Class/Section</th>';
    summary_output += '<th>Admission No</th>';
    summary_output += '<th>Enrollment No</th>';
    summary_output += '<th>Boarding Type</th>';
    summary_output += '<th>Application No</th>';
    summary_output += '<th>Parent Name</th>';
    summary_output += '<th>Parent Number</th>';
    // No Fee Type column in summary table
    summary_output += '<th>Total Amount</th>';
    summary_output += '<th>Amount Paid</th>';
    summary_output += '<th>Concession</th>';
    if (fee_adjustment_amount) {
      summary_output += '<th>Adjustment</th>';
    }
    summary_output += '<th>Discount</th>';
    summary_output += '<th>Balance</th>';
    if (fee_fine_amount) {
      summary_output += '<th>Fine</th>';
    }
    summary_output += '<th>Percentage (%)</th>';
    summary_output += '<th>Overdue Balance</th>';
    if (loan_provider_charges) {
      summary_output += '<th>Loan Charges</th>';
    }
    if (refund_amount) {
      summary_output += '<th>Refund Amount</th>';
    }
    summary_output += '</tr>';
    summary_output += '</thead>';
    summary_output += '</table>';
    $('.summary-view').html(summary_output);
  }

  // Global counter for displayed rows
  var displayedRowCount = 0;

  function constructFeeReport(fee_data, index) {
    var report_type = $('input[name="report_type"]:checked').val();
    var m = 0; // Local counter for this batch

    // Create separate HTML for detailed and summary views
    var detailedHtml = '';
    var summaryHtml = '';

    // For the first batch of data, remove skeleton loading
    if (index === 0 && report_type == 1) {
      // Remove skeleton loading container
      $('#skeleton-loading-container').remove();
    }

    // Start tbody for both tables
    if (index === 0) {
      detailedHtml += '<tbody id="fee-data-container">';
    } else {
      detailedHtml += '<tbody>';
    }
    summaryHtml += '<tbody>';

    for(var k in fee_data){
      var totalFeeAmount = 0;
      var totalFeepaid = 0;
      var totalAdjustment = 0;
      var totalDiscount = 0;
      var totalBalance = 0;
      var totalFine = 0;
      var totalLoancharges = 0;
      var totalConcession = 0;
      var totalRefund = 0;

      // Get validFeeDetails from the current student
      var validFeeDetails = [];
      if (fee_data[k].blueprint && fee_data[k].blueprint.length > 0) {
        for(var b in fee_data[k].blueprint){
          var feeDetail = fee_data[k].feeDetails[fee_data[k].blueprint[b].id];
          if (feeDetail != undefined) {
            validFeeDetails.push({
              blueprint: fee_data[k].blueprint[b],
              detail: feeDetail
            });
          }
        }
      }

      // Skip if no valid fee details, but still increment the counter
      if (validFeeDetails.length === 0) {
        m++;
        continue;
      }

      // Calculate totals from valid fee details
      for(var i = 0; i < validFeeDetails.length; i++){
        var feeDetail = validFeeDetails[i].detail;
        if (feeDetail.student_id == fee_data[k].stdId) {
          totalFeeAmount += parseFloat(feeDetail.total_fee);
          totalFeepaid += parseFloat(feeDetail.total_fee_paid);
          totalConcession += parseFloat(feeDetail.total_concession);
          totalAdjustment += parseFloat(feeDetail.total_adjustment);
          totalDiscount += parseFloat(feeDetail.discount);
          totalBalance += parseFloat(feeDetail.balance);
          totalFine += parseFloat(feeDetail.total_fine);
          totalLoancharges += parseFloat(feeDetail.loan_charges);
          totalRefund += parseFloat(feeDetail.refund_amount);
        }
      }

      // Calculate total due amount
      var totalDueAmount = 0;
      for(var i = 0; i < validFeeDetails.length; i++){
        var feeDetail = validFeeDetails[i].detail;
        if (feeDetail.student_id == fee_data[k].stdId) {
          totalDueAmount += parseFloat(feeDetail.total_due_amount || 0);
        }
      }

      // Calculate percentage
      var percentage = 0;
      if (totalFeeAmount > 0) {
        percentage = (totalBalance / totalFeeAmount) * 100;
      }

      // SUMMARY MODE - Only show student totals
      summaryHtml += '<tr>';
      summaryHtml += '<td>'+(displayedRowCount+1)+'</td>'; // Use global counter for sequential numbering
      summaryHtml += '<td>'+fee_data[k].stdName+'</td>';
      summaryHtml += '<td>'+fee_data[k].class_name+'</td>';
      summaryHtml += '<td>'+fee_data[k].admission_no+'</td>';
      summaryHtml += '<td>'+(fee_data[k].enrollment_no || 'N/A')+'</td>';
      summaryHtml += '<td>'+(fee_data[k].boarding_type || 'N/A')+'</td>';
      summaryHtml += '<td>'+(fee_data[k].application_no || 'N/A')+'</td>';
      summaryHtml += '<td>'+(fee_data[k].parent_name || 'N/A')+'</td>';
      summaryHtml += '<td>'+(fee_data[k].parent_number || 'N/A')+'</td>';
      // No Fee Type column in summary mode
      summaryHtml += '<td>'+in_currency(totalFeeAmount)+'</td>';
      summaryHtml += '<td>'+in_currency(totalFeepaid)+'</td>';
      summaryHtml += '<td>'+in_currency(totalConcession)+'</td>';
      if (fee_adjustment_amount) {
        summaryHtml += '<td>'+in_currency(totalAdjustment)+'</td>';
      }
      summaryHtml += '<td>'+in_currency(totalDiscount)+'</td>';
      summaryHtml += '<td>'+in_currency(totalBalance)+'</td>';

      if (fee_fine_amount) {
        summaryHtml += '<td>'+in_currency(totalFine)+'</td>';
      }

      summaryHtml += '<td>'+percentage.toFixed(2) + "%"+'</td>';
      summaryHtml += '<td>'+in_currency(totalDueAmount)+'</td>';

      if (loan_provider_charges) {
        summaryHtml += '<td>'+in_currency(totalLoancharges)+'</td>';
      }
      if (refund_amount) {
        summaryHtml += '<td>'+in_currency(totalRefund)+'</td>';
      }
      summaryHtml += '</tr>';

      // DETAILED MODE - Show all fee types per student
      detailedHtml += '<tr>';
      // Now we know this student has at least one valid fee detail, so we can add the student info
      detailedHtml += '<td rowspan="'+validFeeDetails.length+'" >'+(displayedRowCount+1)+'</td>'; // Use global counter for sequential numbering
      detailedHtml += '<td rowspan="'+validFeeDetails.length+'" >'+fee_data[k].stdName+'</td>';
      detailedHtml += '<td rowspan="'+validFeeDetails.length+'" >'+fee_data[k].class_name+'</td>';
      detailedHtml += '<td rowspan="'+validFeeDetails.length+'" >'+fee_data[k].admission_no+'</td>';
      detailedHtml += '<td rowspan="'+validFeeDetails.length+'" >'+(fee_data[k].enrollment_no || 'N/A')+'</td>';
      detailedHtml += '<td rowspan="'+validFeeDetails.length+'" >'+(fee_data[k].boarding_type || 'N/A')+'</td>';
      detailedHtml += '<td rowspan="'+validFeeDetails.length+'" >'+(fee_data[k].application_no || 'N/A')+'</td>';
      detailedHtml += '<td rowspan="'+validFeeDetails.length+'" >'+(fee_data[k].parent_name || 'N/A')+'</td>';
      detailedHtml += '<td rowspan="'+validFeeDetails.length+'" >'+(fee_data[k].parent_number || 'N/A')+'</td>';

      // Add each valid fee detail
      for(var i = 0; i < validFeeDetails.length; i++){
        var blueprint = validFeeDetails[i].blueprint;
        var feeDetail = validFeeDetails[i].detail;

        detailedHtml += '<td>'+blueprint.name+'</td>';
        detailedHtml += '<td>'+in_currency(feeDetail.total_fee)+'</td>';
        detailedHtml += '<td>'+in_currency(feeDetail.total_fee_paid)+'</td>';
        detailedHtml += '<td>'+in_currency(feeDetail.total_concession)+'</td>';
        if (fee_adjustment_amount) {
          detailedHtml += '<td>'+in_currency(feeDetail.total_adjustment)+'</td>';
        }
        detailedHtml += '<td>'+in_currency(feeDetail.discount)+'</td>';
        detailedHtml += '<td>'+in_currency(feeDetail.balance)+'</td>';

        if (fee_fine_amount) {
          detailedHtml += '<td>'+in_currency(feeDetail.total_fine)+'</td>';
        }

        // Calculate percentage for this fee detail
        var detailPercentage = 0;
        if (parseFloat(feeDetail.total_fee) > 0) {
          detailPercentage = (parseFloat(feeDetail.balance) / parseFloat(feeDetail.total_fee)) * 100;
        }
        detailedHtml += '<td>'+detailPercentage.toFixed(2) + "%"+'</td>';

        // Add overdue balance for this fee detail
        detailedHtml += '<td>'+in_currency(feeDetail.total_due_amount || 0)+'</td>';

        if (loan_provider_charges) {
          detailedHtml += '<td>'+in_currency(feeDetail.loan_charges)+'</td>';
        }
        if (refund_amount) {
          detailedHtml += '<td>'+in_currency(feeDetail.refund_amount)+'</td>';
        }

        // Close the row except for the last item
        detailedHtml += '</tr>';

        // For all but the last row, start a new row
        if (i < validFeeDetails.length - 1) {
          detailedHtml += '<tr>';
        }
      }

      // Add total row for detailed mode
      detailedHtml += '<tr style="color: #7b6a6a;font-size: 18px;" class="total-row">';
      detailedHtml += '<th colspan="10" style="text-align:right" class="total-cell" data-original-colspan="10">Total</th>';

      detailedHtml += '<th>'+in_currency(totalFeeAmount)+'</th>';
      detailedHtml += '<th>'+in_currency(totalFeepaid)+'</th>';
      detailedHtml += '<th>'+in_currency(totalConcession)+'</th>';
      if (fee_adjustment_amount) {
        detailedHtml += '<th>'+in_currency(totalAdjustment)+'</th>';
      }
      detailedHtml += '<th>'+in_currency(totalDiscount)+'</th>';
      detailedHtml += '<th>'+in_currency(totalBalance)+'</th>';

      if (fee_fine_amount) {
        detailedHtml += '<th>'+in_currency(totalFine)+'</th>';
      }

      detailedHtml += '<th>'+percentage.toFixed(2) + "%"+'</th>';
      detailedHtml += '<th>'+in_currency(totalDueAmount)+'</th>';

      if (loan_provider_charges) {
        detailedHtml += '<th>'+in_currency(totalLoancharges)+'</th>';
      }
      if (refund_amount) {
        detailedHtml += '<th>'+in_currency(totalRefund)+'</th>';
      }
      detailedHtml += '</tr>';

      m++; // Increment local counter
      displayedRowCount++; // Increment global counter for sequential numbering
    }

    // Close tbody for both tables
    detailedHtml += '</tbody>';
    summaryHtml += '</tbody>';

    // Append to the appropriate tables
    $('#fee_student_detailed_data').append(detailedHtml);
    $('#fee_student_summary_data').append(summaryHtml);

    index++;
    callReportGetter(index);
  }


$("#classId").change(function () {
    const previouslySelectedSections = $('#classSectionId').val();

    $('#classSectionId').html('');
    const selectedValue = $(this).val();

    $.ajax({
        url: '<?php echo site_url('feesv2/reports_v2/get_class_section_by_fees_selection_class') ?>',
        data: { 'feeclass': selectedValue },
        type: "post",
        success: function (data) {
            const resdata = JSON.parse(data);
            let option = '';
            resdata.forEach(item => {
                option += `<option value="${item.section_id}">${item.class_section}</option>`;
            });

            $("#classSectionId").html(option);
            $('#classSectionId').selectpicker('refresh');

            if (previouslySelectedSections) {
                $('#classSectionId').val(previouslySelectedSections).selectpicker('refresh');
            }
        },
        error: function (err) {
            console.log(err);
        }
    });
});
</script>

<script type="text/javascript">
  function in_currency(amount) {
    var formatter = new Intl.NumberFormat('en-IN', {
      // style: 'currency',
      currency: 'INR',
    });
    return formatter.format(amount);
  }

  function initializeSearch() {
    // Reset any previous search
    $('#table-search').val('');

    // Reattach the search event handler
    $('#table-search').off('keyup').on('keyup', function() {
      const searchText = $(this).val();
      const report_type = $('input[name="report_type"]:checked').val();
      const searchTextLower = searchText.toLowerCase();

      if (report_type == 2) {
        // SUMMARY MODE - Simple row-by-row search on the summary table
        $('#fee_student_summary_data tbody tr').each(function() {
          let rowVisible = false;

          // Search in all cells of the row
          $(this).find('td').each(function() {
            if ($(this).text().toLowerCase().indexOf(searchTextLower) > -1) {
              rowVisible = true;
              return false; // Break the loop if found
            }
          });

          // Show/hide the row based on search result
          $(this).toggle(rowVisible);
        });
      } else {
        // DETAILED MODE - Handle the complex structure in the detailed table

        // If search is empty, show all rows
        if (searchTextLower === '') {
          $('#fee_student_detailed_data tbody tr').show();
          return;
        }

        // First, hide all rows
        $('#fee_student_detailed_data tbody tr').hide();

        // Find student rows that match the search text
        $('#fee_student_detailed_data tbody tr').each(function() {
          let rowVisible = false;

          // Check if any cell in this row contains the search text
          $(this).find('td').each(function() {
            if ($(this).text().toLowerCase().indexOf(searchTextLower) > -1) {
              rowVisible = true;
              return false; // Break the loop if found
            }
          });

          if (rowVisible) {
            // Show this row
            $(this).show();

            // If this is a student row (has rowspan), show all related fee type rows
            if ($(this).find('td[rowspan]').length > 0) {
              const rowspan = parseInt($(this).find('td[rowspan]:first').attr('rowspan'));
              let nextRow = $(this).next();

              // Show the next rowspan-1 rows (fee type rows)
              for (let i = 1; i < rowspan; i++) {
                if (nextRow.length) {
                  nextRow.show();
                  nextRow = nextRow.next();
                }
              }

              // Show the total row if it exists
              if (nextRow.length && nextRow.find('th:contains("Total")').length > 0) {
                nextRow.show();
              }
            }
            // If this is a fee type row, show the parent student row and all siblings
            else if (!$(this).find('th:contains("Total")').length) {
              // Find the previous student row
              let prevRow = $(this);
              while (prevRow.length && prevRow.find('td[rowspan]').length === 0) {
                prevRow = prevRow.prev();
              }

              if (prevRow.length) {
                // Show the student row
                prevRow.show();

                // Show all fee type rows for this student
                const rowspan = parseInt(prevRow.find('td[rowspan]:first').attr('rowspan'));
                let siblingRow = prevRow.next();

                for (let i = 1; i < rowspan; i++) {
                  if (siblingRow.length) {
                    siblingRow.show();
                    siblingRow = siblingRow.next();
                  }
                }

                // Show the total row if it exists
                if (siblingRow.length && siblingRow.find('th:contains("Total")').length > 0) {
                  siblingRow.show();
                }
              }
            }
            // If this is a total row, show the parent student row and all fee type rows
            else if ($(this).find('th:contains("Total")').length) {
              // Find the previous student row
              let currentRow = $(this).prev();
              let studentRow = null;

              // Go back until we find the student row
              while (currentRow.length) {
                if (currentRow.find('td[rowspan]').length > 0) {
                  studentRow = currentRow;
                  break;
                }
                currentRow = currentRow.prev();
              }

              if (studentRow) {
                // Show the student row
                studentRow.show();

                // Show all fee type rows
                const rowspan = parseInt(studentRow.find('td[rowspan]:first').attr('rowspan'));
                let feeRow = studentRow.next();

                for (let i = 1; i < rowspan; i++) {
                  if (feeRow.length) {
                    feeRow.show();
                    feeRow = feeRow.next();
                  }
                }
              }
            }
          }
        });
      }
    });
  }



  // Global function to show skeleton loading for any table
  function showTableSkeletonLoading(table) {
    // Only add skeleton if it doesn't already exist
    if ($('#column-toggle-skeleton').length === 0) {
      // First, hide the actual table
      table.css('visibility', 'hidden');

      // Create a skeleton table container that will be positioned exactly where the real table is
      const tablePos = table.offset();
      const tableWidth = table.outerWidth();
      const tableHeight = table.outerHeight();
      const tableParent = table.parent();

      // Create a skeleton table with the same structure as the real table
      const skeletonTable = $('<div id="column-toggle-skeleton" class="skeleton-table-container"></div>');

      // Position the skeleton table exactly where the real table is
      skeletonTable.css({
        position: 'absolute',
        top: tablePos.top + 'px',
        left: tablePos.left + 'px',
        width: tableWidth + 'px',
        height: tableHeight + 'px',
        zIndex: 4,
        overflow: 'hidden',
        backgroundColor: '#fff'
      });

      // Create a skeleton table with header and rows
      const skeletonTableHTML = $('<table class="table table-bordered skeleton-table" style="width: 100%;"></table>');

      // Create header row based on the real table's header
      const headerRow = $('<tr></tr>');
      const columnCount = table.find('thead th').length;

      // Add skeleton header cells
      for (let i = 0; i < columnCount; i++) {
        headerRow.append('<th><div class="skeleton-loading" style="height: 20px;"></div></th>');
      }

      // Add header to skeleton table
      skeletonTableHTML.append($('<thead></thead>').append(headerRow));

      // Create tbody for skeleton table
      const skeletonTbody = $('<tbody></tbody>');

      // Add skeleton rows based on visible rows (limited for performance)
      const visibleRows = Math.min(15, table.find('tbody tr:visible').length);
      for (let i = 0; i < visibleRows; i++) {
        const skeletonRow = $('<tr></tr>');

        // Add cells to the row
        for (let j = 0; j < columnCount; j++) {
          skeletonRow.append('<td><div class="skeleton-loading" style="height: 20px;"></div></td>');
        }

        skeletonTbody.append(skeletonRow);
      }

      // Add tbody to skeleton table
      skeletonTableHTML.append(skeletonTbody);

      // Add the skeleton table to the container
      skeletonTable.append(skeletonTableHTML);

      // Add the skeleton table to the body
      $('body').append(skeletonTable);

      // Set a timeout to remove the skeleton if it gets stuck
      setTimeout(function() {
        $('#column-toggle-skeleton').remove();
        table.css('visibility', 'visible');
      }, 3000); // Remove after 3 seconds if not removed by adjustTableLayout
    }
  }

  // Make the function globally accessible
  window.showTableSkeletonLoading = showTableSkeletonLoading;

  // Initialize column visibility without DataTables
  function initializeColumnVisibility() {
    const report_type = $('input[name="report_type"]:checked').val();

    // Get the appropriate table based on report type
    const table = report_type == 2 ? $('#fee_student_summary_data') : $('#fee_student_detailed_data');
    const headers = table.find('thead th');

    // Create separate objects to track column visibility for each table
    if (!window.columnVisibilityDetailed) {
      window.columnVisibilityDetailed = {};
      // Initialize all columns as visible for detailed table
      $('#fee_student_detailed_data').find('thead th').each(function(index) {
        window.columnVisibilityDetailed[index] = true;
      });
    }

    if (!window.columnVisibilitySummary) {
      window.columnVisibilitySummary = {};
      // Initialize all columns as visible for summary table
      $('#fee_student_summary_data').find('thead th').each(function(index) {
        window.columnVisibilitySummary[index] = true;
      });
    }

    // Function to toggle column visibility
    function toggleColumnVisibility(columnIndex, isVisible) {
      // Get the current report type - this needs to be fetched each time to ensure it's current
      const current_report_type = $('input[name="report_type"]:checked').val();

      // Get the appropriate table based on current report type
      const tableSelector = current_report_type == 2 ? '#fee_student_summary_data' : '#fee_student_detailed_data';
      const currentTable = $(tableSelector);

      if (!currentTable.length) {
        console.warn('Table not found:', tableSelector);
        return;
      }

      // Only show skeleton loading for detailed view during column toggle
      if (current_report_type == 1) {
        // Create a skeleton loading overlay for the table
        showTableSkeletonLoading(currentTable);
      }

      // Update visibility state for the appropriate table
      if (current_report_type == 2) {
        // Initialize if not exists
        if (!window.columnVisibilitySummary) {
          window.columnVisibilitySummary = {};
        }
        window.columnVisibilitySummary[columnIndex] = isVisible;
      } else {
        // Initialize if not exists
        if (!window.columnVisibilityDetailed) {
          window.columnVisibilityDetailed = {};
        }
        window.columnVisibilityDetailed[columnIndex] = isVisible;
      }

      // Update checkbox state in column menu if it exists
      $('.column-menu input[data-column="' + columnIndex + '"]').prop('checked', isVisible);

      if (current_report_type == 2) {
        // SUMMARY MODE - Simple column toggling
        // Get the nth column from all rows (both header and data)
        const nthColumn = currentTable.find(`tr > *:nth-child(${columnIndex + 1})`);

        // Use CSS display property instead of jQuery toggle for better performance
        nthColumn.css('display', isVisible ? '' : 'none');
      } else {
        // DETAILED MODE - Handle complex structure with rowspans

        // First, handle the header row
        const headerCell = currentTable.find('thead tr th').eq(columnIndex);
        headerCell.css('display', isVisible ? '' : 'none');

        // Now handle the data rows - using a more efficient approach
        const tbody = currentTable.find('tbody');

        // For student info columns (0-3)
        if (columnIndex <= 3) {
          // Find all cells with rowspan in the specified column position
          tbody.find(`tr td[rowspan]:nth-child(${columnIndex + 1})`)
            .css('display', isVisible ? '' : 'none');
        }
        // For Fee Type column (4)
        else if (columnIndex === 4) {
          // Process rows in batches for better performance
          const rows = tbody.find('tr');
          const batchSize = 20;
          let processedRows = 0;

          function processRowBatch() {
            const endIndex = Math.min(processedRows + batchSize, rows.length);

            for (let i = processedRows; i < endIndex; i++) {
              const row = rows.eq(i);

              // Check if this is a student row (has rowspan)
              if (row.find('td[rowspan]').length > 0) {
                // This is the first row of a student group
                row.find(`td:eq(${columnIndex})`)
                  .css('display', isVisible ? '' : 'none');
              }
              // Handle subsequent rows of each student group
              else if (row.find('th:contains("Total")').length === 0) {
                // This is a subsequent row (not a total row)
                // For these rows, Fee Type is the first column (index 0)
                row.find('td:first')
                  .css('display', isVisible ? '' : 'none');
              }
              // Handle total rows
              else if (row.find('th:contains("Total")').length > 0) {
                // For total rows, we need to adjust the colspan of the first cell
                const totalCell = row.find('th:first');
                if (totalCell.length) {
                  // Instead of modifying colspan, just ensure the cell is visible
                  // This prevents the total row from shifting right
                  totalCell.css('display', '');

                  // Only adjust colspan if explicitly set
                  if (totalCell.attr('colspan')) {
                    // Store the original colspan if not already stored
                    if (!totalCell.data('original-colspan')) {
                      totalCell.data('original-colspan', totalCell.attr('colspan'));
                    }

                    // Use the original colspan value
                    const originalColspan = parseInt(totalCell.data('original-colspan'));

                    // Count how many columns are visible in the student info section (0-9)
                    let visibleStudentInfoCols = 0;
                    for (let i = 0; i <= 9; i++) {
                      if (window.columnVisibilityDetailed[i] !== false) {
                        visibleStudentInfoCols++;
                      }
                    }

                    // Set the colspan to match the number of visible columns
                    totalCell.attr('colspan', visibleStudentInfoCols);
                  }
                }
              }
            }

            processedRows = endIndex;

            if (processedRows < rows.length) {
              // Process next batch asynchronously
              setTimeout(processRowBatch, 0);
            }
          }

          // Start processing rows in batches
          processRowBatch();
        }
        // For data columns (5+)
        else {
          // Process rows in batches for better performance
          const rows = tbody.find('tr');
          const batchSize = 20;
          let processedRows = 0;

          function processRowBatch() {
            const endIndex = Math.min(processedRows + batchSize, rows.length);

            for (let i = processedRows; i < endIndex; i++) {
              const row = rows.eq(i);

              // Check if this is a student row (has rowspan)
              if (row.find('td[rowspan]').length > 0) {
                // This is the first row of a student group
                row.find(`td:eq(${columnIndex})`)
                  .css('display', isVisible ? '' : 'none');
              }
              // Handle subsequent rows of each student group
              else if (row.find('th:contains("Total")').length === 0) {
                // This is a subsequent row (not a total row)
                // For these rows, we need to adjust the column index
                // The first 4 columns (0-3) are rowspan in the first row, so they don't appear in subsequent rows
                const adjustedColumnIndex = columnIndex - 4;
                row.find(`td:eq(${adjustedColumnIndex})`)
                  .css('display', isVisible ? '' : 'none');
              }
              // Handle total rows
              else if (row.find('th:contains("Total")').length > 0) {
                // For total rows, we need to adjust the index
                // The first cell is a th with colspan, so data cells start at index 1
                const adjustedColumnIndex = columnIndex - 4;
                row.find(`th, td`).eq(adjustedColumnIndex)
                  .css('display', isVisible ? '' : 'none');
              }
            }

            processedRows = endIndex;

            if (processedRows < rows.length) {
              // Process next batch asynchronously
              setTimeout(processRowBatch, 0);
            } else {
              // All rows processed, force table layout adjustment
              adjustTableLayout();
            }
          }

          // Start processing rows in batches
          processRowBatch();
        }
      }

      // Function to show skeleton loading overlay for the table during column toggle
      function showTableSkeletonLoading(table) {
        // Only add skeleton if it doesn't already exist
        if ($('#column-toggle-skeleton').length === 0) {
          // Create a skeleton overlay container
          const skeletonOverlay = $('<div id="column-toggle-skeleton" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(255, 255, 255, 0.7); z-index: 5;"></div>');

          // Get table dimensions and position
          const tablePos = table.offset();
          const tableWidth = table.outerWidth();
          const tableHeight = table.outerHeight();

          // Position the overlay
          skeletonOverlay.css({
            top: tablePos.top + 'px',
            left: tablePos.left + 'px',
            width: tableWidth + 'px',
            height: tableHeight + 'px'
          });

          // Add skeleton rows
          const skeletonContent = $('<div class="skeleton-content" style="padding: 10px;"></div>');

          // Add skeleton rows based on visible rows (limited to 10 for performance)
          const visibleRows = Math.min(10, table.find('tbody tr:visible').length);
          for (let i = 0; i < visibleRows; i++) {
            const skeletonRow = $('<div class="skeleton-loading" style="height: 30px; margin-bottom: 5px;"></div>');
            skeletonContent.append(skeletonRow);
          }

          skeletonOverlay.append(skeletonContent);
          $('body').append(skeletonOverlay);

          // Set a timeout to remove the skeleton if it gets stuck
          setTimeout(function() {
            $('#column-toggle-skeleton').fadeOut(300, function() {
              $(this).remove();
            });
          }, 2000); // Remove after 2 seconds if not removed by adjustTableLayout
        }
      }

      // Force table to adjust layout
      function adjustTableLayout() {
        // Count visible columns
        const visibleColumns = current_report_type == 2
          ? Object.values(window.columnVisibilitySummary || {}).filter(v => v).length
          : Object.values(window.columnVisibilityDetailed || {}).filter(v => v).length;

        // Set table layout to auto to allow columns to adjust
        currentTable.css('table-layout', 'auto');

        // Force browser to recalculate layout
        currentTable.width(currentTable.width());

        // Trigger window resize to help with responsive adjustments
        $(window).trigger('resize');

        // Remove skeleton loading and show the real table with a slight delay to ensure rendering is complete
        setTimeout(function() {
          // First make sure all column visibility changes are applied
          if (current_report_type == 1) {
            // For detailed view, we need to ensure all column visibility changes are applied
            const columnVisibility = window.columnVisibilityDetailed || {};

            // Apply any pending visibility changes
            for (let colIndex in columnVisibility) {
              const isVisible = columnVisibility[colIndex];
              const headerCell = currentTable.find('thead tr th').eq(colIndex);
              headerCell.css('display', isVisible ? '' : 'none');

              // Apply to data cells based on column type
              if (colIndex <= 3) {
                // Student info columns with rowspan
                currentTable.find(`tbody tr td[rowspan]:nth-child(${parseInt(colIndex) + 1})`)
                  .css('display', isVisible ? '' : 'none');
              } else if (colIndex == 4) {
                // Fee Type column
                currentTable.find('tbody tr').each(function() {
                  const row = $(this);
                  if (row.find('td[rowspan]').length > 0) {
                    // First row of student group
                    row.find(`td:eq(${colIndex})`).css('display', isVisible ? '' : 'none');
                  } else if (row.find('th:contains("Total")').length === 0) {
                    // Fee type row
                    row.find('td:first').css('display', isVisible ? '' : 'none');
                  }
                });
              } else {
                // Data columns
                currentTable.find('tbody tr').each(function() {
                  const row = $(this);
                  if (row.find('td[rowspan]').length > 0) {
                    // First row of student group
                    row.find(`td:eq(${colIndex})`).css('display', isVisible ? '' : 'none');
                  } else if (row.find('th:contains("Total")').length === 0) {
                    // Fee type row
                    const adjustedIndex = colIndex - 9; // Updated from 4 to 9 to account for new columns
                    row.find(`td:eq(${adjustedIndex})`).css('display', isVisible ? '' : 'none');
                  } else if (row.find('th:contains("Total")').length > 0) {
                    // Total row
                    const adjustedIndex = colIndex - 9; // Updated from 4 to 9 to account for new columns
                    row.find(`th, td`).eq(adjustedIndex).css('display', isVisible ? '' : 'none');

                    // Update the colspan of the "Total" cell
                    const totalCell = row.find('th:contains("Total")');
                    if (totalCell.length && totalCell.attr('colspan')) {
                      // Store original colspan if not already stored
                      if (!totalCell.data('original-colspan')) {
                        totalCell.data('original-colspan', totalCell.attr('colspan'));
                      }

                      // Count visible columns in student info section (0-9)
                      let visibleStudentInfoCols = 0;
                      for (let i = 0; i <= 9; i++) {
                        if (window.columnVisibilityDetailed[i] !== false) {
                          visibleStudentInfoCols++;
                        }
                      }

                      // Update colspan
                      totalCell.attr('colspan', visibleStudentInfoCols);
                    }
                  }
                });
              }
            }
          }

          // Now remove the skeleton and show the real table
          $('#column-toggle-skeleton').fadeOut(200, function() {
            $(this).remove();
            // Make the real table visible again
            currentTable.css('visibility', 'visible');
          });
        }, 500); // Increased delay to ensure all changes are applied
      }

      // For summary mode or simple column operations, adjust layout immediately
      if (current_report_type == 2 || columnIndex <= 3) {
        adjustTableLayout();
      }
      // For complex operations in detailed mode, layout adjustment is handled after batch processing
    }

    // Store the toggle function and skeleton loading function for later use
    window.toggleColumnVisibility = toggleColumnVisibility;
    window.toggleColumnVisibility.showTableSkeletonLoading = showTableSkeletonLoading;
  }

  // Initialize column toggle menu
  function initializeColumnToggle() {
    try {
      // Make sure we have the column visibility tracking initialized
      initializeColumnVisibility();

      // Remove any existing column menu
      $('.column-menu').remove();

      // Create dropdown menu for column visibility
      const columnMenu = $('<div class="column-menu dropdown-menu"></div>');

      // Get the appropriate table and visibility state based on report type
      const report_type = $('input[name="report_type"]:checked').val();
      const tableSelector = report_type == 2 ? '#fee_student_summary_data' : '#fee_student_detailed_data';
      const table = $(tableSelector);

      if (!table.length) {
        console.warn('Table not found:', tableSelector);
        return;
      }

      const headers = table.find('thead th');
      const columnVisibility = report_type == 2 ? window.columnVisibilitySummary : window.columnVisibilityDetailed;

      // Add "Select All" and "Deselect All" options as buttons in a row
      const actionButtonsRow = $(`
        <div class="column-actions-row" style="display: flex; justify-content: space-between; padding: 8px 10px; border-bottom: 1px solid #e0e0e0; margin-bottom: 5px;">
          <button class="btn btn-sm btn-info select-all-btn" style="flex: 1; margin-right: 5px; font-size: 12px; padding: 4px 8px;">Select All</button>
          <button class="btn btn-sm btn-info deselect-all-btn" style="flex: 1; margin-left: 5px; font-size: 12px; padding: 4px 8px;">Deselect All</button>
        </div>
      `);

      columnMenu.append(actionButtonsRow);

      // Add checkboxes for each column
      headers.each(function(index) {
        const headerText = $(this).text().trim();

        // Skip empty headers or very long text
        if (!headerText || headerText.length > 30) {
          return;
        }

        // Check if column is currently visible
        // Default to visible if not explicitly set to false
        const isVisible = columnVisibility[index] !== false;

        // Create checkbox item with just the column name
        const item = $(`
          <div class="dropdown-item">
            <label>
              <input type="checkbox" data-column="${index}" ${isVisible ? 'checked' : ''}> ${headerText}
            </label>
          </div>
        `);

        // Add to menu
        columnMenu.append(item);
      });

      // Add the menu to the document
      $('body').append(columnMenu);

      // Style the menu (but don't show it yet - that's handled by the document click handler)
      columnMenu.css({
        position: 'absolute',
        display: 'none',
        maxHeight: '300px',
        overflowY: 'auto',
        zIndex: 1000,
        backgroundColor: '#fff',
        border: '1px solid #ccc',
        borderRadius: '4px',
        boxShadow: '0 6px 12px rgba(0,0,0,.175)',
        padding: '5px 0'
      });

      // Note: Global document click handler is defined in document.ready

      // Handle "Select All" click
      actionButtonsRow.find('.select-all-btn').on('click', function(e) {
        e.stopPropagation();
        e.preventDefault();

        // Show skeleton loading for detailed view
        if (report_type == 1) {
          const table = $('#fee_student_detailed_data');
          if (window.toggleColumnVisibility && typeof window.toggleColumnVisibility.showTableSkeletonLoading === 'function') {
            window.toggleColumnVisibility.showTableSkeletonLoading(table);
          } else {
            // Fallback if the function isn't available
            showTableSkeletonLoading(table);
          }
        }

        // Check all checkboxes
        columnMenu.find('input[type="checkbox"]').prop('checked', true);

        // Update visibility state and apply changes
        headers.each(function(index) {
          // Skip processing if this is not a valid column
          const headerText = $(this).text().trim();
          if (!headerText || headerText.length > 30) {
            return;
          }

          if (report_type == 2) {
            if (!window.columnVisibilitySummary) {
              window.columnVisibilitySummary = {};
            }
            window.columnVisibilitySummary[index] = true;
          } else {
            if (!window.columnVisibilityDetailed) {
              window.columnVisibilityDetailed = {};
            }
            window.columnVisibilityDetailed[index] = true;
          }

          // Apply visibility
          if (window.toggleColumnVisibility) {
            window.toggleColumnVisibility(index, true);
          }
        });
      });

      // Handle "Deselect All" click
      actionButtonsRow.find('.deselect-all-btn').on('click', function(e) {
        e.stopPropagation();
        e.preventDefault();

        // Show skeleton loading for detailed view
        if (report_type == 1) {
          const table = $('#fee_student_detailed_data');
          if (window.toggleColumnVisibility && typeof window.toggleColumnVisibility.showTableSkeletonLoading === 'function') {
            window.toggleColumnVisibility.showTableSkeletonLoading(table);
          } else {
            // Fallback if the function isn't available
            showTableSkeletonLoading(table);
          }
        }

        // Uncheck all checkboxes
        columnMenu.find('input[type="checkbox"]').prop('checked', false);

        // Update visibility state and apply changes
        headers.each(function(index) {
          // Skip processing if this is not a valid column
          const headerText = $(this).text().trim();
          if (!headerText || headerText.length > 30) {
            return;
          }

          if (report_type == 2) {
            if (!window.columnVisibilitySummary) {
              window.columnVisibilitySummary = {};
            }
            window.columnVisibilitySummary[index] = false;
          } else {
            if (!window.columnVisibilityDetailed) {
              window.columnVisibilityDetailed = {};
            }
            window.columnVisibilityDetailed[index] = false;
          }

          // Apply visibility
          if (window.toggleColumnVisibility) {
            window.toggleColumnVisibility(index, false);
          }
        });
      });

      // Prevent menu from closing when clicking inside it
      columnMenu.on('click', function(e) {
        // Always stop propagation for clicks inside the menu
        // except for the action buttons which handle their own events
        if (!$(e.target).hasClass('select-all-btn') && !$(e.target).hasClass('deselect-all-btn')) {
          e.stopPropagation();
        }
      });

      // Handle checkbox changes - toggle column visibility
      columnMenu.on('change', 'input[type="checkbox"]', function() {
        const columnIndex = $(this).data('column');
        const isChecked = $(this).prop('checked');

        // Show skeleton loading for detailed view
        if (report_type == 1) {
          const table = $('#fee_student_detailed_data');
          if (window.toggleColumnVisibility && typeof window.toggleColumnVisibility.showTableSkeletonLoading === 'function') {
            window.toggleColumnVisibility.showTableSkeletonLoading(table);
          } else {
            // Fallback if the function isn't available
            showTableSkeletonLoading(table);
          }
        }

        // Update visibility state
        if (report_type == 2) {
          window.columnVisibilitySummary[columnIndex] = isChecked;
        } else {
          window.columnVisibilityDetailed[columnIndex] = isChecked;
        }

        // Use our custom function to toggle visibility
        if (window.toggleColumnVisibility) {
          window.toggleColumnVisibility(columnIndex, isChecked);
        } else {
          // Fallback if toggleColumnVisibility is not defined
          const tableSelector = report_type == 2 ? '.summary-view table' : '.detailed-view table';
          const table = $(tableSelector);

          if (report_type == 2) {
            // Summary view - simple column toggling
            table.find(`tr > *:nth-child(${columnIndex + 1})`).css('display', isChecked ? '' : 'none');
          } else {
            // Detailed view - handle complex structure
            const headerCell = table.find('thead tr th').eq(columnIndex);
            headerCell.css('display', isChecked ? '' : 'none');

            // For data cells, we need to handle rowspans
            if (columnIndex <= 3) {
              // Student info columns with rowspan
              table.find(`tbody tr td[rowspan]:nth-child(${columnIndex + 1})`).css('display', isChecked ? '' : 'none');
            } else {
              // Data columns
              table.find(`tbody tr`).each(function() {
                const row = $(this);
                if (row.find('td[rowspan]').length > 0) {
                  // First row of student group
                  row.find(`td:eq(${columnIndex})`).css('display', isChecked ? '' : 'none');
                } else if (row.find('th:contains("Total")').length === 0) {
                  // Fee type row
                  const adjustedIndex = columnIndex - 9; // Updated from 4 to 9 to account for new columns
                  row.find(`td:eq(${adjustedIndex})`).css('display', isChecked ? '' : 'none');
                } else if (row.find('th:contains("Total")').length > 0) {
                  // Total row - update colspan for the "Total" cell
                  const totalCell = row.find('th:contains("Total")');
                  if (totalCell.length && totalCell.attr('colspan')) {
                    // Store original colspan if not already stored
                    if (!totalCell.data('original-colspan')) {
                      totalCell.data('original-colspan', totalCell.attr('colspan'));
                    }

                    // Count visible columns in student info section (0-9)
                    let visibleStudentInfoCols = 0;
                    for (let i = 0; i <= 9; i++) {
                      if (window.columnVisibilityDetailed[i] !== false) {
                        visibleStudentInfoCols++;
                      }
                    }

                    // Update colspan
                    totalCell.attr('colspan', visibleStudentInfoCols);
                  }
                }
              });
            }
          }
        }
      });

    } catch (error) {
      console.error("Error initializing column toggle:", error);
    }
  }

  // Initialize column toggle when button is clicked
  // This is the main handler that will be used when the page first loads
  // It will be overridden when a filter is loaded
  $(document).on('click', '#column-toggle-btn', function(e) {
    e.preventDefault();
    e.stopPropagation();

    // Check if menu is already visible
    const existingMenu = $('.column-menu');
    if (existingMenu.is(':visible')) {
      // If menu is already visible, just hide it
      existingMenu.hide();
    } else {
      // If menu is not visible, initialize and show it
      initializeColumnToggle();

      // Make sure the menu is visible
      $('.column-menu').show();

      // Position the menu correctly
      const buttonPos = $(this).offset();
      $('.column-menu').css({
        position: 'absolute',
        top: buttonPos.top + $(this).outerHeight(),
        left: buttonPos.left,
        maxHeight: '300px',
        overflowY: 'auto',
        zIndex: 1000,
        backgroundColor: '#fff',
        border: '1px solid #ccc',
        borderRadius: '4px',
        boxShadow: '0 6px 12px rgba(0,0,0,.175)',
        padding: '5px 0'
      });
    }
  });

  function printProfile() {
    // Get the report type to determine which table to print
    var report_type = $('input[name="report_type"]:checked').val();

    // Create a new window for printing
    var printWindow = window.open('', '_blank');

    var schoolName = '<?php echo $this->settings->getSetting('school_name') ?>';
    var reportTitle = report_type == 2 ? 'Fees Summary Report' : 'Fees Detailed Report';

    // Write the HTML content to the new window
    printWindow.document.write('<!DOCTYPE html>');
    printWindow.document.write('<html>');
    printWindow.document.write('<head>');
    printWindow.document.write('<title>' + reportTitle + '</title>');
    printWindow.document.write('<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">');
    printWindow.document.write('<style>');
    printWindow.document.write('@page { size: auto; margin: 12mm; }');
    printWindow.document.write('body { font-family: "Poppins", sans-serif; color: #333; background: #fff; }');
    printWindow.document.write('h3, h4 { text-align: center; margin-bottom: 10px; }');
    printWindow.document.write('table { border-collapse: collapse; width: 100%; margin-top: 20px; font-size: 10pt; }');
    printWindow.document.write('th, td { border: 1px solid #ccc; padding: 8px; text-align: left; }');
    printWindow.document.write('th { background-color: #f4f7fc; font-weight: 600; }');
    printWindow.document.write('.panel-controls, .search-box, .btn-search, .input-search { display: none !important; }');
    printWindow.document.write('.print-header { text-align: center; margin-bottom: 20px; }');
    printWindow.document.write('.print-date { font-size: 12px; color: #666; margin-top: 5px; }');
    printWindow.document.write('.print-footer { position: fixed; bottom: 0; width: 100%; text-align: center; font-size: 10px; color: #666; }');
    printWindow.document.write('tr { page-break-inside: avoid; }');
    printWindow.document.write('@media print { .detailed-view table, .summary-view table { page-break-inside: auto; } }');
    printWindow.document.write('</style>');
    printWindow.document.write('</head>');
    printWindow.document.write('<body>');

    // Add header with date and time
    printWindow.document.write('<div class="print-header">');
    printWindow.document.write('<h3>' + schoolName + '</h3>');
    printWindow.document.write('<h4>' + reportTitle + '</h4>');
    printWindow.document.write('<div class="print-date">Generated on: ' + new Date().toLocaleString() + '</div>');
    printWindow.document.write('</div>');

    // Get the fee summary table
    var feeSummaryHtml = $('.fee_summary').html();
    printWindow.document.write('<div class="fee_summary">' + feeSummaryHtml + '</div>');

    // Get the main data table based on report type
    if (report_type == 2) {
      // Summary view
      var tableHtml = $('.summary-view').html();
      printWindow.document.write('<div class="summary-view">' + tableHtml + '</div>');
    } else {
      // Detailed view
      var tableHtml = $('.detailed-view').html();
      printWindow.document.write('<div class="detailed-view">' + tableHtml + '</div>');
    }

    // Add a script to ensure proper table layout for printing
    printWindow.document.write('<script>');
    printWindow.document.write('document.addEventListener("DOMContentLoaded", function() {');
    // Remove any display:none styles from visible columns
    printWindow.document.write('  var allTables = document.querySelectorAll("table");');
    printWindow.document.write('  for (var i = 0; i < allTables.length; i++) {');
    printWindow.document.write('    allTables[i].style.width = "100%";');
    printWindow.document.write('    var visibleCells = allTables[i].querySelectorAll("th:not([style*=\'display: none\']), td:not([style*=\'display: none\'])");');
    printWindow.document.write('    for (var j = 0; j < visibleCells.length; j++) {');
    printWindow.document.write('      visibleCells[j].style.display = "table-cell";');
    printWindow.document.write('    }');
    printWindow.document.write('  }');

    // Remove any hidden rows/columns
    printWindow.document.write('  var hiddenElements = document.querySelectorAll("[style*=\'display: none\']");');
    printWindow.document.write('  for (var i = 0; i < hiddenElements.length; i++) {');
    printWindow.document.write('    hiddenElements[i].parentNode.removeChild(hiddenElements[i]);');
    printWindow.document.write('  }');

    // Fix any empty cells to show N/A
    printWindow.document.write('  var allCells = document.querySelectorAll("td:empty");');
    printWindow.document.write('  for (var i = 0; i < allCells.length; i++) {');
    printWindow.document.write('    allCells[i].textContent = "N/A";');
    printWindow.document.write('  }');

    // Ensure all rows are visible
    printWindow.document.write('  var allRows = document.querySelectorAll("tr");');
    printWindow.document.write('  for (var i = 0; i < allRows.length; i++) {');
    printWindow.document.write('    allRows[i].style.display = "";');
    printWindow.document.write('  }');

    // Print the document after it's fully loaded
    printWindow.document.write('  setTimeout(function() { window.print(); }, 500);');
    printWindow.document.write('});');
    printWindow.document.write('<\/script>');

    printWindow.document.write('</body>');
    printWindow.document.write('</html>');

    printWindow.document.close();
    printWindow.focus();
  }

  function exportToExcel_daily(){
    var report_type = $('input[name="report_type"]:checked').val();
    var uri = 'data:application/vnd.ms-excel;base64,';
    var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/><style>td, th { mso-number-format: "\@"; } .number-column { mso-number-format: "#,##0.00"; } .text-column { mso-number-format: "\@"; } .integer-column { mso-number-format: "0"; }</style></head><body><table border="1">{table}</table></body></html>';
    var base64 = function(s) {
        return window.btoa(unescape(encodeURIComponent(s)))
    };

    var format = function(s, c) {
        return s.replace(/{(\w+)}/g, function(m, p) {
            return c[p];
        })
    };

    // Get school name and report title for header
    var schoolName = '<?php echo $this->settings->getSetting('school_name') ?>';
    var reportTitle = report_type == 2 ? 'Fees Summary Detail Report' : 'Fees Summary Detailed Report';
    var header = '<h3>' + schoolName + '</h3><h4>' + reportTitle + '</h4>';
    var currentDate = '<div style="text-align:center; font-size:12px; margin-bottom:10px;">Generated on: ' + new Date().toLocaleString() + '</div>';

    // Get the summary table first
    var summaryTable = $(".fee_summary table").clone();
    summaryTable.find('th, td').css('display', ''); // Make sure all cells are visible

    // Get the main data table based on report type
    var mainTable;
    if (report_type == 2) {
      // Summary view - get the table directly
      mainTable = $("#fee_student_summary_data").clone();
    } else {
      // Detailed view - get the table directly
      mainTable = $("#fee_student_detailed_data").clone();
    }

    // Process the main table to ensure all visible columns are included
    // Remove any style attributes that might hide columns
    mainTable.find('th, td').each(function() {
      // If the cell is not hidden with display:none style
      if (!$(this).is('[style*="display: none"]')) {
        // Remove any style attributes that might affect visibility
        $(this).removeAttr('style');
      }
    });

    // Format header cells for Excel - especially for numbering columns
    mainTable.find('thead th').each(function() {
      var headerText = $(this).text().trim();

      // Check if this is a numbering/index column
      var isNumberingColumn = headerText === '#' ||
                             headerText === 'S.No' ||
                             headerText === 'S.No.' ||
                             headerText === 'Sl.No' ||
                             headerText === 'Sl.No.' ||
                             headerText === 'No.' ||
                             headerText === 'No';

      if (isNumberingColumn) {
        $(this).addClass('integer-column').attr('style', "mso-number-format:'0';");
      }
    });

    // Remove any completely hidden columns
    var columnsToRemove = [];
    mainTable.find('thead th').each(function(index) {
      if ($(this).is('[style*="display: none"]')) {
        columnsToRemove.push(index);
      }
    });

    // Remove hidden columns from all rows (in reverse order to avoid index shifting)
    if (columnsToRemove.length > 0) {
      for (var i = columnsToRemove.length - 1; i >= 0; i--) {
        var columnIndex = columnsToRemove[i];
        mainTable.find('tr').each(function() {
          $(this).find('th, td').eq(columnIndex).remove();
        });
      }
    }

    // Format cells for Excel
    mainTable.find('tbody tr').each(function() {
      $(this).find('td').each(function(cellIndex) {
        var cellValue = $(this).text().trim();
        var headerText = mainTable.find('thead th').eq(cellIndex).text().trim();

        // Check if this is a numbering/index column (usually labeled as "#" or "S.No" or similar)
        var isNumberingColumn = headerText === '#' ||
                               headerText === 'S.No' ||
                               headerText === 'S.No.' ||
                               headerText === 'Sl.No' ||
                               headerText === 'Sl.No.' ||
                               headerText === 'No.' ||
                               headerText === 'No';

        // Check if this is a mobile/phone number column
        var isMobileColumn = headerText.includes('Number') ||
                            headerText.includes('Mobile') ||
                            headerText.includes('Phone');

        // For numbering columns, format as integers to prevent decimal places
        if (isNumberingColumn) {
          $(this).addClass('integer-column').attr('style', "mso-number-format:'0';");
        }
        // For mobile numbers, explicitly format as text
        else if (isMobileColumn) {
          $(this).addClass('text-column').attr('style', "mso-number-format:'\\@';");
        }
        // Check if this is a numeric column (amount) but NOT a mobile/phone number
        else if (cellValue.includes('₹') || (!isNaN(parseFloat(cellValue.replace(/,/g, ''))) && cellValue !== '')) {
          $(this).addClass('number-column');
        }
      });
    });

    // Combine all content
    var htmlContent = '<div>' + header + currentDate + '</div>';
    htmlContent += '<div style="margin-bottom:20px;">' + summaryTable.prop('outerHTML') + '</div>';
    // Add empty rows between tables to create a gap
    htmlContent += '<table border="0" style="margin-top:10px; margin-bottom:10px;"><tr><td>&nbsp;</td></tr><tr><td>&nbsp;</td></tr><tr><td>&nbsp;</td></tr></table>';
    htmlContent += '<div>' + mainTable.prop('outerHTML') + '</div>';

    var ctx = {
      worksheet: 'Spreadsheet',
      table: htmlContent
    };

    var link = document.createElement("a");
    var filename = report_type == 2 ? "fee_summary_report.xls" : "fee_detailed_report.xls";
    link.download = filename;
    link.href = uri + base64(format(template, ctx));
    link.click();
  }
</script>