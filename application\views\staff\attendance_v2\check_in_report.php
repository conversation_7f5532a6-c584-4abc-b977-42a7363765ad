<ul class="breadcrumb">
	<li><a href="<?php echo site_url('dashboard'); ?>">Dashboard</a></li>
	<li><a href="<?php echo site_url('staff/attendance'); ?>">Staff Attendance</a></li>
	<li>Check-in Report</li>
</ul>

<div class="col-md-12">
	<div class="card cd_border">
		<div class="card-header panel_heading_new_style_staff_border">
			<div class="row" style="margin: 0px;">
				<div class="d-flex justify-content-between" style="width:100%;">
					<h3 class="card-title panel_title_new_style_staff">
						<a class="back_anchor" href="<?php echo site_url('staff/attendance'); ?>">
							<span class="fa fa-arrow-left"></span>
						</a>
						Check-in Report
					</h3>
				</div>
			</div>
		</div>

		<div class="card-body pt-1">
			<div class="row" style="margin: 0px;">
				<div class="col-md-2 form-group">
					<label for="fromdateId" class="control-label">Attendance range</label>
					<div id="reportrange" class="dtrange" style="width: 100%">
						<span></span>
						<input type="hidden" id="from_date">
						<input type="hidden" id="to_date">
					</div>
				</div>

				<div class="col-md-2 form-group">
					<label class="control-label">Staff Type</label>
					<select class="form-control select" title="All" multiple name="selected_staff_type[]" id="selected_staff_type">
						<!-- <option value="all">All</option> -->
						<?php foreach ($staff_types as $key => $val) {
							echo "<option value='$key'>$val</option>";
						}
						?>
					</select>
				</div>

				<div class="col-md-2 form-group">
					<label class="control-label">Staff Status Type</label>
					<select class="form-control select" name="staff_status_type" id="staff_status_type" onChange="hideDataArea()">
						<!-- <option value="all">All</option> -->
							<option value='2'>Approved</option>
							<option value='4'>Resigned</option>
					</select>
            	</div>

				<!-- <div class="col-md-2 form-group">
					<label class="control-label">Attendance Type</label>
					<select class="form-control" name="attendence_type" id="attendence_type">
						<option value="all">Show All</option>
						<option value="check-in">Show Check-in staff</option>
						<option value="not check-in">Show not check-in staff</option>
					</select>
				</div> -->

				<div class="col-md-3 form-group pt-3">
					<button class="btn btn-primary mt-3" onclick="get_Staff_AttendanceData()">Get Report</button>
				</div>
			</div>

			<!-- <button class="mx-2 btn btn-primary mb-2 pull-right" onclick="exportToExcel()" style="display:none;" id="exportBTN">Excel</button> -->

			<div id="staff_data">
				<div id="staff-attendance-count" style="margin: 13px auto;">

				</div>
				<div id="staff-attendance-data">

				</div>
			</div>
		</div>
	</div>
</div>

<style>
  /* styles over here */
  .modal {
    overflow-y:auto;
  }
  
  .modal-dialog{
    margin: 4% auto;
    width: 80%;
  }
  
  .modal-header{
    position:relative;
  }

  .close{
    font-size: 34px;
    color: red;
    position: absolute;
    right: 10px;
  }

  tr:hover{
    background: #F1EFEF;
  }

  .row_background_color
  {
    background:#7f848780;
  }

  .dt-buttons{
    font-size: 14px;
    background:"red";
  }

  td>a>i{
		text-decoration: none;
		font-size: 16px;
		color: #191818;
		padding: 2px 5px;
	}

	.dataTables_wrapper .dt-buttons {
		float: right;
	}

	.dataTables_filter input {
		background-color: #f2f2f2;
		border: 1px solid #ccc;
		border-radius: 4px;
		margin-right: 5px;
	}
  
	.dataTables_wrapper .dataTables_filter {
		float: right;
		text-align: left;
		width: unset;
	}

	.dataTables_filter{
		position:absolute;
		right: 20%;
	}

	.dt-buttons{
		position:absolute;
		right:15px;
	}

	@media only screen and (min-width:1404px){
		.dataTables_filter{
			position:absolute;
			right: 15%;
		}	
	}

	@media only screen and (min-width:1734px){
		.dataTables_filter{
			position:absolute;
			right: 11%;
		}	
	}
</style>



<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript"
	src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>

<script>
	$(document).ready(function () {
		$("#reportrange").daterangepicker({
			ranges: {
				'Today': [moment(), moment()],
				'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
				'Last 7 Days': [moment().subtract(6, 'days'), moment()],
				'Last 30 Days': [moment().subtract(29, 'days'), moment()],
				// 'This Month': [moment().startOf('month'), moment()],
				// 'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
			},
			dateLimit: {
            'months': 1,
            'days': 0
            },
			opens: 'right',
			buttonClasses: ['btn btn-default'],
			applyClass: 'btn-small btn-primary',
			cancelClass: 'btn-small',
			format: 'DD-MM-YYYY',
			separator: ' to ',
			startDate: moment(),
			endDate: moment()
		}, function (start, end) {
			$('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
			$('#from_date').val(start.format('DD-MM-YYYY'));
			$('#to_date').val(end.format('DD-MM-YYYY'));
		});

		$("#reportrange span").html(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
		$('#from_date').val(moment().format('DD-MM-YYYY'));
		$('#to_date').val(moment().format('DD-MM-YYYY'));
	});

	function hideDataArea(){
		$("#staff-attendance-count").html("");
		$("#staff-attendance-data").html("");
	}

	function get_Staff_AttendanceData() {
		$("#staff-attendance-count").html('');
		$("#staff-attendance-data").html('<div class="text-center"><i class="fa fa-spinner fa-spin" style="font-size: 3rem;"></i></div>');
		var from_date = $("#from_date").val();
		var to_date = $("#to_date").val();
		var selected_staff_type = $("#selected_staff_type").val();
		
		var staff_status_type = $("#staff_status_type").val();

		$.ajax({
			url: '<?php echo site_url('staff/attendance/get_Staff_AttendanceData'); ?>',
			type: 'post',
			data: { 'from_date': from_date, 'to_date': to_date, 'selected_staff_type': selected_staff_type, 'staff_status_type':staff_status_type},
			success: function (data) {
				// $("#exportBTN").show();

				var data = JSON.parse(data);
				// console.log(data);
				var dates = data.dates;
				var attendance = data.attendance;
				var output = '<div class="d-flex justify-content-between align-items-center"><p><b style="color: red;"> </b><b style="color: red;"> </b></p><div class="d-flex align-items-center"><div class="form-group" id="range-input" style="width: 300px;"></div></div></div>';
				output += '<div class="table-responsive" id="report-container-summary">';
				output += '<table id="att-table-summary" class="table table-bordered">';
				output += construct_count_Header(dates, attendance);
				output += '</table>';
				output += '</div>';
				var html = '';
				// html += `
				// 	<div class="col-md-12 d-flex align-items-center justify-content-end">
				// 		<ul class="panel-controls" id="exporter">
				// 			<a style="margin-left:3px;" onclick="exportToExcel()" class="btn btn-info pull-right">Export</a>
				// 		</ul> 
				// 	</div>`;
				html += '<div class="table-responsive" id="report-container">';
				html += '<table id="att-table" class="table table-bordered">';
				html += constructHeader(dates);
				html += constructReport(dates, attendance);
				html += '</table>';
				html += '</div>';
				$("#staff-attendance-count").html(output);
				$("#staff-attendance-data").html(html);

				$('#staff_check_in_data').DataTable( {
				
        		});
				add_scroller('report-container-summary');
				
				add_scroller('report-container');

				exportAllDatatables();
				
				// const reportName=`staff_attendance_checkin_report_${new Date().toLocaleString('default', { month: 'short' })+" "+new Date().getDate()+" "+new Date().getFullYear()}_${new Date().getHours()+""+new Date().getMinutes()}`;

			// 	$('#att-table').DataTable( {
			// 	"language": {
			// 		"search": "",
			// 		"searchPlaceholder": "Enter Search..."
			// 	},
			// 	"lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
			// 			"pageLength": 10,
			// 	dom: 'lBfrtip',
			// 	buttons: [
			// 		{
			// 		extend: 'excelHtml5',
			// 		text: 'Excel',
			// 		filename: reportName,
			// 		className: 'btn btn-info'
			// 		},
			// 		{
			// 		extend: 'csvHtml5',
			// 		text: 'CSV',
			// 		filename: reportName,
			// 		className: 'btn btn-info'
			// 		},
			// 		{
			// 		extend: 'pdfHtml5',
			// 		text: 'PDF',
			// 		filename: reportName,
			// 		className: 'btn btn-info'
			// 		}
			// 	]
        	// });
			},
			error: function (err) {
				console.log(err);
			}
		});
	}

// 	function exportToExcel() {
// 		var htmls = "";
// 		var uri = 'data:application/vnd.ms-excel;base64,';
// 		var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
// 		var base64 = function(s) {
// 		return window.btoa(unescape(encodeURIComponent(s)))
// 		};

// 		var format = function(s, c) {
// 		return s.replace(/{(\w+)}/g, function(m, p) {
// 			return c[p];
// 		})
// 		};

// 		var mainTable = $("#report-container").html();
// 		htmls = mainTable;

// 		var ctx = {
// 		worksheet: 'Spreadsheet',
// 		table: htmls
// 		}


// 		var link = document.createElement("a");
// 		link.download = "export.xls";
// 		link.href = uri + base64(format(template, ctx));
// 		link.click();

//   }

	function exportAllDatatables() {

		// var table = $('#att-table-summary').DataTable();
		// var table2 = $('#myTable2').DataTable();
			const reportName1=`staff_attendance_checkin_summary_report_${new Date().toLocaleString('default', { month: 'short' })+" "+new Date().getDate()+" "+new Date().getFullYear()}_${new Date().getHours()+""+new Date().getMinutes()}`;

			const reportName=`staff_attendance_checkin_report_${new Date().toLocaleString('default', { month: 'short' })+" "+new Date().getDate()+" "+new Date().getFullYear()}_${new Date().getHours()+""+new Date().getMinutes()}`;


		var table = $('#att-table-summary').DataTable( {
				"language": {
					"search": "",
					"searchPlaceholder": "Enter Search..."
				},
				"lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
						"pageLength": 10,
				dom: 'lBfrtip',
				buttons: [
					{
					extend: 'excelHtml5',
					text: 'Excel',
					filename: reportName1,
					className: 'btn btn-info'
					},
					{
					extend: 'csvHtml5',
					text: 'CSV',
					filename: reportName1,
					className: 'btn btn-info'
					},
					{
					extend: 'pdfHtml5',
					text: 'PDF',
					filename: reportName1,
					className: 'btn btn-info'
					}
				]
        	});

			var table2 = $('#att-table').DataTable( {
				ordering:false,
				"language": {
					"search": "",
					"searchPlaceholder": "Enter Search..."
				},
				"lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
						"pageLength": 10,
				dom: 'lBfrtip',
				buttons: [
					{
					extend: 'excelHtml5',
					text: 'Excel',
					filename: reportName,
					className: 'btn btn-info'
					},
					{
					extend: 'csvHtml5',
					text: 'CSV',
					filename: reportName,
					className: 'btn btn-info'
					},
					{
					extend: 'pdfHtml5',
					text: 'PDF',
					filename: reportName,
					className: 'btn btn-info'
					}
				]
        	});

		//Trigger the button at index 2-1 (Wherever your export button in each table):
		table.button( '2-1' ).trigger();
		table2.button( '2-1' ).trigger();
	}

	function construct_count_Header(dates, attendance) {
		var html = '';
		html += '<thead>';
		html += '<tr>';
		html += '<th style="text-align:center;min-width: 100px;"></th>';
		for (var i in dates) {
			html += '<th style="text-align:center;min-width: 100px;">' + dates[i].format2 + '</th>';
		}
		html += '</tr>';
		html += '</thead>';
		html += '<tbody>';

		///////////////////////////////////////////////
		html += '<tr  style="background: lightgreen;">';
		html += '<th style="text-align: left;min-width: 100px;">Checked-in</th>';

		let checked_in_count = 0;

		for (var k in dates) {
			checked_in_count = 0;

			for (var i in attendance) {
				if (attendance[i].attendance.hasOwnProperty(dates[k].format2)) {
					if (attendance[i].attendance[dates[k].format2].first_check_in_time && attendance[i].staff_name) {
						checked_in_count++
					}
				}
			}

			html += `<td style="text-align: center;min-width: 80px;">${checked_in_count} </td>`;
		}

		html += '</tr>';
		//////////////////////////////////////////////
		// html += '<tr  style="background: lightgreen;">';
		// html += '<th style="text-align: left;min-width: 100px;">Present</th>';

		// let check_in_present = 0;

		// for (var k in dates) {
		// 	check_in_present = 0;

		// 	for (var i in attendance) {
		// 		if (dates[k].format2 in attendance[i]) {
		// 			var att_status = attendance[i][dates[k].format2].status;
		// 			var att_status1 = att_status.split(' ');
		// 			// if (attendance[i][dates[k].format2].first_check_in_time && att_status != 'H' && attendance[i].staff_name) {
		// 			// 	check_in_count = check_in_count + 1;
		// 			// }

		// 			if (att_status === 'P') {
		// 				check_in_present++;
		// 			}


		// 		}
		// 	}

		// 	// const isTodaysReport=new Date(dates[k].format2).toDateString()==new Date().toDateString();
		// 	// if(!isTodaysReport){
		// 	html += `<td style="text-align: center;min-width: 80px;">${check_in_present} </td>`;
		// 	// }else{
		// 	// html += `<td>-</td>`;
		// 	// }
		// }

		// html += '</tr>';

		// html += '<tr  style="background: lightyellow;">';
		// html += '<th style="text-align: left;min-width: 100px;">Half Day</th>';

		// let check_in_hald_day = 0;
		// for (var k in dates) {
		// 	check_in_hald_day = 0;

		// 	for (var i in attendance) {
		// 		if (dates[k].format2 in attendance[i]) {
		// 			var att_status = attendance[i][dates[k].format2].status;
		// 			var att_status1 = att_status.split(' ');

		// 			if (att_status === 'HD') {
		// 				check_in_hald_day++;
		// 			}

		// 		}
		// 	}

		// 	// const isTodaysReport=new Date(dates[k].format2).toDateString()==new Date().toDateString();
		// 	// if(!isTodaysReport){
		// 	html += `<td style="text-align: center;min-width: 80px;">${check_in_hald_day} </td>`;
		// 	// }else{
		// 	// html += `<td>-</td>`;
		// 	// }
		// }

		// html += '</tr>';

		html += '<tr  style="background: lightpink;">';
		html += '<th style="text-align: left;min-width: 100px;">Not checked-in (Leave Not Applied)</th>';

		let not_checked_in_leave_not_applied = 0;
		for (var k in dates) {
			not_checked_in_leave_not_applied = 0;

			for (var i in attendance) {
				if (attendance[i].attendance.hasOwnProperty(dates[k].format2)) {
					if (!attendance[i].attendance[dates[k].format2].first_check_in_time && !+attendance[i].attendance[dates[k].format2].on_leave && attendance[i].staff_name) {
						not_checked_in_leave_not_applied++;
					}
				}
			}

			// const isTodaysReport=new Date(dates[k].format2).toDateString()==new Date().toDateString();
			// if(!isTodaysReport){
			html += `<td style="text-align: center;min-width: 80px;">${not_checked_in_leave_not_applied} </td>`;
			// }else{
			// html += `<td>-</td>`;
			// }
		}

		html += '</tr>';

		html += '<tr  style="background: lightpink;">';
		html += '<th style="text-align: left;min-width: 100px;">Not checked-in (Leave Applied)</th>';
		let not_checked_in_leave_applied = 0;
		for (var k in dates) {
			not_checked_in_leave_applied = 0;
			for (var i in attendance) {
				if (attendance[i].attendance.hasOwnProperty(dates[k].format2)) {
					// var att_status = attendance[i][dates[k].format2].status;
					// if (att_status === "CL" || att_status === "LOP" || att_status === "AL" || att_status === "EL" || att_status === "PL" || att_status === "SL" || att_status === "RH" || att_status === "CO" || att_status === "OOD" || att_status === "COMML" || att_status === "EXL" || att_status === "MRL" || att_status === "ML" || att_status === "PaL") {
					// 	others_count++
					// }
					if (!attendance[i].attendance[dates[k].format2].first_check_in_time && +attendance[i].attendance[dates[k].format2].on_leave && attendance[i].staff_name) {
						not_checked_in_leave_applied++;
					}
				}
			}

			// const isTodaysReport = new Date(dates[k].format2).toDateString() == new Date().toDateString();
			// if (!isTodaysReport) {
			html += `<td style="text-align: center;min-width: 80px;">${not_checked_in_leave_applied}</td>`;
			// } else {
			// html += `<td>-</td>`;
			// }
		}
		html += '</tr>';

		// html += '<tr style="background: lightpink;">';
		// html += '<th style="text-align: left;min-width: 100px;">Absent</th>';
		// let leave_aplied = '';
		// let leave_aplied_approved = 0;
		// let leave_aplied_not_approved = 0;
		// let leave_not_aplied_count=0;
		// for (var k in dates) {
		// 	leave_aplied = '';
		// 	leave_aplied_approved= 0
		// 	leave_aplied_not_approved= 0;
		// 	leave_not_aplied_count=0;

		// 	for (var i in attendance) {
		// 		if (dates[k].format2 in attendance[i]) {
		// 			var att_status = attendance[i][dates[k].format2].status;
		// 			var att_status1= att_status.split(' ');
		// 			var approval= '';
		// 			if( att_status1[1] != undefined ) {
		// 				approval= att_status1[1].split('__');
		// 			}
		// 			if (attendance[i][dates[k].format2].first_check_in_time == '' && att_status1[1] != undefined && att_status1[0] == 'AB' && attendance[i].staff_name != null && (approval[1]== '1' || approval[1] == '2') ) {
		// 				leave_aplied_approved = leave_aplied_approved + 1;
		// 			}
		// 			if (attendance[i][dates[k].format2].first_check_in_time == '' && att_status1[1] != undefined && att_status1[0] == 'AB' && attendance[i].staff_name != null && (approval[1]!= '1' && approval[1] != '2') ) {
		// 				leave_aplied_not_approved = leave_aplied_not_approved + 1;
		// 			}

		// 			if (attendance[i][dates[k].format2].first_check_in_time == '' && att_status1[1] == undefined && att_status1[0] == 'AB' && attendance[i].staff_name != null) {
		// 				leave_not_aplied_count++;
		// 			}
		// 		}
		// 	}

		// 	if(leave_aplied_approved && leave_aplied_not_approved) {
		// 		leave_aplied= `${leave_aplied_approved} <small>(Approved)</small><br>${leave_aplied_not_approved} <small>(Not Approved)</small>`;
		// 	} else if(leave_aplied_approved) {
		// 		leave_aplied= `${leave_aplied_approved} <small>(Approved)</small>`;
		// 	} else if(leave_aplied_not_approved) {
		// 		leave_aplied= `${leave_aplied_not_approved}  <small>(Not Approved)</small>`;
		// 	} else {
		// 		leave_aplied= 0;
		// 	}

		// 	const isTodaysReport=new Date(dates[k].format2).toDateString()==new Date().toDateString();
		// 		if(!isTodaysReport){
		// 			html += `<td style="text-align: center;min-width: 120px;"> ${leave_aplied_approved+leave_aplied_not_approved+leave_not_aplied_count} (${leave_aplied_approved+leave_aplied_not_approved} Leave Applied + ${leave_not_aplied_count} Leave Not Applied ) </td>`;
		// 		}else{
		// 			html += `<td>-</td>`;
		// 		}
		// }
		// // html += '</tr>';

		// html += '</tr>';
		// html += '<tr  style="background: lightpink;">';
		// html += '<th style="text-align: left;min-width: 100px;">Absent Leave Not Applied</th>';
		// var leave_not_aplied = 0;
		// for (var k in dates) {
		// 	leave_not_aplied = 0;
		// 	for (var i in attendance) {
		// 		if (dates[k].format2 in attendance[i]) {
		// 			var att_status = attendance[i][dates[k].format2].status;
		// 			var att_status1= att_status.split(' ');
		// 			if (attendance[i][dates[k].format2].first_check_in_time == '' && att_status1[1] == undefined && att_status1[0] == 'AB' && attendance[i].staff_name != null) {
		// 				leave_not_aplied = leave_not_aplied + 1;
		// 			}
		// 		}
		// 	}
		// 	html += '<td style="text-align: center;min-width: 80px;">' + leave_not_aplied + '</td>';
		// }
		// html += '</tr>';

		// html += '<tr  style="background: lightgreen;">';
		// html += '<th style="text-align: left;min-width: 100px;">Holiday(s)</th>';
		// let holiday;
		// for (var k in dates) {
		// 	holiday = 0;
		// 	for (var i in attendance) {
		// 		if (dates[k].format2 in attendance[i]) {
		// 			var att_status = attendance[i][dates[k].format2].status;
		// 			if (att_status == 'H') {
		// 				holiday++;
		// 			}
		// 		}
		// 	}

		// 	// const isTodaysReport = new Date(dates[k].format2).toDateString() == new Date().toDateString();
		// 	// if (!isTodaysReport) {
		// 	html += `<td style="text-align: center;min-width: 80px;">${holiday}</td>`;
		// 	// } else {
		// 	// html += `<td>-</td>`;
		// 	// }
		// }
		// html += '</tr>';

		// html += '<tr  style="background: ;">';
		// html += '<th style="text-align: left;min-width: 100px;">Week Off</th>';
		// let week_off_count = 0;
		// for (var k in dates) {
		// 	week_off_count = 0;
		// 	for (var i in attendance) {
		// 		if (dates[k].format2 in attendance[i]) {
		// 			var att_status = attendance[i][dates[k].format2].status;
		// 			if (att_status === 'WO') {
		// 				week_off_count++
		// 			}
		// 		}
		// 	}

		// 	// const isTodaysReport = new Date(dates[k].format2).toDateString() == new Date().toDateString();
		// 	// if (!isTodaysReport) {
		// 	html += `<td style="text-align: center;min-width: 80px;">${week_off_count}</td>`;
		// 	// } else {
		// 	// html += `<td>-</td>`;
		// 	// }
		// }
		// html += '</tr>';

		html += '<tr style="background: gray;">';
		html += '<th style="text-align: left;min-width: 100px;">Reporting Staff</th>';

		var staff_count = 0;
		for (var k in dates) {
			staff_count = 0;
			for (var i in attendance) {
				if (attendance[i].attendance.hasOwnProperty(dates[k].format2)) {

					if (attendance[i].staff_name != null) {
						staff_count = staff_count + 1;
					}
				}
			}
			html += `<td style="text-align: center;min-width: 80px;">${staff_count}</td>`;
		}
		// alert(d);

		html += '</tr>';

		html += '</tbody>';

		return html;
	}

	function constructHeader(dates) {
		var html = '';
		html += `
		<thead>
			<tr>
				<th data-toggle="tooltip" onclick="clearStatusFilters(${dates?.length})" data-original-title="Clear Filters" style="width:5%;cursor: pointer;color: #e04b4a;vertical-align: middle;font-size: 1.5rem;"><i class="fa fa-times"></i>
				</th>

				<th>
					<input onkeyup="filterStaffList(${dates?.length})" type="text" class="form-control" style="margin-top:18px;" id="staffId-filter" placeholder="Search"/>
				</th>

				<th>
					<input onkeyup="filterStaffList(${dates?.length})" type="text" class="form-control" style="margin-top:18px;" id="staff-filter" placeholder="Search"/>
				</th>

				<th>
					<input onkeyup="filterStaffList(${dates?.length})" type="text" class="form-control" style="margin-top:18px;" id="email-filter" placeholder="Search"/>
				</th>

				<th>
					<input onkeyup="filterStaffList(${dates?.length})" type="text" class="form-control" style="margin-top:18px;" id="staffType-filter" placeholder="Search"/>
				</th>

				<th>
					<input onkeyup="filterStaffList(${dates?.length})" type="text" class="form-control" style="margin-top:18px;" id="dept-filter" placeholder="Search"/>
				</th>
				
				`

		for (let i = 1; i <= dates?.length; i++) {
			html += `<th>
						<select onchange="filterStaffList(${dates?.length})" class="form-control" style="margin-top:18px;" id="date${i}-filter">
							<option value="">All</option>
							<option value="taken">Checked-in</option>
							<option value="not_checked_in_al">Not Checked-in (LA)</option>
							<option value="not_checked_in_nal">Not Checked-in (LNA)</option>
							<option value="holiday">Holiday</option>
							<option value="week_off">Week-Off</option>
						</select>
					</th>

					<th>
						<select onchange="filterStaffList(${dates?.length})" class="form-control" style="margin-top:18px;" id="status${i}-filter">
							<option value="">All</option>
							<option value="P">P</option>
							<option value="OOD">OOD</option>
							<option value="HD">HD</option>
							<option value="AB">AB</option>
							<option value="H">H</option>
							<option value="WO">WO</option>
						</select>
					</th>

					<th>
					</th>
					`;
		}

		html += '</tr>';

		html += '<tr>';
		html += '<th >#</th>';
		html += '<th style="min-width: 150px;">Staff Id</th>';
		html += '<th style="min-width: 150px;">Staff Name</th>';
		html += '<th style="min-width: 150px;">Email</th>';
		html += '<th style="min-width: 150px;">Staff Type</th>';
		html += '<th style="min-width: 150px;">Department</th>';
		for (var i in dates) {
			html += '<th style="text-align:center;min-width: 100px;">' + dates[i].format2 + '</th>';
			html += '<th style="text-align:center;min-width: 100px;">Status</th>';
			html += '<th style="text-align:center;min-width: 100px;">Remarks</th>';
		}
		html += '</tr>';
		html += '</thead>';
		return html;
	}

	function clearStatusFilters(dateslength) {
		// $(".datesLength")
		$(`#staffId-filter`).val('');
		$(`#staff-filter`).val('');
		$(`#email-filter`).val('');
		$(`#staffType-filter`).val('');
		$(`#dept-filter`).val('');

		for (let i = 1; i <= dateslength; i++) {
			$(`#date${i}-filter`).val('');
			$(`#status${i}-filter`).val('');
		}
		filterStaffList(dateslength);
	}

	function filterStaffList(dateslength) {
		const staffIdFilter = $(`#staffId-filter`).val().toLowerCase();
		const staffFilter = $(`#staff-filter`).val().toLowerCase();
		const emailFilter = $(`#email-filter`).val().toLowerCase();
		const staffTypeFilter = $(`#staffType-filter`).val().toLowerCase();
		const deptFilter = $(`#dept-filter`).val().toLowerCase();
		var find_string = '';

		if (staffFilter) {
			find_string += `[data-staffName*=${staffFilter}]`;
		}

		if (staffIdFilter) {
			find_string += `[data-staffId*=${staffIdFilter}]`;
		}

		if (emailFilter) {
			find_string += `[data-email*=${emailFilter}]`;
		}

		if (staffTypeFilter) {
			find_string += `[data-staffType*=${staffTypeFilter}]`;
		}

		if (deptFilter) {
			find_string += `[data-dept*=${deptFilter}]`;
		}

		for (let i = 1; i <= dateslength; i++) {
			const dateFilter = String($(`#date${i}-filter`).val()).toLowerCase();
			if (dateFilter) {
				find_string += `[data-date${i}*=${dateFilter}]`;
			}
			
			const statusFilter = String($(`#status${i}-filter`).val()).toUpperCase();
			if (statusFilter) {
				find_string += `[data-status${i}*=${statusFilter}]`;
			}
		}

		if (find_string === '') {
			$(".staff-filters").show();
		} else {
			$(".staff-filters").hide();
			$(`.staff-filters${find_string}`).show();
		}

	}

	function constructReport(dates, attendance) {
		// console.log(attendance);
		var j = 1;
		var html = '<tbody>';

		for (var i in attendance) {
			if (attendance[i].staff_name != null) {

				html += `<tr
				data-staffId="${attendance[i].employee_code.toLowerCase()}" 
				data-staffName="${attendance[i].staff_name.toLowerCase()}" 
				data-email="${attendance[i]?.email?.toLowerCase() || "na"}" 
				data-staffType="${attendance[i]?.staff_type?.toLowerCase() || "na"}" 
				data-dept="${attendance[i]?.staff_department?.toLowerCase() || "na"}" 
				`;

				for (var k in dates) {
					if (attendance[i].attendance.hasOwnProperty(dates[k].format2)) {
						html += `
						data-date${++k}="${String(attendance[i].attendance[dates[--k]?.format2]?.first_check_in_time).toLowerCase() && "taken" || attendance[i].attendance[dates[k].format2].status == "H" && "holiday" || attendance[i].attendance[dates[k].format2].shift_type == 3 && "holiday" || attendance[i].attendance[dates[k].format2].status == "WO" && "week_off" || attendance[i].attendance[dates[k].format2].shift_type == 2 && "week_off" || +attendance[i].attendance[dates[k].format2].on_leave && "not_checked_in_al".toLowerCase() || "not_checked_in_nal"}"`;

						const att_status = attendance[i].attendance[dates[k].format2].status;
						html += `data-status${++k}="${att_status}"`;
					}
				}

				html += ` class="staff-filters">`;
				html += '<td>' + (j++) + '</td>';

				html += '<td style="">' + attendance[i].employee_code + '</td>';
				html += `<td style=""><a target="_blank" href="<?php echo site_url() ?>${'staff/attendance/indv_staff_report/'}${attendance[i].staff_id}"> ${attendance[i].staff_name} </a> </td>`;
				if(attendance[i].email)
					html += `<td style=""><a href = "mailto:${attendance[i].email}">${attendance[i].email}</a></td>`;
				else
					html += `<td style="">NA</td>`;

				html += `<td style="">${attendance[i]?.staff_type || "NA"}</td>`;
				html += `<td style="">${attendance[i]?.staff_department || "NA"}</td>`;

				for (var k in dates) {
					if(attendance[i].attendance.hasOwnProperty(dates[k].format2)){
						var att_status = attendance[i].attendance[dates[k].format2].status;
						var is_manually_changed = attendance[i].attendance[dates[k].format2].is_manually_changed;
						// var att_status1 = att_status?.split(' ');

						var check_in_time = `<p style="color:green;"><b> ${attendance[i].attendance[dates[k].format2].first_check_in_time} ${+attendance[i].attendance[dates[k].format2].is_late && "<span style='color:red'>(L) </span>" || ""}</b>
						</p>`;

						if (att_status == 'H') {
							check_in_time = '<p style="color:red;">Holiday</p>';
						}else if (att_status == 'WO') {
							check_in_time = '<p style="color:red;">Week OFF</p>';
						}else if (!attendance[i].attendance[dates[k].format2].first_check_in_time) {
							// 2 -> week off
							// 3 -> holiday
							if(attendance[i].attendance[dates[k].format2].shift_type==2){
								check_in_time = `<p style="color:red;">Week OFF</p>`;
							}else if(attendance[i].attendance[dates[k].format2].shift_type==3){
								check_in_time = `<p style="color:red;">Holiday</p>`;
							}else{
								check_in_time = `<p style="color:${+attendance[i].attendance[dates[k].format2].on_leave && "green;font-weight: 600" || "red"}" style="color:red;">Not checked in ${+attendance[i].attendance[dates[k].format2].on_leave && "<span>(LA) </span>" || "<span>(LNA) </span>"}</p>`;
							}
						}
						html += '<td style="text-align:center;min-width: 80px;max-width: 205px;">' + check_in_time + '</td>';
						html += '<td style="text-align:center;min-width: 80px;max-width: 205px;">' + `${att_status || "-"} ${+is_manually_changed===1 ? "<br> (Overridden)" : ""}`+ '</td>';
						html += `<td style="text-align:center;min-width: 80px;max-width: 205px;"><small>${+attendance[i].attendance[dates[k].format2].is_late && attendance[i].attendance[dates[k].format2].reason!="Auto Checkout" && attendance[i].attendance[dates[k].format2].reason || "-"}</small></td>`;

					} else {
						html += '<td style="color: grey;text-align:center;">Not found</td>';
						html += '<td style="text-align:center;min-width: 80px;max-width: 205px;">-</td>';
						html += '<td style="color: grey;text-align:center;">-</td>';
					}
				}
				html += '</tr>';
			}
		}

		html += '</tbody>';
		return html;

	}

	// function exportToExcel() {
	// 	var htmls = "";
	// 	var uri = 'data:application/vnd.ms-excel;base64,';
	// 	var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>';
	// 	var base64 = function (s) {
	// 		return window.btoa(unescape(encodeURIComponent(s)))
	// 	};

	// 	var format = function (s, c) {
	// 		return s.replace(/{(\w+)}/g, function (m, p) {
	// 			return c[p];
	// 		})
	// 	};

	// 	var mainTable = $("#staff_data").html();

	// 	htmls = mainTable;

	// 	var ctx = {
	// 		worksheet: 'Spreadsheet',
	// 		table: htmls
	// 	}

	// 	var link = document.createElement("a");
	// 	link.download = `staff_attendance_checkin_report_${new Date().toLocaleString('default', { month: 'short' })+" "+new Date().getDate()+" "+new Date().getFullYear()}_${new Date().getHours()+""+new Date().getMinutes()}.xls`;
	// 	link.href = uri + base64(format(template, ctx));
	// 	link.click();

	// }


</script>