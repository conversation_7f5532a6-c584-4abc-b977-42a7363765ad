<?php include(APPPATH . 'views/feesv2/reports/components/report_header.php'); ?>
<style>
    /* Custom hover styling for Bootstrap-Select dropdown options */
    .bootstrap-select .dropdown-menu li a:hover {
        background-color: #f5f5f5 !important;
        color: #000000;
    }

    .bootstrap-select .dropdown-menu li a:focus {
        background-color: #f5f5f5 !important;
        color: #000000;
    }

    .bootstrap-select .dropdown-menu li.selected a {
        background-color: #f5f5f5 !important;
        color: #000000;
    }

    .bootstrap-select .dropdown-menu li.selected a:hover {
        background-color: #f5f5f5 !important;
        color: #000000;
    }
                /* Override thin scrollbars for this specific table */
                .componenent_wise_report::-webkit-scrollbar {
                    width: 12px !important;
                    height: 12px !important;
                }
                
                .componenent_wise_report::-webkit-scrollbar-track {
                    background: #f1f1f1 !important;
                    border-radius: 6px !important;
                }
                
                .componenent_wise_report::-webkit-scrollbar-thumb {
                    background: #888 !important;
                    border-radius: 6px !important;
                    border: 2px solid #f1f1f1 !important;
                }
                
                .componenent_wise_report::-webkit-scrollbar-thumb:hover {
                    background: #555 !important;
                }
                
                /* Firefox scrollbar styling */
                .componenent_wise_report {
                    scrollbar-width: auto !important;
                    scrollbar-color: #888 #f1f1f1 !important;
                }

                /* Sticky table header for vertical scroll */
                .componenent_wise_report table thead th {
                    position: sticky !important;
                    top: 0 !important;
                    background-color: #EFECFD !important;
                    z-index: 10 !important;
                }

                /* Ensure proper stacking context */
                .componenent_wise_report {
                    position: relative !important;
                }

                /* Spacing adjustments for better layout */
                .summary_data_report {
                    margin-bottom: 25px !important;
                }

                .panel-controls {
                    margin-top: 15px !important;
                    margin-bottom: 15px !important;
                    padding: 15px 0 !important;
                }

                .componenent_wise_report {
                    margin-top: 15px !important;
                }

</style>
<div class="card-body">
            <form id="component_wise_report">
                <div class="col-md-12" style="padding-left: 0px;">
                    <div class="row" style="margin: 0px">
                        <div class="col-md-3 form-group" id="blueprintSelect">
                            <p style="margin-bottom: 2px;">Select Fee Type <font color="red">*</font>
                            </p>
                            <select class="form-control changeFeeType selectpicker" id="fee_type" name="fee_type[]" multiple title="Select Fee Types" data-live-search="true">
                                <!-- <option value="">Select Blueprint</option> -->
                                <?php foreach ($fee_blueprints as $key => $val) { ?>
                                    <option value="<?= $val->id ?>"><?php echo $val->name ?></option>
                                <?php } ?>
                                <option value="application">Applications</option>
                            </select>
                        </div>

                        <div class="col-md-3 form-group" id="component">
                            <p style="margin-bottom: 2px;">Select Component <font color="red">*</font>
                            </p>
                            <select id="component_name" name="component_name[]" multiple title="Select Component" class="form-control select" data-live-search="true" data-actions-box="true">
                                <option value="">Please Select Blueprint First</option>
                            </select>
                        </div>

                        <div class="col-md-3 form-group">
                            <p style="margin-bottom: 2px;">Class</p>
                            <?php
                            $array = array();
                            foreach (
                                isset(
                                    $classes
                                ) ? $classes : array() as $key => $class
                            ) {
                                $array[$class->classId] = $class->className;
                            }
                            echo form_dropdown("class_name[]", $array, set_value("class_name", isset(
                                $clsId
                            ) ? $clsId : ''), "id='classId' multiple title='All' class='form-control classId select '");
                            ?>
                        </div>

                        <div class="col-md-3 form-group">
                            <p style="margin-bottom: 2px;">&nbsp;</p>
                            <input type="button" onclick="get_component_wise_report()" name="search" id="search" class="btn btn-primary" value="Get Report" >
                        </div>

                        <!-- <div class="col-md-3 form-group">
                            <p style="margin-bottom: 2px;">Class/Section</p>
                            <?php
                            $array = array();
                            foreach (
                                isset(
                                    $classSectionList
                                ) ? $classSectionList : array() as $key => $cl
                            ) {
                                $array[$cl->id] = $cl->class_name . $cl->section_name;
                            }
                            echo form_dropdown("classSectionId", $array, '', "id='classSectionId' multiple title='All' class='form-control select'");
                            ?>
                        </div> -->

                    </div>
                </div>
            </form>

            <div class="text-center mt-2">
                <div style="display: none;" class="progress" id="progress">
                    <div id="progress-ind" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" ariavaluenow="50" aria-valuemin="0" aria-valuemax="100" style="width: 50%"></div>
                </div>
            </div>



            <div class="col-12 text-center loading-icon" style="display: none;">
                <i class="fa fa-spinner fa-spin" style="font-size: 40px;margin: 20px 0;"></i>
            </div>

            <div id="printArea">
                <div id="print_visible" style="display: none; text-align: center;">
                    <h3 style="margin-bottom: 0.25rem; font-size: 1.5rem; font-family: 'Poppins', serif;">
                        <?php echo htmlspecialchars($this->settings->getSetting('school_name')); ?>
                    </h3>
                    <h4 style="margin-top: 0.25rem;font-size: 1.3rem;font-weight: bold;letter-spacing: 1px;color: #222;text-transform: uppercase;border-top: 1px solid #444;border-bottom: 1px solid #444;padding: 0.5rem 0;font-family: 'Poppins', serif;">
                        Fees Summary Report
                    </h4>
                </div>

                <div id="no-data-message" style="display:none; color: red; text-align: center; font-size: 16px; margin: 20px 0;">
                    No Data Found
                </div>



                <div class="summary_data_report mt-2">

                </div>
                <div class="panel-controls" id="exportButtons" style="display: none;">
                    <div class="d-flex justify-content-end align-items-center gap-2">
                        <div class="search-box position-relative">
                            <i class="bi bi-search"></i>
                            <input type="text" class="input-search" id="table-search" placeholder="Enter Search...">
                        </div>
                        <button onclick="printProfile()" class="btn btn-outline-primary" id="expbtns" style="margin-bottom: 15px; margin-left: 4px;"><?= $this->load->view('svg_icons/print.svg', [], true); ?> Print</button>
                        <button onclick="exportToExcel_daily()" class="btn btn-outline-primary" id="expbtns" style="margin-bottom: 15px;"><?= $this->load->view('svg_icons/excel_2.svg', [], true); ?> Excel</button>
                    </div>
                </div>
                <div class="componenent_wise_report" style="overflow-x: auto; max-height: 500px; overflow-y: auto;">

                </div>
            </div>
        </div>

    </div>
</div>
</div>

<script type="text/javascript">
    let componentNames = {

    }

    /*
    function changeInstallment() {
        var installment_type = $('#installment_type').val();
        $.ajax({
            url: '<?php echo site_url('feesv2/reports/installment_type_installment') ?>',
            type: 'post',
            data: {
                'installment_type': installment_type
            },
            success: function(data) {
                var data = JSON.parse(data);
                var output = '<option value="">Select Installments</option>';
                for (var i = 0; i < data.length; i++) {
                    output += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
                }
                $('#installmentId').html(output);
                $('#installment').show();
                $('#installmentId').selectpicker();
            }
        });
    }
    $('#installment_type').on('change', function() {
        changeInstallment();
    });
    */
    let msg = `
  <div style="color:red;text-align:center;
    color: black;
    border: 2px solid #fffafa;
    text-align: center;
    border-radius: 6px;
    position: relative;
    margin-left: 14px;
    padding: 10px;
    font-size: 14px;
    margin-top: 14px;
    background: #ebf3ff;">
      No Data to show
    </div>
  `;

    $("#fee_type").change(() => {
        let fee_type = $('#fee_type').val();;
        if (fee_type) {
            $.ajax({
                url: '<?php echo site_url('feesv2/reports_v2/get_blueprint_components1'); ?>',
                type: "POST",
                data: {
                    "fb_id": fee_type
                },
                success: function(data) {
                    data = $.parseJSON(data)
                    if (data.length) {
                        let options = ``
                        data.forEach((data, i) => {
                            options += `<option value="${data.id}">${data.name}</option>`
                        })
                        $("#component_name").html(options);
                        $('#component_name').selectpicker('refresh');
                    } else {
                        $("#component_name").html(`<option value="">Component(s) Not Available</option>`);
                    }
                }
            })
        }
    })
    var cohortStudentIds = [];
    var completed = 0;
    var total_students = 0;
    // var totalFeeAssingedStudentCount = 0;
    // var aluminiCount = 0;
    // var summaryData = [];
    function get_component_wise_report() {
        $('#table-search').val('');
        $(".summary_data_report").hide();
        completed = 0;
        total_students = 0;

        // Disable the button and show loading state
        $('#search').prop('disabled', true).val('Please wait...');

        let fee_type = $('#fee_type').val();
        // let installment_type = $('#installment_type').val();
        let component_id = $("#component_name").val();

        let validationErrors = [];

        if (!fee_type || fee_type === '') {
            validationErrors.push('Select Fee Type');
        }

        if ($('#component').is(':visible') && (!component_id || component_id.length === 0)) {
            validationErrors.push('Select Component');
        }

        if (validationErrors.length > 0) {
            let errorMessage = '';
            if (validationErrors.length === 1) {
                errorMessage = 'Please ' + validationErrors[0];
            } else {
                errorMessage = 'Please select the following fields:\n• ' + validationErrors.join('\n• ');
            }

            new PNotify({
                title: 'Required Fields Missing',
                text: errorMessage,
                type: 'error',
                styling: 'bootstrap3',
                delay: 5000,
                addclass: 'custom-validation-notification',
                width: '350px',
                icon: 'fa fa-exclamation-triangle',
                hide: true,
                closer: true,
                sticker: false,
                opacity: 0.95,
                shadow: true,
                cornerclass: 'ui-pnotify-sharp'
            });

            // Hide all tables and export buttons if validation fails
            $('#fee_component_data').hide();
            $('#print_summary').hide();
            $(".summary_data_report").hide();
            $(".componenent_wise_report").hide();
            $("#exportButtons").hide();
            $("#no-data-message").hide(); // Optionally, you can show a message if you want

            // Re-enable button on validation error
            $('#search').prop('disabled', false).val('Get Report');
            return false;
        }

        $(".loading-icon").show();
        $(".componenent_wise_report").hide()

        let clsId = $('#classId').val();
        let classSectionId = $('#classSectionId').val();
        let admission_type = $('#admission_type').val();
        let admission_status = $('#admission_status').val();

        $.ajax({
            url: '<?php echo site_url('feesv2/reports_v2/component_wise_getStudentsForSummary'); ?>',
            type: "POST",
            data: {
                "fee_type": fee_type,
                "clsId": clsId,
                "classSectionId": classSectionId,
                "component_id": component_id,
                "admission_type": admission_type,
                "admission_status": admission_status,
            },
            success: function(data) {

                var cohort_student_ids = JSON.parse(data);
                if (cohort_student_ids.length == 0) {
                    // Hide all tables and export buttons
                    $('#fee_component_data').hide();
                    $('#print_summary').hide();
                    $(".summary_data_report").hide();
                    $(".componenent_wise_report").hide();
                    $("#exportButtons").hide();

                    // Show the no data message
                    $("#no-data-message").show();

                    $(".loading-icon").hide();
                    $('#search').prop('disabled', false).val('Get Report');
                    $("#progress").hide();
                    return;
                }
                // When data is present
                $("#no-data-message").hide();
                $('#fee_component_data').show();
                $('#print_summary').show();
                $(".summary_data_report").show();
                $(".componenent_wise_report").show();
                cohortStudentIds = cohort_student_ids;
                total_students = parseInt(150 * (cohortStudentIds.length - 2)) + parseInt(cohortStudentIds[cohortStudentIds.length - 1]).length;
                var progress = document.getElementById('progress-ind');
                progress.style.width = (completed / total_students) * 100 + '%';
                callReportGetter(0);
            },
            error: function(err) {
                // Re-enable button on AJAX error
                $('#search').prop('disabled', false).val('Get Report');
                $("#progress").hide();
                $(".loading-icon").hide();
                alert('Network may be slow. Please try again.');
            }
        })
    }

    function callReportGetter(index) {

        if (index < cohortStudentIds.length) {
            getReport(index);
        } else {
            $("#progress").hide();
            $(".loading-icon").hide();
            $("#exportButtons").show();
            $("#exportButtons2").show();

            // Re-enable button when all processing is complete
            $('#search').prop('disabled', false).val('Get Report');

            generateSummaryTable(); // Generate summary table only at the end
            initializeSearch();
        }
    }



    function getReport(index) {
        var cohortstudentids = cohortStudentIds[index];
        let component_id = $("#component_name").val();
        let fee_type = $('#fee_type').val();
        // let installment_type = $('#installment_type').val();
        // let installmentId = $('#installmentId').val();
        $.ajax({
            url: '<?php echo site_url('feesv2/reports_v2/get_component_wise_getStudentsForSummary'); ?>',
            type: "POST",
            data: {
                "cohortstudentids": cohortstudentids,
                "component_id": component_id,
                "fee_type": fee_type
                // "installment_type": installment_type,
                // "installmentId": installmentId
            },
            success: function(data) {
                var resdata = $.parseJSON(data);
                var componentheader = resdata.component_header;
                var student_data = resdata.student_data;
                var fee_data = resdata.feeArry;
                if (index == 0) {
                    constructFeeHeader(componentheader);
                }

                completed += student_data.length;
                // completed += 2;
                var progress = document.getElementById('progress-ind');
                progress.style.width = (completed / total_students) * 100 + '%';
                construct_fee_component_data(componentheader, student_data, fee_data, index);
                $(".loading-icon").hide();
            }
        })
    }

    function construct_fee_component_data(componentheader, student_data, fee_data, index) {
        let srNo = Number(index) * 150;
        let output = '<tbody>';
        let m = 0;

        for (let i in student_data) {
            let student = student_data[i];
            let studentFeeData = fee_data[student.student_id] || [];

            let studentFeeByComponent = {};
            studentFeeData.forEach(fd => {
                if (fd?.comp_id) studentFeeByComponent[fd.comp_id] = fd;
            });

            // Compute totals
            let total_amount = 0,
                paid_amount = 0,
                concession = 0,
                discount = 0;
            for (let comp_id in componentheader) {
                let componentData = studentFeeByComponent[comp_id];
                if (componentData) {
                    total_amount += +componentData.component_amount || 0;
                    paid_amount += +componentData.component_amount_paid || 0;
                    concession += +componentData.concession_amount || 0;
                    discount += +componentData.student_discount || 0;

                    // Update component totals
                    const compKey = `data_${comp_id}`;
                    componentNames[compKey][`component_amount_${comp_id}`] =
                        (componentNames[compKey][`component_amount_${comp_id}`] || 0) + (+componentData.component_amount || 0);
                    componentNames[compKey][`component_amount_paid_${comp_id}`] =
                        (componentNames[compKey][`component_amount_paid_${comp_id}`] || 0) + (+componentData.component_amount_paid || 0);
                    componentNames[compKey][`concession_amount_${comp_id}`] =
                        (componentNames[compKey][`concession_amount_${comp_id}`] || 0) + (+componentData.concession_amount || 0);
                    componentNames.total.total_component_amount += +componentData.component_amount || 0;
                    componentNames.total.total_component_amount_paid += +componentData.component_amount_paid || 0;
                    componentNames.total.total_concession_amount += +componentData.concession_amount || 0;
                }
            }

            let balance = total_amount - paid_amount - concession;

            // Row rendering
            output += '<tr>';
            output += `<td>${m + 1 + srNo}</td>`;
            output += `<td>${student.first_name}</td>`;
            output += `<td>${student.class}</td>`;
            output += `<td>${student.admission_no}</td>`;
            output += `<td>${student.enrollment_number}</td>`;
            output += `<td>${total_amount}</td>`;
            output += `<td>${paid_amount}</td>`;
            output += `<td>${concession}</td>`;
            output += `<td>${discount}</td>`;
            output += `<td>${balance}</td>`;

            // Component amount only per header
            for (let comp_id in componentheader) {
                let componentData = studentFeeByComponent[comp_id];
                output += `<td>${componentData?.component_amount || 0}</td>`;
                // output += `<td>${componentData?.component_amount_paid || 0}</td>`; 
            }

            output += '</tr>';
            m++;
        }

        output += '</tbody>';
        $('#fee_component_data').append(output);
        index++;
        callReportGetter(index);
    }


    function generateSummaryTable() {
        // Check if componentNames is properly initialized
        if (!componentNames || !componentNames.total) {
            return;
        }

        let table = `<table id="print_summary" class="table table-bordered">
                    <thead>
                        <tr>
                            <th style="background-color: #EFECFD !important; color: #161327 !important; font-weight: 600 !important; text-align: center !important; padding: 16px 20px !important;">Fee Blueprint Name</th>
                            <th style="background-color: #EFECFD !important; color: #161327 !important; font-weight: 600 !important; text-align: center !important; padding: 16px 20px !important;">Component Name</th>
                            <th style="background-color: #EFECFD !important; color: #161327 !important; font-weight: 600 !important; text-align: center !important; padding: 16px 20px !important;">Component Amount</th>
                            <th style="background-color: #EFECFD !important; color: #161327 !important; font-weight: 600 !important; text-align: center !important; padding: 16px 20px !important;">Component Amount Paid</th>
                            <th style="background-color: #EFECFD !important; color: #161327 !important; font-weight: 600 !important; text-align: center !important; padding: 16px 20px !important;">Concession Amount</th>
                            <th style="background-color: #EFECFD !important; color: #161327 !important; font-weight: 600 !important; text-align: center !important; padding: 16px 20px !important;">Total to be Collected</th>
                        </tr>
                    </thead>
                    <tbody>`

        // Generate rows for each component
        for (let val of Object.values(componentNames)) {
            if (val.name && val.comp_id) {
                const blueprint_name = val.blueprint_name || '-';
                const component_amount = +val[`component_amount_${val.comp_id}`] || 0;
                const component_amount_paid = +val[`component_amount_paid_${val.comp_id}`] || 0;
                const concession_amount = +val[`concession_amount_${val.comp_id}`] || 0;
                const balance = component_amount - component_amount_paid - concession_amount;
                table += `
                <tr>
                <td>${blueprint_name}</td>
                <td>${val.name}</td>
                <td>${in_currency(component_amount)}</td>
                <td>${in_currency(component_amount_paid)}</td>
                <td>${in_currency(concession_amount)}</td>
                <td>${in_currency(balance)}</td>
                </tr>
                `
            }
        }

        // Add grand total row
        if (componentNames.total) {
            let {
                total: {
                    total_component_amount = 0,
                    total_component_amount_paid = 0,
                    total_concession_amount = 0
                }
            } = componentNames

            const total_balance = total_component_amount - total_component_amount_paid - total_concession_amount;

            table += `
                    <tr style="font-weight: 600; background-color: #EFECFD !important;">
                    <td colspan="2" style="text-align: right; background-color: #EFECFD !important;">Grand Total</td>
                    <td style="background-color: #EFECFD !important;">${in_currency(total_component_amount)}</td>
                    <td style="background-color: #EFECFD !important;">${in_currency(total_component_amount_paid)}</td>
                    <td style="background-color: #EFECFD !important;">${in_currency(total_concession_amount)}</td>
                    <td style="background-color: #EFECFD !important;">${in_currency(total_balance)}</td>
                    </tr>
                    `
        }

        table += `</tbody></table>`

        $(".summary_data_report").html(table)
    }

    function in_currency(amount) {
        var formatter = new Intl.NumberFormat('en-IN', {
            // style: 'currency',
            currency: 'INR',
        });
        return formatter.format(amount);
    }

    function constructFeeHeader(componentData) {
        $(".summary_data_report").show();
        $('.componenent_wise_report').empty();

        var h_output = '<table id="fee_component_data" class="table table-bordered">';
        h_output += '<thead><tr>';
        h_output += '<th>#</th>';
        h_output += '<th>Student Name</th>';
        h_output += '<th>Class</th>';
        h_output += '<th>Admission No</th>';
        h_output += '<th>Enrollment Number</th>';
        h_output += '<th>Total Amount (All)</th>';
        h_output += '<th>Amount Paid (All)</th>';
        h_output += '<th>Concession (All)</th>';
        h_output += '<th>Discount (All)</th>';
        h_output += '<th>Balance (All)</th>';

        // Initialize component summary mapping
        componentNames = {
            total: {}
        };
        componentNames.total.total_component_amount = 0;
        componentNames.total.total_component_amount_paid = 0;
        componentNames.total.total_concession_amount = 0;
        componentNames.total.total_student_discount = 0;

        for (let k in componentData) {
            if (typeof componentData[k] === 'object') {
                componentNames[`data_${k}`] = {
                    name: componentData[k].name,
                    comp_id: k,
                    blueprint_name: componentData[k].blueprint_name || '-'
                };
                h_output += `<th>${componentData[k].name}</th>`;
                // h_output += `<th>${componentData[k].name} Paid</th>`; 
            }
        }

        h_output += '</tr></thead></table>';
        $('.componenent_wise_report').html(h_output);
    }


    function printProfile() {
        const printWindow = window.open('', '_blank');
        let printHeader = document.getElementById('print_visible').outerHTML;
        printHeader = printHeader.replace('display: none;', ''); // Remove the inline style

        const componentTable = document.getElementById('fee_component_data').outerHTML;


        printWindow.document.write(`
            <html>
            <head>
                <title>Fee Component Report</title>
                <style>
                    body {
                        font-family: 'Poppins', sans-serif;
                        padding: 20px;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 15px 0;
                    }
                    th, td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        font-size: 12px;
                    }
                    h3 { margin: 15px 0; }
                    @media print {
                        table { page-break-inside: auto }
                        tr { page-break-inside: avoid }
                    }
                </style>
            </head>
            <body>
                ${printHeader}
                <h3>Component Details</h3>
                ${componentTable}
                <script>
                window.onload = function() {
                    window.print();
                };
                window.onafterprint = function() {
                    window.close();
                };
            <\/script>
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.print();
    }

    function printAll() {
        const printWindow = window.open('', '_blank');
        let printHeader = document.getElementById('print_visible').outerHTML;
        printHeader = printHeader.replace('display: none;', ''); // Remove the inline style

        const summaryTable = document.getElementById('print_summary').outerHTML;
        const componentTable = document.getElementById('fee_component_data').outerHTML;

        printWindow.document.write(`
            <html>
            <head>
                <title>Fee Component Report - Complete</title>
                <style>
                    body {
                        font-family: 'Poppins', sans-serif;
                        padding: 20px;
                    }
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 15px 0;
                    }
                    th, td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        font-size: 12px;
                    }
                    h3 { margin: 15px 0; }
                    @media print {
                        table { page-break-inside: auto }
                        tr { page-break-inside: avoid }
                    }
                </style>
            </head>
            <body>
                ${printHeader}
                <h3>Fee Summary</h3>
                ${summaryTable}
                <h3>Component Details</h3>
                ${componentTable}
                <script>
                window.onload = function() {
                    window.print();
                };
                window.onafterprint = function() {
                    window.close();
                };
            <\/script>
            </body>
            </html>
        `);

        printWindow.document.close();
        printWindow.print();
    }

    function exportToExcel_daily() {
        var htmls = "";
        var uri = 'data:application/vnd.ms-excel;base64,';
        var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head><body><table>{table}</table></body></html>';
        var base64 = function(s) {
            return window.btoa(unescape(encodeURIComponent(s)))
        };

        var format = function(s, c) {
            return s.replace(/{(\w+)}/g, function(m, p) {
                return c[p];
            })
        };

        const componentTable = document.getElementById('fee_component_data').outerHTML;

        htmls = '<h3>Component Details</h3>' + componentTable;

        var ctx = {
            worksheet: 'Fee Report',
            table: htmls
        }

        var link = document.createElement("a");
        link.download = "fee_report_component_wise.xls";
        link.href = uri + base64(format(template, ctx));
        link.click();
    }

    function exportAll() {
        var htmls = "";
        var uri = 'data:application/vnd.ms-excel;base64,';
        var template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head><body><table>{table}</table></body></html>';
        var base64 = function(s) {
            return window.btoa(unescape(encodeURIComponent(s)))
        };

        var format = function(s, c) {
            return s.replace(/{(\w+)}/g, function(m, p) {
                return c[p];
            })
        };

        const summaryTable = document.getElementById('print_summary').outerHTML;
        const componentTable = document.getElementById('fee_component_data').outerHTML;

        htmls = '<h3>Fee Summary</h3>' + summaryTable + '<h3>Component Details</h3>' + componentTable;

        var ctx = {
            worksheet: 'Fee Report - Complete',
            table: htmls
        }

        var link = document.createElement("a");
        link.download = "fee_report_component_wise_complete.xls";
        link.href = uri + base64(format(template, ctx));
        link.click();
    }

    function initializeSearch() {
        // Reset any previous search
        $('#table-search').val('');

        // Attach the search event handler
        $('#table-search').off('keyup').on('keyup', function() {
            const searchText = $(this).val().toLowerCase();

            // Search in the component-wise table
            $('#fee_component_data tbody tr').each(function() {
                let rowVisible = false;

                // Search in all cells of the row
                $(this).find('td').each(function() {
                    if ($(this).text().toLowerCase().indexOf(searchText) > -1) {
                        rowVisible = true;
                        return false; // Break the loop if found
                    }
                });

                // Show/hide the row based on search result
                $(this).toggle(rowVisible);
            });
        });
    }

    // Document ready function for search functionality
    $(document).ready(function() {
        $('#fee_type').selectpicker(); // initialize selectpicker for Fee Type
        $('#component_name').selectpicker();
        // Initialize search functionality when document is ready
        $(document).on('keyup', '#table-search', function() {
            const searchText = $(this).val().toLowerCase();

            // Search in the component-wise table
            $('#fee_component_data tbody tr').each(function() {
                let rowVisible = false;

                // Search in all cells of the row
                $(this).find('td').each(function() {
                    if ($(this).text().toLowerCase().indexOf(searchText) > -1) {
                        rowVisible = true;
                        return false; // Break the loop if found
                    }
                });

                // Show/hide the row based on search result
                $(this).toggle(rowVisible);
            });
        });
    });
    $(document).on('click', function(event) {
        var $target = $(event.target);
        if (!$target.closest('.bootstrap-select').length && $('.bootstrap-select').hasClass('open')) {
            $('.bootstrap-select').removeClass('open show');
            $('.dropdown-menu').removeClass('show');
        }
    });

    // Dynamic update for class/section dropdown based on class selection
    // $(document).ready(function() {
    //     $("#classId").change(function() {
    //         const previouslySelectedSections = $('#classSectionId').val();
    //         $('#classSectionId').html('');
    //         const selectedValue = $(this).val();
    //         $.ajax({
    //             url: '<?php echo site_url('feesv2/reports_v2/get_class_section_by_fees_selection_class') ?>',
    //             data: {
    //                 'feeclass': selectedValue
    //             },
    //             type: "post",
    //             success: function(data) {
    //                 const resdata = JSON.parse(data);
    //                 let option = '';
    //                 resdata.forEach(item => {
    //                     option += `<option value="${item.section_id}">${item.class_section}</option>`;
    //                 });
    //                 $("#classSectionId").html(option);
    //                 $('#classSectionId').selectpicker('refresh');
    //                 if (previouslySelectedSections) {
    //                     $('#classSectionId').val(previouslySelectedSections).selectpicker('refresh');
    //                 }
    //             },
    //             error: function(err) {
    //                 console.log(err);
    //             }
    //         });
    //     });
    // });
</script>

