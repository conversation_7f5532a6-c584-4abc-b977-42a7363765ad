<?php

/**
 * Reusable Report Script Component
 * 
 * This component handles the main report generation logic
 *
 * Parameters:
 * - $report_type: Type of report (daily_transaction, fee_details, etc.)
 * - $controller_url: Base controller URL for AJAX calls
 * - $report_endpoints: Array of endpoint URLs for different report functions
 */

$report_type = 'daily_transaction';
$controller_url = 'feesv2/reports/';
$report_endpoints = isset($report_endpoints) ? $report_endpoints : [];

// Default endpoints for daily transaction report
if (empty($report_endpoints)) {
  $report_endpoints = [
    'summary' => 'generate_report_for_day_book_summary',
    'summary_with_parents' => 'generate_report_for_day_book_summary1',
    'detailed' => 'generate_report_for_day_bookV1',
    'installment' => 'generate_report_for_installment_wise_day_bookV1',
    'save_filters' => 'save_filters',
    'get_filters' => 'get_predefined_filters',
    'get_filter_by_id' => 'get_predefined_filters_by_id',
    'update_filters' => 'update_filters'
  ];
}
?>

<script type="text/javascript">
  // Global variables for report functionality
  var reportType = '<?php echo $report_type; ?>';
  var controllerUrl = '<?php echo site_url($controller_url); ?>';
  var reportEndpoints = <?php echo json_encode($report_endpoints); ?>;
  var columns_activeArry = [];
  var setColumnDef = [];
  var summaryTable;
  var detailsTable;

  // Main report generation function
  function getReport() {
    // Show loading
    showLoading();


    // Disable search button
    $('#search').prop('disabled', true).html('Please wait...');

    // Get form data
    var formData = getFormData();

    // Determine URL based on report type and format
    var url = getReportUrl(formData);

    // Make AJAX request
    $.ajax({
      url: url,
      type: 'POST',
      data: formData,
      success: function(response) {
        handleReportSuccess(response, formData);
      },
      error: function(xhr, status, error) {
        handleReportError(error);
      },
      complete: function() {
        // Re-enable search button
        $('#search').prop('disabled', false).html('Get Report');
        hideLoading();
      }
    });
  }

  // Get form data based on report type
  function getFormData() {
    var baseData = {
      from_date: $('#from_date').val(),
      to_date: $('#to_date').val(),
      classId: $('#classId').val(),
      fee_type: $('#fee_type').val() || ['All']
    };
    // Get report type from radio buttons
    var reportTypeValue = '2'; // default to summary
    if ($('#type-1').is(':checked')) {
      reportTypeValue = '1';
    } else if ($('#type-3').is(':checked')) {
      reportTypeValue = '3';
    } else if ($('#type-2').is(':checked')) {
      reportTypeValue = '2';
    } else if ($('#type-4').is(':checked')) {
      reportTypeValue = '4';
    }

    // Add report-specific data
    switch (reportType) {
      case 'daily_transaction':
        return $.extend(baseData, {
          paymentModes: $('#paymentModes').val(),
          admission_type: $('#admission_type').val(),
          components: $('#components_include').is(':checked') ? 1 : 0,
          components_v_h: $('#components_v_h').is(':checked') ? 0 : 1,
          components_exclude: $('#components_exclude').is(':checked') ? 1 : 0,
          include_delete: $('#include_delete').is(':checked') ? 1 : 0,
          recon: $('#recon').is(':checked') ? 1 : 0,
          report_type: reportTypeValue
        });

      case 'fee_details':
        return $.extend(baseData, {
          student_status: $('#student_status').val(),
          fee_status: $('#fee_status').val(),
          amount_from: $('#amount_from').val(),
          amount_to: $('#amount_to').val()
        });

      default:
        return baseData;
    }
  }

  // Get report URL based on type and format
  function getReportUrl(formData) {
    var endpoint = '';

    switch (reportType) {
      case 'daily_transaction':
        var reportFormat = formData.report_type || '2';
        switch (reportFormat) {
          case '2':
            endpoint = reportEndpoints.summary;
            break;
          case '3':
            endpoint = reportEndpoints.summary_with_parents;
            break;
          case '4':
            endpoint = reportEndpoints.installment;
            break;
          default:
            endpoint = reportEndpoints.detailed;
        }
        break;

      case 'fee_details':
        endpoint = reportEndpoints.fee_details || 'generate_fee_details_report';
        break;

      default:
        endpoint = reportEndpoints.default || 'generate_report';
    }

    return controllerUrl + endpoint;
  }

  // Handle successful report response
  function handleReportSuccess(data, formData) {

    try {
      var reportFormat = formData.report_type || '2';

      if (reportType === 'daily_transaction' && (reportFormat == '2' || reportFormat == '3')) {
        handleSummaryReport(data, formData);
      } else {
        handleDetailedReport(data);
      }

      // Show print sections
      $('#print_visible').show();
      $('#print_summary').show();
      $('#exportButtons2').show();
    } catch (error) {
      console.error('Error processing report data:', error);
      handleReportError('Error processing report data');
    }
  }

  // Handle summary report
  function handleSummaryReport(data, formData) {
    try {
      var daily_tx = JSON.parse(data);
      if (daily_tx && daily_tx.length > 0) {
        $('#transaction_data').html(construct_summary_table(daily_tx, formData.from_date, formData.to_date));
        initializeTransactionDataTable();
      } else {
        showNoDataMessage();
      }
    } catch (error) {
      console.error('Error parsing summary data:', error);
      showNoDataMessage();
    }
  }

  // Handle detailed report
  function handleDetailedReport(data) {
    if (data && data.trim() !== '') {
      $('.day_book').html(data);
      $('#sliderDiv').show();
      initializeDetailsDataTable();
    } else {
      showNoDataMessage();
    }
  }

  // Handle report error
  function handleReportError(error) {
    console.error('Report generation error:', error);

    var errorHtml = `
        <div class="alert alert-danger text-center">
            <i class="fa fa-exclamation-triangle fa-2x mb-3"></i>
            <h5>Error Generating Report</h5>
            <p class="mb-0">There was an error generating the report. Please try again or contact support if the problem persists.</p>
        </div>
    `;

    $('.day_book_summary').html(errorHtml);
    hideLoading();
  }

  // Show loading indicator
  function showLoading() {
    $('.progress').show();
    $('.no-data-state').hide();
    $('#summary_data').html('');
    $('#transaction_data').html('');
    $('.day_book_summary').html('');
    $('#exportButtons2').hide();
  }

  // Hide loading indicator
  function hideLoading() {
    $('.progress').hide();
  }

  // Show no data message
  function showNoDataMessage() {
    $('#summary_data').html('');
    $('#transaction_data').html('');
    $('.no-data-state').show();
    $('#exportButtons2').hide();
    hideLoading();
  }

  // Initialize Date Range Picker
  function initializeDateRangePicker() {
    if (typeof moment !== 'undefined' && typeof $.fn.daterangepicker !== 'undefined') {
      $("#reportrange").daterangepicker({
        ranges: {
          'Today': [moment(), moment()],
          'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
          'Last 7 Days': [moment().subtract(6, 'days'), moment()],
          'Last 30 Days': [moment().subtract(29, 'days'), moment()],
          'This Month': [moment().startOf('month'), moment().endOf('month')],
          'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        },
        opens: 'right',
        buttonClasses: ['btn btn-default'],
        applyClass: 'btn-small btn-primary',
        cancelClass: 'btn-small',
        format: 'DD.MM.YYYY',
        separator: ' to ',
        startDate: moment(),
        endDate: moment(),
        autoUpdateInput: true
      }, function(start, end) {
        $('#reportrange').val(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
        $('#from_date').val(start.format('DD-MM-YYYY'));
        $('#to_date').val(end.format('DD-MM-YYYY'));
      });

      // Set initial values to current date
      $('#reportrange').val(moment().format('MMM D, YYYY') + ' - ' + moment().format('MMM D, YYYY'));
      $('#from_date').val(moment().format('DD-MM-YYYY'));
      $('#to_date').val(moment().format('DD-MM-YYYY'));
    } else {
      console.warn('Moment.js or DateRangePicker not loaded');
    }
  }

  // Initialize Multi-select dropdowns
  function initializeMultiSelect() {
    if (typeof $.fn.select2 !== 'undefined') {
      $('.select').select2({
        placeholder: "Select options",
        allowClear: true,
        width: '100%'
      });
    } else if (typeof $.fn.selectpicker !== 'undefined') {
      $('.select').selectpicker({
        noneSelectedText: 'Select options',
        selectAllText: 'Select All',
        deselectAllText: 'Deselect All'
      });
    } else {
      console.warn('Select2 or Bootstrap Select not loaded');
    }
  }

  // // Alias for backward compatibility
  // function get_daily_tx_report() {
  //     getReport();
  // }

  // // Get predefined filters function
  function get_predefined_filters() {
    $.ajax({
      url: '<?php echo site_url('feesv2/reports/get_predefined_filters'); ?>',
      type: 'post',
      success: function(data) {
        var res_data = JSON.parse(data);
        if (res_data != '') {
          var html = '';
          html += '<option value="">Select</option>';
          for (var i = 0; i < res_data.length; i++) {
            html += `<option value="${res_data[i].id}">${res_data[i].title}</option>`;
          }
          $('#filter_types').html(html);
        }
      }
    });
  }

  // Payment type function
  function payment_type_new(paymentType, reconStatus, school_short_name, online_tx_mode) {
    var NC = '';
    if (reconStatus == 1) {
      NC = '<span style="color:red;"> <b> (N/C) </b><span>';
    } else if (reconStatus == 2) {
      NC = '<span> <b> (C) </b><span>';
    }
    var txmode = '<span style="color:red;">( <b>' + online_tx_mode + ' )</b><span>';
    if (online_tx_mode == undefined) {
      txmode = '';
    }
    var pValue = '';
    switch (paymentType) {
      case '1':
        pValue = 'DD';
        break;
      case '2':
        pValue = 'Credit Card';
        break;
      case '3':
        pValue = 'Debit Card';
        break;
      case '4':
        pValue = 'Cheque ' + NC;
        break;
      case '5':
        pValue = 'Wallet Payment';
        break;
      case '6':
        pValue = 'Challan ' + NC;;
        break;
      case '7':
        pValue = 'Card (POS)';
        break;
      case '8':
        pValue = 'Net Banking ' + NC;;
        break;
      case '9':
        pValue = 'Cash';
        break;
      case '10':
        pValue = 'Online Payment <br>' + txmode;
        break;
      case '11':
        pValue = 'UPI';
        break;
      case '12':
        pValue = 'Loan';
        break;
      case '13':
        pValue = 'Loan';
        break;
      case '20':
        pValue = 'St.Marys A/C';
        break;
      case '21':
        pValue = 'Mary Madha A/C';
        break;
      case '22':
        if (school_short_name == 'advitya' || school_short_name == 'advityadegree') {
          pValue = 'QR SCAN';
        } else {
          pValue = 'Adjusted';
        }
        break;
      case '999':
        pValue = 'Excess Amount';
        break;
      case '777':
        pValue = 'Online Challan Payment';
        break;
      case '55':
        $pValue = 'Debit card Rupay';
        break;
      case '30':
        pValue = 'Transfer from Indus';
        break;
      case '31':
        pValue = 'Bank Deposit';
        break;
      case '32':
        pValue = 'HDFC  net banking';
        break;
      case '33':
        pValue = 'BOB 145 net banking';
        break;
      case '34':
        pValue = 'BOB 066 net banking';
        break;
      case '35':
        pValue = 'Airpay';
        break;
      case '36':
        pValue = 'Online (Manual)';
        break;
      case '66':
        pValue = 'Grayquest';
        break;
      case '67':
        pValue = 'DRCC';
        break;
      case '68':
        pValue = 'CS';
        break;
      default:
        pValue = '';
        break;
    }
    return pValue;
  }

  // Number to currency function
  function numberToCurrency(amount) {
    var formatter = new Intl.NumberFormat('en-IN', {
      // style: 'currency',
      currency: 'INR',
    });
    return formatter.format(amount);
  }

  // Construct summary table function
  function construct_summary_table(daily_tx, fromDate, toDate) {
    //console.log(daily_tx);
    var loan_column = '<?php echo $this->settings->getSetting('loan_provider_charges') ?>';
    var fee_adjustment_amount = '<?php echo $this->settings->getSetting('fee_adjustment_amount') ?>';
    var fee_fine_amount = '<?php echo $this->settings->getSetting('fee_fine_amount') ?>';
    var fee_ins_column = '<?php echo $this->settings->getSetting('fee_daily_tx_installment_column') ?>';
    var fee_createdBy_column = '<?php echo $this->settings->getSetting('fee_daily_tx_created_by_column') ?>';
    var fee_refund_amount = '<?php echo $this->settings->getSetting('fee_refund_amount_display') ?>';
    var is_semester_scheme = '<?php echo $this->settings->getSetting('is_semester_scheme') ?>';
    var school_short_name = '<?php echo $this->settings->getSetting('school_short_name') ?>';
    var report_type = '2'; // default to summary
    if ($('#type-1').is(':checked')) {
      report_type = '1';
    } else if ($('#type-3').is(':checked')) {
      report_type = '3';
    } else if ($('#type-2').is(':checked')) {
      report_type = '2';
    } else if ($('#type-4').is(':checked')) {
      report_type = '4';
    }

    // var totalNonReconciledAmount = 0;
    var paymentTypeSummary = [];
    var paymentTypeExcess = [];

    for (var i = 0; i < daily_tx.length; i++) {

      if (!(paymentTypeSummary).hasOwnProperty(daily_tx[i].payment_type)) {
        paymentTypeSummary[daily_tx[i].payment_type] = [];
        paymentTypeSummary[daily_tx[i].payment_type]['total_collected_amount'] = 0;
      }
      paymentTypeSummary[daily_tx[i].payment_type]['total_collected_amount'] += parseFloat(daily_tx[i].amount_paid) + parseFloat(daily_tx[i].fine_amount) - parseFloat(daily_tx[i].loan_provider_charges) - parseFloat(daily_tx[i].discount_amount);

      if (daily_tx[i].fee_type == 'Excess Fee') {
        if (!(paymentTypeExcess).hasOwnProperty(daily_tx[i].payment_type)) {
          paymentTypeExcess[daily_tx[i].payment_type] = [];
          paymentTypeExcess[daily_tx[i].payment_type]['total_collected_amount'] = 0;
        }
        paymentTypeExcess[daily_tx[i].payment_type]['total_collected_amount'] += parseFloat(daily_tx[i].amount_paid) + parseFloat(daily_tx[i].fine_amount) - parseFloat(daily_tx[i].loan_provider_charges) - parseFloat(daily_tx[i].discount_amount);
      }

    }

    var html = '';

    html += `
<div class="container-fluid">
  <div class="summary-card mb-4">

    <!-- Shared Header Block (inside the card now) -->
    <div class="summary-header text-center">
      <h3 id="report_title" class="custom-title">Daily Fee Report</h3>
      <h5 class="custom-dates">From <span>${fromDate}</span> To <span>${toDate}</span></h5>
    </div>

    <!-- Summary tables inside the same card -->
    <div class="d-flex justify-content-center flex-wrap summary-wrapper" style="gap: 0;">

`;


    // Payment Summary Table
    var totalCollectedAmount = 0;
    html += `
  <div class="summary-table">
    <table class="table table-bordered">
      <thead>
        <tr>
          <th>Payment Method</th>
          <th>Amount</th>
        </tr>
      </thead>
      <tbody>
`;

    for (var paymentType in paymentTypeSummary) {
      if (paymentType != 999) {
        totalCollectedAmount += parseFloat(paymentTypeSummary[paymentType].total_collected_amount);
        html += `
      <tr>
        <th>${payment_type_new(paymentType, 0, school_short_name)}</th>
        <td>${numberToCurrency(paymentTypeSummary[paymentType].total_collected_amount)}</td>
      </tr>
    `;
      }
    }

    html += `
      </tbody>
      <tfoot>
        <tr>
          <th>Total</th>
          <th>${numberToCurrency(parseFloat(totalCollectedAmount))}</th>
        </tr>
`;

    // Include 999 (Debited) if present
    for (var paymentType in paymentTypeSummary) {
      if (paymentType == 999) {
        html += `
      <tr>
        <th>${payment_type_new(paymentType, 0, school_short_name)} (Debited)</th>
        <th>(${numberToCurrency(paymentTypeSummary[paymentType].total_collected_amount)})</th>
      </tr>
    `;
      }
    }

    html += `
      </tfoot>
    </table>
  </div>
`;

    // Excess Payment Summary (if available)
    var totalCollectedExcessAmount = 0;
    if (paymentTypeExcess.length > 0) {
      html += `
    <div class="summary-table">
      <table class="table table-bordered">
        <thead>
          <tr>
            <th>Excess Payment Method</th>
            <th>Collected Total</th>
          </tr>
        </thead>
        <tbody>
  `;
      for (var paymentType in paymentTypeExcess) {
        totalCollectedExcessAmount += parseFloat(paymentTypeExcess[paymentType].total_collected_amount);
        html += `
      <tr>
        <th>${payment_type_new(paymentType, 0, school_short_name, '')}</th>
        <td>${numberToCurrency(paymentTypeExcess[paymentType].total_collected_amount)}</td>
      </tr>
    `;
      }

      html += `
        </tbody>
        <tfoot>
          <tr>
            <th>Excess Amount Total</th>
            <th>${numberToCurrency(parseFloat(totalCollectedExcessAmount))}</th>
          </tr>
        </tfoot>
      </table>
    </div>
  `;
    }

    html += `</div></div>`; // Close summary-wrapper and container-fluid

    html += '<input type="hidden" id="totalCollectedAmount" value=' + numberToCurrency(totalCollectedAmount) + ' >';

    html += '</div>';

    html += '<div style="justify-content: space-between!important;display: flex!important;" class="d-flex justify-content-between align-items-center"><p><b style="color: red;"> </b><b style="color: red;"> </b></p><div class="d-flex align-items-center" style="display: flex!important;"><div class="form-group" id="range-input" style="width: 300px;"></div></div></div>';
    html += '<div class="row table-responsive" id="fee-container-scroll" style="overflow-x: auto;">';

    html += '<table class="table table-bordered " id="daily_dataTable" style="width:100%; white-space: nowrap;">';
    html += '<thead>';
    html += '<tr>';
    html += '<th>#</th>';
    html += '<th>Receipt Date</th>';
    html += '<th>Receipt Number</th>';
    // html +='<th class="printShow" style="display:none;" >Receipt Number</th>';
    if (report_type == 2) {
      html += '<th>Admission Number</th>';
      html += '<th>Enrollment No</th>';
    }
    html += '<th>Student Name</th>';
    html += '<th>Class / Section</th>';
    if (is_semester_scheme == 1) {
      html += '<th>Semester</th>';
    }
    if (report_type == 3) {
      html += '<th>Admission No</th>';
      html += '<th>Enrollment No</th>';
      html += '<th>Father</th>';
      html += '<th>Father Address</th>';
      html += '<th>Father PAN No.</th>';
      html += '<th>Father Aadhar No.</th>';
      html += '<th>Mother</th>';
      html += '<th>Mother PAN No.</th>';
      html += '<th>Mother Aadhar No.</th>';
    }
    html += '<th>Fee Type</th>';
    html += '<th>Payment Method</th>';
    html += '<th>Bank Name</th>';
    html += '<th>DD/Cheque Date</th>';
    html += '<th>Reference No.</th>';
    html += '<th>Remarks</th>';

    if ($('#recon').is(':checked')) {
      html += '<th>Recon date</th>';
    }

    if (fee_ins_column) {
      html += '<th>Installments</th>';
    }
    html += '<th>Total Amount</th>';
    html += '<th>Concession</th>';
    if (fee_adjustment_amount) {
      html += '<th>Adjustment</th>';
    }
    if (loan_column) {
      html += '<th>Loan Provider Charges</th>';
    }
    if (fee_fine_amount) {
      html += '<th>Fine</th>';
    }
    html += '<th>Discount</th>';
    html += '<th>Collected Amount</th>';
    // if (fee_refund_amount) {
    //   html +='<th>Refund Amount</th>';
    // }
    if (fee_createdBy_column) {
      html += '<th>Collected By</th>';
    }
    html += '</tr>';
    html += '</thead>';
    html += '<tbody>';
    var totalAmount = 0;
    var totalCon = 0;
    var totalAdjust = 0;
    var totalLoan = 0;
    var totalFine = 0;
    var totalDiscount = 0;
    var totalColleted = 0;
    for (var i = 0; i < daily_tx.length; i++) {
      var urlStdhistory = '';
      if (daily_tx[i].student_id != undefined) {
        var feev1settings = '<?php echo $this->settings->getSetting('fee_collection_v1') ?>';
        if (daily_tx[i].fee_type == 'Excess Fee') {
          urlStdhistory = '';
        } else {
          if (feev1settings == 1) {
            urlStdhistory = '<?php echo site_url('feesv2/fees_collection/fee_reciept_viewv1/') ?>' + daily_tx[i].transId + '/0';
          } else {
            urlStdhistory = '<?php echo site_url('feesv2/fees_collection/fee_reciept_view/') ?>' + daily_tx[i].transId + '/0';
          }
        }

      }

      if (daily_tx[i].payment_type == 999) {
        totalAmount += 0;
        totalColleted += 0;
      } else {
        totalAmount += parseFloat(daily_tx[i].amount_paid) + parseFloat(daily_tx[i].concession_amount) + parseFloat(daily_tx[i].adjustment_amount);
        totalColleted += parseFloat(daily_tx[i].amount_paid) + parseFloat(daily_tx[i].fine_amount) - parseFloat(daily_tx[i].loan_provider_charges) - parseFloat(daily_tx[i].discount_amount);
      }
      totalCon += parseFloat(daily_tx[i].concession_amount);
      totalAdjust += parseFloat(daily_tx[i].adjustment_amount);
      totalLoan += parseFloat(daily_tx[i].loan_provider_charges);
      totalFine += parseFloat(daily_tx[i].fine_amount);
      totalDiscount += parseFloat(daily_tx[i].discount_amount);

      html += '<tr>';
      html += '<td>' + (i + 1) + '</td>';
      html += '<td>' + daily_tx[i].receipt_date + '</td>';

      // html +='<td><a class="printHide" style="border: none;" target="_blank" href='+urlStdhistory+'>'+daily_tx[i].receipt_number+'</a><span class="printShow" style="display:none;"  >'+daily_tx[i].receipt_number+'</span></td>';

      html += '<td class="printHide" ><a class="printHide" style="border: none;" target="_blank" href=' + urlStdhistory + '>' + daily_tx[i].receipt_number + '</a></td>';

      // html +='<td class="printShow" style="display:none;" >'+daily_tx[i].receipt_number+'</td>';
      if (report_type == 2) {
        if (daily_tx[i].admission_no == undefined) {
          html += '<td>-</td>';
        } else {
          html += '<td>' + daily_tx[i].admission_no + '</td>';
        }
        html += '<td>' + daily_tx[i].enrollment_number + '</td>';
      }
      html += '<td>' + daily_tx[i].student_name + '</td>';
      if (daily_tx[i].class_name == null) {
        html += '<td></td>';
      } else {
        html += '<td>' + daily_tx[i].class_name + '' + daily_tx[i].sectionName + '</td>';
      }

      if (is_semester_scheme == 1) {
        if (daily_tx[i].semester == null) {
          html += '<td></td>';
        } else {
          html += '<td>' + daily_tx[i].semester + '</td>';
        }
      }
      if (report_type == 3) {
        html += '<td>' + daily_tx[i].admission_no + '</td>';
        html += '<td>' + daily_tx[i].enrollment_number + '</td>';
        html += '<td>' + daily_tx[i].fName + '</td>';
        html += '<td>' + daily_tx[i].address + '</td>';
        html += '<td>' + daily_tx[i].f_pan_number + '</td>';
        html += '<td>' + daily_tx[i].f_aadhar_no + '</td>';
        html += '<td>' + daily_tx[i].mName + '</td>';
        html += '<td>' + daily_tx[i].m_pan_number + '</td>';
        html += '<td>' + daily_tx[i].m_aadhar_no + '</td>';
      }
      html += '<td>' + daily_tx[i].fee_type + '</td>';
      html += '<td>' + payment_type_new(daily_tx[i].payment_type, daily_tx[i].reconciliation_status, school_short_name, daily_tx[i].online_tx_payment_mode) + '</td>';
      if (daily_tx[i].bank_name == null) {
        daily_tx[i].bank_name = '';
      }
      html += '<td>' + daily_tx[i].bank_name + '</td>';
      if (daily_tx[i].payment_type == 1 || daily_tx[i].payment_type == 4 || daily_tx[i].payment_type == 8 || daily_tx[i].payment_type == 67) {
        html += '<td>' + daily_tx[i].cheque_or_dd_date + '</td>';
      } else {
        html += '<td></td>';
      }
      var remarks = '';
      var cheque_dd_nb_cc_dd_number = '';
      if (daily_tx[i].remarks != null) {
        remarks = daily_tx[i].remarks
      }
      if (daily_tx[i].cheque_dd_nb_cc_dd_number != null) {
        cheque_dd_nb_cc_dd_number = daily_tx[i].cheque_dd_nb_cc_dd_number
      }
      html += '<td>' + cheque_dd_nb_cc_dd_number + '</td>';

      html += '<td>' + remarks + '</td>';
      var reconDate = daily_tx[i].recon_submitted_on;
      if (daily_tx[i].recon_submitted_on == null || daily_tx[i].recon_submitted_on == '00-00-0000' || daily_tx[i].recon_submitted_on == undefined) {
        reconDate = '';
      }
      if ($('#recon').is(':checked')) {
        html += '<td>' + reconDate + '</td>';
      }

      if (fee_ins_column) {
        if (daily_tx[i].ins_name == undefined) {
          html += '<td>-</td>';
        } else {
          html += '<td>' + daily_tx[i].ins_name + '</td>';
        }
      }
      if (daily_tx[i].payment_type == 999) {
        html += '<td>(' + numberToCurrency(parseFloat(daily_tx[i].amount_paid) + parseFloat(daily_tx[i].concession_amount) + parseFloat(daily_tx[i].adjustment_amount)) + ')</td>';
      } else {
        html += '<td>' + numberToCurrency(parseFloat(daily_tx[i].amount_paid) + parseFloat(daily_tx[i].concession_amount) + parseFloat(daily_tx[i].adjustment_amount)) + '</td>';
      }


      html += '<td>(' + numberToCurrency(daily_tx[i].concession_amount) + ')</td>';
      if (fee_adjustment_amount) {
        html += '<td>(' + numberToCurrency(daily_tx[i].adjustment_amount) + ')</td>';
      }
      if (loan_column) {
        html += '<td>(' + numberToCurrency(daily_tx[i].loan_provider_charges) + ')</td>';
      }
      if (fee_fine_amount) {
        html += '<td>' + numberToCurrency(daily_tx[i].fine_amount) + '</td>';
      }
      html += '<td>(' + numberToCurrency(daily_tx[i].discount_amount) + ')</td>';

      if (daily_tx[i].payment_type == 999) {
        html += '<td>(' + numberToCurrency(parseFloat(daily_tx[i].amount_paid) + parseFloat(daily_tx[i].fine_amount) - parseFloat(daily_tx[i].loan_provider_charges) - parseFloat(daily_tx[i].discount_amount)) + ')</td>';
      } else {
        html += '<td>' + numberToCurrency(parseFloat(daily_tx[i].amount_paid) + parseFloat(daily_tx[i].fine_amount) - parseFloat(daily_tx[i].loan_provider_charges) - parseFloat(daily_tx[i].discount_amount)) + '</td>';
      }


      if (fee_createdBy_column) {
        if (daily_tx[i].collected_name == undefined) {
          html += '<td>-</td>';
        } else {
          html += '<td>' + daily_tx[i].collected_name + '</td>';
        }
      }
      // if (fee_refund_amount) {
      //   html +='<th>'+daily_tx[i].refund_amount+'</th>';
      // }
      html += '</tr>';
    }
    html += '</tbody>';
    html += '<tfoot>';
    html += '<tr>';
    var colspan = '13';

    if ($('#recon').is(':checked')) {
      colspan = '14';
    }

    if (fee_ins_column) {
      colspan = '14';
    }
    if (is_semester_scheme == 1) {
      colspan = '14';
    }
    if (fee_ins_column && is_semester_scheme) {
      colspan = '15';
    }
    if (report_type == 3) {
      colspan = parseInt(colspan) + 8 - 1;
    }

    html += '<th colspan="' + colspan + '">Grand Total</th>';
    html += '<th>' + numberToCurrency(totalAmount) + '</th>';
    html += '<th>(' + numberToCurrency(totalCon) + ')</th>';
    if (fee_adjustment_amount) {
      html += '<th>(' + numberToCurrency(totalAdjust) + ')</th>';
    }
    if (loan_column) {
      html += '<th>(' + numberToCurrency(totalLoan) + ')</th>';
    }
    if (fee_fine_amount) {
      html += '<th>' + numberToCurrency(totalFine) + '</th>';
    }
    html += '<th>(' + numberToCurrency(totalDiscount) + ')</th>';
    html += '<th>' + numberToCurrency(totalColleted) + '</th>';
    if (fee_createdBy_column) {
      html += '<th></th>';
    }
    html += '</tr>';
    html += '</tfoot>';
    html += '</table>';
    html += '</div>';
    return html;
  }

  // Document ready initialization
  $(document).ready(function() {
    // Initialize components
    initializeDateRangePicker();
    initializeMultiSelect();

    // Load predefined filters
    get_predefined_filters();

    // Initialize date picker for existing elements
    if (typeof $.fn.datetimepicker !== 'undefined' && typeof moment !== 'undefined') {
      $('.date').datetimepicker({
        viewMode: 'days',
        format: 'DD-MM-YYYY'
      });
    }

    // Bind search button click event
    $('#search').on('click', function() {
      $('#search').prop('disabled', true).val('Please wait...');
      getReport();
    });

    // Bind report type change event
    $('input[name="report_type"]').change(function() {
      getReport();
    });

    // Ensure class filter uses Select2
    if ($.fn.select2) {
      $('#classId').select2({
        placeholder: "All Classes"
      });
    }
  });
  //Dropdown Selection
  $(document).on('click', function(event) {
    var $target = $(event.target);
    if (!$target.closest('.bootstrap-select').length && $('.bootstrap-select').hasClass('open')) {
      $('.bootstrap-select').removeClass('open show');
      $('.dropdown-menu').removeClass('show');
    }
  });
  // Pagination
  function customizePagination() {
    var paginateDiv = $('.dataTables_paginate');
    if (paginateDiv.length && paginateDiv.find('.pagination').length) {
      var pagination = paginateDiv.find('.pagination');

      // Get existing buttons
      var firstBtn = pagination.find('.paginate_button.first');
      var prevBtn = pagination.find('.paginate_button.previous');
      var nextBtn = pagination.find('.paginate_button.next');
      var lastBtn = pagination.find('.paginate_button.last');

      // Add data-control attributes and nav-arrow class
      if (firstBtn.length) {
        firstBtn.attr('data-control', 'first').find('a').addClass('nav-arrow');
      }
      if (prevBtn.length) {
        prevBtn.attr('data-control', 'prev').find('a').addClass('nav-arrow');
        // Add Previous label after the previous arrow
        if (prevBtn.next().length === 0 || !prevBtn.next().hasClass('prev-label')) {
          prevBtn.after('<li class="page-item" data-control="prev-label"><a class="page-link nav-arrow" href="#" data-page="prev-label">Previous</a></li>');
        }
      }
      if (nextBtn.length) {
        nextBtn.attr('data-control', 'next').find('a').addClass('nav-arrow');
        // Add Next label before the next arrow
        if (nextBtn.prev().length === 0 || !nextBtn.prev().hasClass('next-label')) {
          nextBtn.before('<li class="page-item" data-control="next-label"><a class="page-link nav-arrow" href="#" data-page="next-label">Next</a></li>');
        }
      }
      if (lastBtn.length) {
        lastBtn.attr('data-control', 'last').find('a').addClass('nav-arrow');
      }

      // Handle click events for new buttons
      pagination.off('click.custom').on('click.custom', '.page-link[data-page="prev-label"], .page-link[data-page="next-label"]', function(e) {
        e.preventDefault();
        var action = $(this).attr('data-page');
        if (action === 'prev-label') {
          prevBtn.find('a').click();
        } else if (action === 'next-label') {
          nextBtn.find('a').click();
        }
      });
    }
  }
</script>

<!-- Required JavaScript Libraries -->
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>