-- Add pen_number field to student_admission table
-- This field is used in attendance reports to display PEN numbers

ALTER TABLE `student_admission` 
ADD COLUMN `pen_number` VARCHAR(150) NULL DEFAULT NULL AFTER `sts_number`;

-- Update student_admission_audit trigger to include pen_number field
-- Note: This assumes the audit trigger exists and needs to be updated
-- You may need to run this separately if the trigger exists:

-- DROP TRIGGER IF EXISTS student_admission_audit;
-- DELIMITER $$
-- CREATE TRIGGER student_admission_audit
-- AFTER UPDATE ON student_admission FOR EACH ROW
-- BEGIN
--     SET @oldJson = NULL;
--     SET @newJson = NULL;
--     -- Add all existing fields here...
--     CALL merge_object(@oldJson, @newJson, OLD.pen_number, NEW.pen_number, 'pen_number');
--     -- Continue with other fields...
-- END$$
-- DELIMITER ;
